<template>
  <div class="users-view">
    <!-- 搜索和筛选 -->
    <div class="search-section">
      <div class="search-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户名、ID..."
          style="width: 300px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select v-model="userTypeFilter" placeholder="用户类型" style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="儿童" value="child" />
          <el-option label="志愿者" value="volunteer" />
        </el-select>
        
        <el-select v-model="statusFilter" placeholder="状态" style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="在线" value="online" />
          <el-option label="离线" value="offline" />
          <el-option label="学习中" value="studying" />
        </el-select>
      </div>
      
      <div class="search-right">
        <el-button type="primary" @click="exportUsers">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#409EFF"><User /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ userStats.totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#67C23A"><UserFilled /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ userStats.activeUsers }}</div>
          <div class="stat-label">活跃用户</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#E6A23C"><Avatar /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ userStats.newUsers }}</div>
          <div class="stat-label">新增用户(今日)</div>
        </div>
      </div>
    </div>

    <!-- 用户表格 -->
    <div class="table-section">
      <el-table 
        :data="filteredUsers" 
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="用户信息" width="200">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :size="40" :src="scope.row.avatar" />
              <div class="user-details">
                <div class="user-name">{{ scope.row.name }}</div>
                <div class="user-type">
                  <el-tag 
                    :type="scope.row.type === 'child' ? 'primary' : 'success'" 
                    size="small"
                  >
                    {{ scope.row.type === 'child' ? '儿童' : '志愿者' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="age" label="年龄" width="80" />
        
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.status)" 
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="学习数据" width="150">
          <template #default="scope">
            <div v-if="scope.row.type === 'child'" class="study-data">
              <div>等级: Level {{ scope.row.level }}</div>
              <div>积分: {{ scope.row.points }}</div>
              <div>学习时长: {{ scope.row.studyTime }}h</div>
            </div>
            <div v-else class="volunteer-data">
              <div>服务时长: {{ scope.row.serviceHours }}h</div>
              <div>评分: {{ scope.row.rating }}/5</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="lastActive" label="最后活跃" width="150">
          <template #default="scope">
            {{ formatTime(scope.row.lastActive) }}
          </template>
        </el-table-column>
        
        <el-table-column label="心理状态" width="100">
          <template #default="scope">
            <el-tag 
              v-if="scope.row.type === 'child'"
              :type="getMoodType(scope.row.mood)" 
              size="small"
            >
              {{ getMoodText(scope.row.mood) }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewUserDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              @click="editUser(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteUser(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalUsers"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedUsers.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedUsers.length }} 个用户
      </div>
      <div class="batch-buttons">
        <el-button type="warning" @click="batchEdit">批量编辑</el-button>
        <el-button type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search, Download, User, UserFilled, Avatar } from '@element-plus/icons-vue'
import { childrenData, volunteersData } from '@/mock/data'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const searchQuery = ref('')
const userTypeFilter = ref('')
const statusFilter = ref('')
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const selectedUsers = ref([])

// 用户数据
const allUsers = ref([])
const userStats = ref({
  totalUsers: 0,
  activeUsers: 0,
  newUsers: 0
})

// 过滤后的用户数据
const filteredUsers = computed(() => {
  let filtered = allUsers.value
  
  // 搜索过滤
  if (searchQuery.value) {
    filtered = filtered.filter(user => 
      user.name.includes(searchQuery.value) || 
      user.id.toString().includes(searchQuery.value)
    )
  }
  
  // 用户类型过滤
  if (userTypeFilter.value) {
    filtered = filtered.filter(user => user.type === userTypeFilter.value)
  }
  
  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(user => user.status === statusFilter.value)
  }
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

const totalUsers = computed(() => {
  let filtered = allUsers.value
  
  if (searchQuery.value) {
    filtered = filtered.filter(user => 
      user.name.includes(searchQuery.value) || 
      user.id.toString().includes(searchQuery.value)
    )
  }
  
  if (userTypeFilter.value) {
    filtered = filtered.filter(user => user.type === userTypeFilter.value)
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(user => user.status === statusFilter.value)
  }
  
  return filtered.length
})

// 初始化数据
const initData = () => {
  // 合并儿童和志愿者数据
  const children = childrenData.list.map(child => ({
    ...child,
    type: 'child'
  }))
  
  const volunteers = volunteersData.list.map(volunteer => ({
    ...volunteer,
    type: 'volunteer',
    mood: null,
    level: null,
    points: null,
    studyTime: null
  }))
  
  allUsers.value = [...children, ...volunteers]
  
  // 计算统计数据
  userStats.value = {
    totalUsers: allUsers.value.length,
    activeUsers: allUsers.value.filter(u => u.status === 'online').length,
    newUsers: Math.floor(Math.random() * 20) + 5
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap = {
    online: 'success',
    offline: 'info',
    studying: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    online: '在线',
    offline: '离线',
    studying: '学习中'
  }
  return textMap[status] || '未知'
}

// 获取心情类型
const getMoodType = (mood: string) => {
  const typeMap = {
    happy: 'success',
    calm: 'primary',
    excited: 'warning',
    sad: 'danger',
    confused: 'info'
  }
  return typeMap[mood] || 'info'
}

// 获取心情文本
const getMoodText = (mood: string) => {
  const textMap = {
    happy: '开心',
    calm: '平静',
    excited: '兴奋',
    sad: '难过',
    confused: '困惑'
  }
  return textMap[mood] || '未知'
}

// 格式化时间
const formatTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 查看用户详情
const viewUserDetail = (user: any) => {
  ElMessage.info(`查看用户 ${user.name} 的详细信息`)
}

// 编辑用户
const editUser = (user: any) => {
  ElMessage.info(`编辑用户 ${user.name}`)
}

// 删除用户
const deleteUser = (user: any) => {
  ElMessageBox.confirm(
    `确定要删除用户 ${user.name} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 导出用户数据
const exportUsers = () => {
  ElMessage.success('用户数据导出成功')
}

// 批量编辑
const batchEdit = () => {
  ElMessage.info(`批量编辑 ${selectedUsers.value.length} 个用户`)
}

// 批量删除
const batchDelete = () => {
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`,
    '确认批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('批量删除成功')
    selectedUsers.value = []
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

onMounted(() => {
  initData()
})
</script>

<style scoped>
.users-view {
  padding: 0;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-left {
  display: flex;
  gap: 15px;
  align-items: center;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.table-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.study-data, .volunteer-data {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.pagination {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px 25px;
  background: white;
  border-radius: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.batch-info {
  font-size: 14px;
  color: #666;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-left {
    flex-wrap: wrap;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .batch-actions {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
