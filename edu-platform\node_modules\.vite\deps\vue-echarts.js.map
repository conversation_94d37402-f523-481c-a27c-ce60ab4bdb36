{"version": 3, "sources": ["../../vue-demi/lib/index.mjs", "../../vue-echarts/src/composables/api.ts", "../../vue-echarts/src/composables/autoresize.ts", "../../vue-echarts/src/utils.ts", "../../vue-echarts/src/composables/loading.ts", "../../vue-echarts/src/wc.ts", "../../vue-echarts/src/ECharts.ts"], "sourcesContent": ["import * as Vue from 'vue'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nfunction install() {}\n\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key)\n    target.splice(key, 1, val)\n    return val\n  }\n  target[key] = val\n  return val\n}\n\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1)\n    return\n  }\n  delete target[key]\n}\n\nexport * from 'vue'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { Ref } from \"vue-demi\";\nimport type { EChartsType } from \"../types\";\n\nconst METHOD_NAMES = [\n  \"getWidth\",\n  \"getHeight\",\n  \"getDom\",\n  \"getOption\",\n  \"resize\",\n  \"dispatchAction\",\n  \"convertToPixel\",\n  \"convertFromPixel\",\n  \"containPixel\",\n  \"getDataURL\",\n  \"getConnectedDataURL\",\n  \"appendData\",\n  \"clear\",\n  \"isDisposed\",\n  \"dispose\"\n] as const;\n\ntype MethodName = (typeof METHOD_NAMES)[number];\n\ntype PublicMethods = Pick<EChartsType, MethodName>;\n\nexport function usePublicAPI(\n  chart: Ref<EChartsType | undefined>\n): PublicMethods {\n  function makePublicMethod<T extends MethodName>(\n    name: T\n  ): (...args: Parameters<EChartsType[T]>) => ReturnType<EChartsType[T]> {\n    return (...args) => {\n      if (!chart.value) {\n        throw new Error(\"ECharts is not initialized yet.\");\n      }\n      return (chart.value[name] as any).apply(chart.value, args);\n    };\n  }\n\n  function makePublicMethods(): PublicMethods {\n    const methods = Object.create(null);\n    METHOD_NAMES.forEach(name => {\n      methods[name] = makePublicMethod(name);\n    });\n\n    return methods as PublicMethods;\n  }\n\n  return makePublicMethods();\n}\n", "import { watch } from \"vue-demi\";\nimport { throttle } from \"echarts/core\";\n\nimport type { Ref, PropType } from \"vue-demi\";\nimport type { EChartsType, AutoResize } from \"../types\";\n\nexport function useAutoresize(\n  chart: Ref<EChartsType | undefined>,\n  autoresize: Ref<AutoResize | undefined>,\n  root: Ref<HTMLElement | undefined>\n): void {\n  watch(\n    [root, chart, autoresize],\n    ([root, chart, autoresize], _, onCleanup) => {\n      let ro: ResizeObserver | null = null;\n\n      if (root && chart && autoresize) {\n        const { offsetWidth, offsetHeight } = root;\n        const autoresizeOptions = autoresize === true ? {} : autoresize;\n        const { throttle: wait = 100, onResize } = autoresizeOptions;\n\n        let initialResizeTriggered = false;\n\n        const callback = () => {\n          chart.resize();\n          onResize?.();\n        };\n\n        const resizeCallback = wait ? throttle(callback, wait) : callback;\n\n        ro = new ResizeObserver(() => {\n          // We just skip ResizeObserver's initial resize callback if the\n          // size has not changed since the chart is rendered.\n          if (!initialResizeTriggered) {\n            initialResizeTriggered = true;\n            if (\n              root.offsetWidth === offsetWidth &&\n              root.offsetHeight === offsetHeight\n            ) {\n              return;\n            }\n          }\n          resizeCallback();\n        });\n        ro.observe(root);\n      }\n\n      onCleanup(() => {\n        if (ro) {\n          ro.disconnect();\n          ro = null;\n        }\n      });\n    }\n  );\n}\n\nexport const autoresizeProps = {\n  autoresize: [Boolean, Object] as PropType<AutoResize>\n};\n", "import { unref, isRef } from \"vue-demi\";\n\nimport type { Injection } from \"./types\";\n\ntype Attrs = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key: string]: any;\n};\n\n// Copied from\n// https://github.com/vuejs/vue-next/blob/5a7a1b8293822219283d6e267496bec02234b0bc/packages/shared/src/index.ts#L40-L41\nconst onRE = /^on[^a-z]/;\nexport const isOn = (key: string): boolean => onRE.test(key);\n\nexport function omitOn(attrs: Attrs): Attrs {\n  const result: Attrs = {};\n  for (const key in attrs) {\n    if (!isOn(key)) {\n      result[key] = attrs[key];\n    }\n  }\n\n  return result;\n}\n\nexport function unwrapInjected<T, V>(\n  injection: Injection<T>,\n  defaultValue: V\n): T | V {\n  const value = isRef(injection) ? unref(injection) : injection;\n\n  if (value && typeof value === \"object\" && \"value\" in value) {\n    return value.value || defaultValue;\n  }\n\n  return value || defaultValue;\n}\n", "import { unwrapInjected } from \"../utils\";\nimport { inject, computed, watchEffect } from \"vue-demi\";\n\nimport type { Ref, InjectionKey, PropType } from \"vue-demi\";\nimport type { EChartsType, LoadingOptions } from \"../types\";\n\nexport const LOADING_OPTIONS_KEY =\n  \"ecLoadingOptions\" as unknown as InjectionKey<\n    LoadingOptions | Ref<LoadingOptions>\n  >;\n\nexport function useLoading(\n  chart: Ref<EChartsType | undefined>,\n  loading: Ref<boolean>,\n  loadingOptions: Ref<LoadingOptions | undefined>\n): void {\n  const defaultLoadingOptions = inject(LOADING_OPTIONS_KEY, {});\n  const realLoadingOptions = computed(() => ({\n    ...unwrapInjected(defaultLoadingOptions, {}),\n    ...loadingOptions?.value\n  }));\n\n  watchEffect(() => {\n    const instance = chart.value;\n    if (!instance) {\n      return;\n    }\n\n    if (loading.value) {\n      instance.showLoading(realLoadingOptions.value);\n    } else {\n      instance.hideLoading();\n    }\n  });\n}\n\nexport const loadingProps = {\n  loading: Boolean,\n  loadingOptions: Object as PropType<LoadingOptions>\n};\n", "let registered: boolean | null = null;\n\nexport const TAG_NAME = \"x-vue-echarts\";\n\nexport interface EChartsElement extends HTMLElement {\n  __dispose: (() => void) | null;\n}\n\nexport function register(): boolean {\n  if (registered != null) {\n    return registered;\n  }\n\n  if (\n    typeof HTMLElement === \"undefined\" ||\n    typeof customElements === \"undefined\"\n  ) {\n    return (registered = false);\n  }\n\n  try {\n    // Class definitions cannot be transpiled to ES5\n    // so we are doing a little trick here to ensure\n    // we are using native classes. As we use this as\n    // a progressive enhancement, it will be fine even\n    // if the browser doesn't support native classes.\n    const reg = new Function(\n      \"tag\",\n      // Use esbuild repl to keep build process simple\n      // https://esbuild.github.io/try/#dAAwLjIzLjAALS1taW5pZnkAY2xhc3MgRUNoYXJ0c0VsZW1lbnQgZXh0ZW5kcyBIVE1MRWxlbWVudCB7CiAgX19kaXNwb3NlID0gbnVsbDsKCiAgZGlzY29ubmVjdGVkQ2FsbGJhY2soKSB7CiAgICBpZiAodGhpcy5fX2Rpc3Bvc2UpIHsKICAgICAgdGhpcy5fX2Rpc3Bvc2UoKTsKICAgICAgdGhpcy5fX2Rpc3Bvc2UgPSBudWxsOwogICAgfQogIH0KfQoKaWYgKGN1c3RvbUVsZW1lbnRzLmdldCh0YWcpID09IG51bGwpIHsKICBjdXN0b21FbGVtZW50cy5kZWZpbmUodGFnLCBFQ2hhcnRzRWxlbWVudCk7Cn0K\n      \"class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);\"\n    );\n    reg(TAG_NAME);\n  } catch (e) {\n    return (registered = false);\n  }\n\n  return (registered = true);\n}\n", "/* eslint-disable vue/multi-word-component-names */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport {\n  defineComponent,\n  shallowRef,\n  toRefs,\n  watch,\n  computed,\n  inject,\n  onMounted,\n  onBeforeUnmount,\n  h,\n  nextTick,\n  watchEffect,\n  getCurrentInstance,\n  Vue2\n} from \"vue-demi\";\nimport { init as initChart } from \"echarts/core\";\n\nimport {\n  usePublicAPI,\n  useAutoresize,\n  autoresizeProps,\n  useLoading,\n  loadingProps\n} from \"./composables\";\nimport { isOn, omitOn, unwrapInjected } from \"./utils\";\nimport { register, TAG_NAME } from \"./wc\";\n\nimport type { PropType, InjectionKey } from \"vue-demi\";\nimport type {\n  EChartsType,\n  EventTarget,\n  Option,\n  Theme,\n  ThemeInjection,\n  InitOptions,\n  InitOptionsInjection,\n  UpdateOptions,\n  UpdateOptionsInjection,\n  Emits\n} from \"./types\";\nimport type { EChartsElement } from \"./wc\";\n\nimport \"./style.css\";\n\nconst __CSP__ = false;\nconst wcRegistered = __CSP__ ? false : register();\n\nif (Vue2) {\n  Vue2.config.ignoredElements.push(TAG_NAME);\n}\n\nexport const THEME_KEY = \"ecTheme\" as unknown as InjectionKey<ThemeInjection>;\nexport const INIT_OPTIONS_KEY =\n  \"ecInitOptions\" as unknown as InjectionKey<InitOptionsInjection>;\nexport const UPDATE_OPTIONS_KEY =\n  \"ecUpdateOptions\" as unknown as InjectionKey<UpdateOptionsInjection>;\nexport { LOADING_OPTIONS_KEY } from \"./composables\";\n\nconst NATIVE_EVENT_RE = /(^&?~?!?)native:/;\n\nexport default defineComponent({\n  name: \"echarts\",\n  props: {\n    option: Object as PropType<Option>,\n    theme: {\n      type: [Object, String] as PropType<Theme>\n    },\n    initOptions: Object as PropType<InitOptions>,\n    updateOptions: Object as PropType<UpdateOptions>,\n    group: String,\n    manualUpdate: Boolean,\n    ...autoresizeProps,\n    ...loadingProps\n  },\n  emits: {} as unknown as Emits,\n  inheritAttrs: false,\n  setup(props, { attrs }) {\n    const root = shallowRef<EChartsElement>();\n    const chart = shallowRef<EChartsType>();\n    const manualOption = shallowRef<Option>();\n    const defaultTheme = inject(THEME_KEY, null);\n    const defaultInitOptions = inject(INIT_OPTIONS_KEY, null);\n    const defaultUpdateOptions = inject(UPDATE_OPTIONS_KEY, null);\n\n    const { autoresize, manualUpdate, loading, loadingOptions } = toRefs(props);\n\n    const realOption = computed(\n      () => manualOption.value || props.option || null\n    );\n    const realTheme = computed(\n      () => props.theme || unwrapInjected(defaultTheme, {})\n    );\n    const realInitOptions = computed(\n      () => props.initOptions || unwrapInjected(defaultInitOptions, {})\n    );\n    const realUpdateOptions = computed(\n      () => props.updateOptions || unwrapInjected(defaultUpdateOptions, {})\n    );\n    const nonEventAttrs = computed(() => omitOn(attrs));\n    const nativeListeners: Record<string, unknown> = {};\n\n    // @ts-expect-error listeners for Vue 2 compatibility\n    const listeners = getCurrentInstance().proxy.$listeners;\n    const realListeners: Record<string, any> = {};\n\n    if (!listeners) {\n      // This is for Vue 3.\n      // We are converting all `on<Event>` props to event listeners compatible with Vue 2\n      // and collect them into `realListeners` so that we can bind them to the chart instance\n      // later in the same way.\n      // For `onNative:<event>` props, we just strip the `Native:` part and collect them into\n      // `nativeListeners` so that we can bind them to the root element directly.\n      Object.keys(attrs)\n        .filter(key => isOn(key))\n        .forEach(key => {\n          // onClick    -> c + lick\n          // onZr:click -> z + r:click\n          let event = key.charAt(2).toLowerCase() + key.slice(3);\n\n          // Collect native DOM events\n          if (event.indexOf(\"native:\") === 0) {\n            // native:click -> onClick\n            const nativeKey = `on${event.charAt(7).toUpperCase()}${event.slice(\n              8\n            )}`;\n\n            nativeListeners[nativeKey] = attrs[key];\n            return;\n          }\n\n          // clickOnce    -> ~click\n          // zr:clickOnce -> ~zr:click\n          if (event.substring(event.length - 4) === \"Once\") {\n            event = `~${event.substring(0, event.length - 4)}`;\n          }\n\n          realListeners[event] = attrs[key];\n        });\n    } else {\n      // This is for Vue 2.\n      // We just need to distinguish normal events and `native:<event>` events and\n      // collect them into `realListeners` and `nativeListeners` respectively.\n      // For `native:<event>` events, we just strip the `native:` part and collect them\n      // into `nativeListeners` so that we can bind them to the root element directly.\n      // native:click   -> click\n      // ~native:click  -> ~click\n      // &~!native:click -> &~!click\n      Object.keys(listeners).forEach(key => {\n        if (NATIVE_EVENT_RE.test(key)) {\n          nativeListeners[key.replace(NATIVE_EVENT_RE, \"$1\")] = listeners[key];\n        } else {\n          realListeners[key] = listeners[key];\n        }\n      });\n    }\n\n    function init(option?: Option) {\n      if (!root.value) {\n        return;\n      }\n\n      const instance = (chart.value = initChart(\n        root.value,\n        realTheme.value,\n        realInitOptions.value\n      ));\n\n      if (props.group) {\n        instance.group = props.group;\n      }\n\n      Object.keys(realListeners).forEach(key => {\n        let handler = realListeners[key];\n\n        if (!handler) {\n          return;\n        }\n\n        let event = key.toLowerCase();\n        if (event.charAt(0) === \"~\") {\n          event = event.substring(1);\n          handler.__once__ = true;\n        }\n\n        let target: EventTarget = instance;\n        if (event.indexOf(\"zr:\") === 0) {\n          target = instance.getZr();\n          event = event.substring(3);\n        }\n\n        if (handler.__once__) {\n          delete handler.__once__;\n\n          const raw = handler;\n\n          handler = (...args: any[]) => {\n            raw(...args);\n            target.off(event, handler);\n          };\n        }\n\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore EChartsType[\"on\"] is not compatible with ZRenderType[\"on\"]\n        // but it's okay here\n        target.on(event, handler);\n      });\n\n      function resize() {\n        if (instance && !instance.isDisposed()) {\n          instance.resize();\n        }\n      }\n\n      function commit() {\n        const opt = option || realOption.value;\n        if (opt) {\n          instance.setOption(opt, realUpdateOptions.value);\n        }\n      }\n\n      if (autoresize.value) {\n        // Try to make chart fit to container in case container size\n        // is changed synchronously or in already queued microtasks\n        nextTick(() => {\n          resize();\n          commit();\n        });\n      } else {\n        commit();\n      }\n    }\n\n    function setOption(option: Option, updateOptions?: UpdateOptions) {\n      if (props.manualUpdate) {\n        manualOption.value = option;\n      }\n\n      if (!chart.value) {\n        init(option);\n      } else {\n        chart.value.setOption(option, updateOptions || {});\n      }\n    }\n\n    function cleanup() {\n      if (chart.value) {\n        chart.value.dispose();\n        chart.value = undefined;\n      }\n    }\n\n    let unwatchOption: (() => void) | null = null;\n    watch(\n      manualUpdate,\n      manualUpdate => {\n        if (typeof unwatchOption === \"function\") {\n          unwatchOption();\n          unwatchOption = null;\n        }\n\n        if (!manualUpdate) {\n          unwatchOption = watch(\n            () => props.option,\n            (option, oldOption) => {\n              if (!option) {\n                return;\n              }\n              if (!chart.value) {\n                init();\n              } else {\n                chart.value.setOption(option, {\n                  // mutating `option` will lead to `notMerge: false` and\n                  // replacing it with new reference will lead to `notMerge: true`\n                  notMerge: option !== oldOption,\n                  ...realUpdateOptions.value\n                });\n              }\n            },\n            { deep: true }\n          );\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      [realTheme, realInitOptions],\n      () => {\n        cleanup();\n        init();\n      },\n      {\n        deep: true\n      }\n    );\n\n    watchEffect(() => {\n      if (props.group && chart.value) {\n        chart.value.group = props.group;\n      }\n    });\n\n    const publicApi = usePublicAPI(chart);\n\n    useLoading(chart, loading, loadingOptions);\n\n    useAutoresize(chart, autoresize, root);\n\n    onMounted(() => {\n      init();\n    });\n\n    onBeforeUnmount(() => {\n      if (wcRegistered && root.value) {\n        // For registered web component, we can leverage the\n        // `disconnectedCallback` to dispose the chart instance\n        // so that we can delay the cleanup after exsiting leaving\n        // transition.\n        root.value.__dispose = cleanup;\n      } else {\n        cleanup();\n      }\n    });\n\n    return {\n      chart,\n      root,\n      setOption,\n      nonEventAttrs,\n      nativeListeners,\n      ...publicApi\n    };\n  },\n  render() {\n    // Vue 3 and Vue 2 have different vnode props format:\n    // See https://v3-migration.vuejs.org/breaking-changes/render-function-api.html#vnode-props-format\n    const attrs = (\n      Vue2\n        ? { attrs: this.nonEventAttrs, on: this.nativeListeners }\n        : { ...this.nonEventAttrs, ...this.nativeListeners }\n    ) as any;\n    attrs.ref = \"root\";\n    attrs.class = attrs.class ? [\"echarts\"].concat(attrs.class) : \"echarts\";\n    return h(TAG_NAME, attrs);\n  }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,IAAI,OAAO;;;ACAX,IAAM,eAAe;EACnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAMO,SAAS,aACd,OACe;AACf,WAAS,iBACP,MACqE;AACrE,WAAO,IAAI,SAAS;AACd,UAAA,CAAC,MAAM,OAAO;AACV,cAAA,IAAI,MAAM,iCAAiC;MAAA;AAEnD,aAAQ,MAAM,MAAM,IAAI,EAAU,MAAM,MAAM,OAAO,IAAI;IAAA;EAC3D;AAGF,WAAS,oBAAmC;AACpC,UAAA,UAAiB,uBAAA,OAAO,IAAI;AAClC,iBAAa,QAAQ,CAAQ,SAAA;AACnB,cAAA,IAAI,IAAI,iBAAiB,IAAI;IAAA,CACtC;AAEM,WAAA;EAAA;AAGT,SAAO,kBAAkB;AAC3B;AC5CgB,SAAA,cACd,OACA,YACA,MACM;AACN;IACE,CAAC,MAAM,OAAO,UAAU;IACxB,CAAC,CAACA,OAAMC,QAAOC,WAAU,GAAG,GAAG,cAAc;AAC3C,UAAI,KAA4B;AAE5BF,UAAAA,SAAQC,UAASC,aAAY;AACzB,cAAA,EAAE,aAAa,aAAA,IAAiBF;AACtC,cAAM,oBAAoBE,gBAAe,OAAO,CAAA,IAAKA;AACrD,cAAM,EAAE,UAAU,OAAO,KAAK,SAAA,IAAa;AAE3C,YAAI,yBAAyB;AAE7B,cAAM,WAAW,MAAM;AACrBD,iBAAM,OAAO;AACF;QAAA;AAGb,cAAM,iBAAiB,OAAO,SAAS,UAAU,IAAI,IAAI;AAEpD,aAAA,IAAI,eAAe,MAAM;AAG5B,cAAI,CAAC,wBAAwB;AACF,qCAAA;AACzB,gBACED,MAAK,gBAAgB,eACrBA,MAAK,iBAAiB,cACtB;AACA;YAAA;UACF;AAEa,yBAAA;QAAA,CAChB;AACD,WAAG,QAAQA,KAAI;MAAA;AAGjB,gBAAU,MAAM;AACd,YAAI,IAAI;AACN,aAAG,WAAW;AACT,eAAA;QAAA;MACP,CACD;IAAA;EACH;AAEJ;AAEO,IAAM,kBAAkB;EAC7B,YAAY,CAAC,SAAS,MAAM;AAC9B;AChDA,IAAM,OAAO;AACN,IAAM,OAAO,CAAC,QAAyB,KAAK,KAAK,GAAG;AAEpD,SAAS,OAAO,OAAqB;AAC1C,QAAM,SAAgB,CAAA;AACtB,aAAW,OAAO,OAAO;AACnB,QAAA,CAAC,KAAK,GAAG,GAAG;AACP,aAAA,GAAG,IAAI,MAAM,GAAG;IAAA;EACzB;AAGK,SAAA;AACT;AAEgB,SAAA,eACd,WACA,cACO;AACP,QAAM,QAAQ,MAAM,SAAS,IAAI,MAAM,SAAS,IAAI;AAEpD,MAAI,SAAS,OAAO,UAAU,YAAY,WAAW,OAAO;AAC1D,WAAO,MAAM,SAAS;EAAA;AAGxB,SAAO,SAAS;AAClB;AC9BO,IAAM,sBACX;AAIc,SAAA,WACd,OACA,SACA,gBACM;AACN,QAAM,wBAAwB,OAAO,qBAAqB,CAAA,CAAE;AACtD,QAAA,qBAAqB,SAAS,OAAO;IACzC,GAAG,eAAe,uBAAuB,CAAA,CAAE;IAC3C,GAAG,iDAAgB;EAAA,EACnB;AAEF,cAAY,MAAM;AAChB,UAAM,WAAW,MAAM;AACvB,QAAI,CAAC,UAAU;AACb;IAAA;AAGF,QAAI,QAAQ,OAAO;AACR,eAAA,YAAY,mBAAmB,KAAK;IAAA,OACxC;AACL,eAAS,YAAY;IAAA;EACvB,CACD;AACH;AAEO,IAAM,eAAe;EAC1B,SAAS;EACT,gBAAgB;AAClB;ACvCA,IAAI,aAA6B;AAE1B,IAAM,WAAW;AAMjB,SAAS,WAAoB;AAClC,MAAI,cAAc,MAAM;AACf,WAAA;EAAA;AAGT,MACE,OAAO,gBAAgB,eACvB,OAAO,mBAAmB,aAC1B;AACA,WAAQ,aAAa;EAAA;AAGnB,MAAA;AAMF,UAAM,MAAM,IAAI;MACd;;;MAGA;IAAA;AAEF,QAAI,QAAQ;EAAA,SACL,GAAG;AACV,WAAQ,aAAa;EAAA;AAGvB,SAAQ,aAAa;AACvB;;ACSA,IAAM,eAAiC,SAAS;AAEhD,IAAI,MAAM;AACH,OAAA,OAAO,gBAAgB,KAAK,QAAQ;AAC3C;AAEO,IAAM,YAAY;AAClB,IAAM,mBACX;AACK,IAAM,qBACX;AAGF,IAAM,kBAAkB;AAExB,IAAA,UAAe,gBAAgB;EAC7B,MAAM;EACN,OAAO;IACL,QAAQ;IACR,OAAO;MACL,MAAM,CAAC,QAAQ,MAAM;IAAA;IAEvB,aAAa;IACb,eAAe;IACf,OAAO;IACP,cAAc;IACd,GAAG;IACH,GAAG;EAAA;EAEL,OAAO,CAAA;EACP,cAAc;EACd,MAAM,OAAO,EAAE,MAAA,GAAS;AACtB,UAAM,OAAO,WAA2B;AACxC,UAAM,QAAQ,WAAwB;AACtC,UAAM,eAAe,WAAmB;AAClC,UAAA,eAAe,OAAO,WAAW,IAAI;AACrC,UAAA,qBAAqB,OAAO,kBAAkB,IAAI;AAClD,UAAA,uBAAuB,OAAO,oBAAoB,IAAI;AAE5D,UAAM,EAAE,YAAY,cAAc,SAAS,eAAe,IAAI,OAAO,KAAK;AAE1E,UAAM,aAAa;MACjB,MAAM,aAAa,SAAS,MAAM,UAAU;IAAA;AAE9C,UAAM,YAAY;MAChB,MAAM,MAAM,SAAS,eAAe,cAAc,CAAA,CAAE;IAAA;AAEtD,UAAM,kBAAkB;MACtB,MAAM,MAAM,eAAe,eAAe,oBAAoB,CAAA,CAAE;IAAA;AAElE,UAAM,oBAAoB;MACxB,MAAM,MAAM,iBAAiB,eAAe,sBAAsB,CAAA,CAAE;IAAA;AAEtE,UAAM,gBAAgB,SAAS,MAAM,OAAO,KAAK,CAAC;AAClD,UAAM,kBAA2C,CAAA;AAG3C,UAAA,YAAY,mBAAmB,EAAE,MAAM;AAC7C,UAAM,gBAAqC,CAAA;AAE3C,QAAI,CAAC,WAAW;AAOP,aAAA,KAAK,KAAK,EACd,OAAO,CAAA,QAAO,KAAK,GAAG,CAAC,EACvB,QAAQ,CAAO,QAAA;AAGV,YAAA,QAAQ,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAGrD,YAAI,MAAM,QAAQ,SAAS,MAAM,GAAG;AAE5B,gBAAA,YAAY,KAAK,MAAM,OAAO,CAAC,EAAE,YAAA,CAAa,GAAG,MAAM;YAC3D;UAAA,CACD;AAEe,0BAAA,SAAS,IAAI,MAAM,GAAG;AACtC;QAAA;AAKF,YAAI,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,QAAQ;AAChD,kBAAQ,IAAI,MAAM,UAAU,GAAG,MAAM,SAAS,CAAC,CAAC;QAAA;AAGpC,sBAAA,KAAK,IAAI,MAAM,GAAG;MAAA,CACjC;IAAA,OACE;AASL,aAAO,KAAK,SAAS,EAAE,QAAQ,CAAO,QAAA;AAChC,YAAA,gBAAgB,KAAK,GAAG,GAAG;AAC7B,0BAAgB,IAAI,QAAQ,iBAAiB,IAAI,CAAC,IAAI,UAAU,GAAG;QAAA,OAC9D;AACS,wBAAA,GAAG,IAAI,UAAU,GAAG;QAAA;MACpC,CACD;IAAA;AAGH,aAASG,OAAK,QAAiB;AACzB,UAAA,CAAC,KAAK,OAAO;AACf;MAAA;AAGI,YAAA,WAAY,MAAM,QAAQC;QAC9B,KAAK;QACL,UAAU;QACV,gBAAgB;MAAA;AAGlB,UAAI,MAAM,OAAO;AACf,iBAAS,QAAQ,MAAM;MAAA;AAGzB,aAAO,KAAK,aAAa,EAAE,QAAQ,CAAO,QAAA;AACpC,YAAA,UAAU,cAAc,GAAG;AAE/B,YAAI,CAAC,SAAS;AACZ;QAAA;AAGE,YAAA,QAAQ,IAAI,YAAY;AAC5B,YAAI,MAAM,OAAO,CAAC,MAAM,KAAK;AACnB,kBAAA,MAAM,UAAU,CAAC;AACzB,kBAAQ,WAAW;QAAA;AAGrB,YAAI,SAAsB;AAC1B,YAAI,MAAM,QAAQ,KAAK,MAAM,GAAG;AAC9B,mBAAS,SAAS,MAAM;AAChB,kBAAA,MAAM,UAAU,CAAC;QAAA;AAG3B,YAAI,QAAQ,UAAU;AACpB,iBAAO,QAAQ;AAEf,gBAAM,MAAM;AAEZ,oBAAU,IAAI,SAAgB;AAC5B,gBAAI,GAAG,IAAI;AACJ,mBAAA,IAAI,OAAO,OAAO;UAAA;QAC3B;AAMK,eAAA,GAAG,OAAO,OAAO;MAAA,CACzB;AAED,eAAS,SAAS;AAChB,YAAI,YAAY,CAAC,SAAS,WAAA,GAAc;AACtC,mBAAS,OAAO;QAAA;MAClB;AAGF,eAAS,SAAS;AACV,cAAA,MAAM,UAAU,WAAW;AACjC,YAAI,KAAK;AACE,mBAAA,UAAU,KAAK,kBAAkB,KAAK;QAAA;MACjD;AAGF,UAAI,WAAW,OAAO;AAGpB,iBAAS,MAAM;AACN,iBAAA;AACA,iBAAA;QAAA,CACR;MAAA,OACI;AACE,eAAA;MAAA;IACT;AAGO,aAAA,UAAU,QAAgB,eAA+B;AAChE,UAAI,MAAM,cAAc;AACtB,qBAAa,QAAQ;MAAA;AAGnB,UAAA,CAAC,MAAM,OAAO;AAChBD,eAAK,MAAM;MAAA,OACN;AACL,cAAM,MAAM,UAAU,QAAQ,iBAAiB,CAAA,CAAE;MAAA;IACnD;AAGF,aAAS,UAAU;AACjB,UAAI,MAAM,OAAO;AACf,cAAM,MAAM,QAAQ;AACpB,cAAM,QAAQ;MAAA;IAChB;AAGF,QAAI,gBAAqC;AACzC;MACE;MACA,CAAAE,kBAAgB;AACV,YAAA,OAAO,kBAAkB,YAAY;AACzB,wBAAA;AACE,0BAAA;QAAA;AAGlB,YAAI,CAACA,eAAc;AACD,0BAAA;YACd,MAAM,MAAM;YACZ,CAAC,QAAQ,cAAc;AACrB,kBAAI,CAAC,QAAQ;AACX;cAAA;AAEE,kBAAA,CAAC,MAAM,OAAO;AACXF,uBAAA;cAAA,OACA;AACC,sBAAA,MAAM,UAAU,QAAQ;;;kBAG5B,UAAU,WAAW;kBACrB,GAAG,kBAAkB;gBAAA,CACtB;cAAA;YACH;YAEF,EAAE,MAAM,KAAK;UAAA;QACf;MACF;MAEF;QACE,WAAW;MAAA;IACb;AAGF;MACE,CAAC,WAAW,eAAe;MAC3B,MAAM;AACI,gBAAA;AACHA,eAAA;MAAA;MAEP;QACE,MAAM;MAAA;IACR;AAGF,gBAAY,MAAM;AACZ,UAAA,MAAM,SAAS,MAAM,OAAO;AACxB,cAAA,MAAM,QAAQ,MAAM;MAAA;IAC5B,CACD;AAEK,UAAA,YAAY,aAAa,KAAK;AAEzB,eAAA,OAAO,SAAS,cAAc;AAE3B,kBAAA,OAAO,YAAY,IAAI;AAErC,cAAU,MAAM;AACTA,aAAA;IAAA,CACN;AAED,oBAAgB,MAAM;AAChB,UAAA,gBAAgB,KAAK,OAAO;AAK9B,aAAK,MAAM,YAAY;MAAA,OAClB;AACG,gBAAA;MAAA;IACV,CACD;AAEM,WAAA;MACL;MACA;MACA;MACA;MACA;MACA,GAAG;IAAA;EACL;EAEF,SAAS;AAGP,UAAM,QACJ,OACI,EAAE,OAAO,KAAK,eAAe,IAAI,KAAK,gBAAgB,IACtD,EAAE,GAAG,KAAK,eAAe,GAAG,KAAK,gBAAgB;AAEvD,UAAM,MAAM;AACN,UAAA,QAAQ,MAAM,QAAQ,CAAC,SAAS,EAAE,OAAO,MAAM,KAAK,IAAI;AACvD,WAAA,EAAE,UAAU,KAAK;EAAA;AAE5B,CAAC;", "names": ["root", "chart", "autoresize", "init", "initChart", "manualUpdate"]}