<template>
  <div class="courses-view">
    <div class="page-header">
      <h2>课程设计</h2>
      <p>设计和管理你的教学课程，跟踪学习效果</p>
    </div>
    
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button type="primary" @click="createCourse">
          <el-icon><Plus /></el-icon>
          创建新课程
        </el-button>
        <el-button @click="importCourse">
          <el-icon><Upload /></el-icon>
          导入课程
        </el-button>
      </div>
      <div class="action-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索课程..."
          style="width: 250px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 课程统计 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#409EFF"><Document /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ courseStats.totalCourses }}</div>
          <div class="stat-label">总课程数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#67C23A"><User /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ courseStats.totalStudents }}</div>
          <div class="stat-label">学习人数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#E6A23C"><TrendCharts /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ courseStats.avgCompletion }}%</div>
          <div class="stat-label">平均完成率</div>
        </div>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="courses-section">
      <div class="section-header">
        <h3>我的课程</h3>
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="进行中" value="active" />
          <el-option label="已完成" value="completed" />
          <el-option label="草稿" value="draft" />
        </el-select>
      </div>
      
      <div class="courses-grid">
        <div 
          v-for="course in filteredCourses" 
          :key="course.id"
          class="course-card"
        >
          <div class="course-header">
            <div class="course-status">
              <el-tag 
                :type="getStatusType(course.status)" 
                size="small"
              >
                {{ getStatusText(course.status) }}
              </el-tag>
            </div>
            <el-dropdown @command="(command) => handleCourseAction(command, course)">
              <el-button type="text">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="duplicate">复制</el-dropdown-item>
                  <el-dropdown-item command="export">导出</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="course-thumbnail">
            <img :src="course.thumbnail" :alt="course.title" />
            <div class="course-duration">{{ course.duration }}分钟</div>
          </div>
          
          <div class="course-content">
            <h4>{{ course.title }}</h4>
            <p>{{ course.description }}</p>
            
            <div class="course-meta">
              <div class="meta-item">
                <el-icon><User /></el-icon>
                <span>{{ course.studentsCount }}人学习</span>
              </div>
              <div class="meta-item">
                <el-icon><Star /></el-icon>
                <span>{{ course.rating }}/5</span>
              </div>
            </div>
            
            <div class="course-progress">
              <div class="progress-label">学习进度</div>
              <el-progress 
                :percentage="course.avgProgress" 
                :stroke-width="6"
              />
            </div>
            
            <div class="course-actions">
              <el-button type="primary" @click="editCourse(course)">
                编辑课程
              </el-button>
              <el-button @click="viewAnalytics(course)">
                查看数据
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程创建对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新课程"
      width="600px"
    >
      <el-form :model="newCourse" label-width="100px">
        <el-form-item label="课程标题">
          <el-input v-model="newCourse.title" placeholder="请输入课程标题" />
        </el-form-item>
        <el-form-item label="课程描述">
          <el-input 
            v-model="newCourse.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入课程描述"
          />
        </el-form-item>
        <el-form-item label="课程分类">
          <el-select v-model="newCourse.category" placeholder="选择分类">
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="英语" value="英语" />
            <el-option label="科学" value="科学" />
            <el-option label="艺术" value="艺术" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级">
          <el-rate v-model="newCourse.level" :max="5" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCourse">创建课程</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Plus, Upload, Search, Document, User, TrendCharts, 
  MoreFilled, Star 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const showCreateDialog = ref(false)

// 课程统计
const courseStats = ref({
  totalCourses: 8,
  totalStudents: 156,
  avgCompletion: 78
})

// 新课程表单
const newCourse = ref({
  title: '',
  description: '',
  category: '',
  level: 1
})

// 课程数据
const courses = ref([
  {
    id: 1,
    title: '古诗词鉴赏入门',
    description: '通过经典古诗词学习，培养文学素养和审美能力',
    category: '语文',
    status: 'active',
    thumbnail: 'https://via.placeholder.com/300x180',
    duration: 45,
    studentsCount: 32,
    rating: 4.8,
    avgProgress: 75
  },
  {
    id: 2,
    title: '数学思维训练',
    description: '通过趣味数学题目，提升逻辑思维和解题能力',
    category: '数学',
    status: 'active',
    thumbnail: 'https://via.placeholder.com/300x180',
    duration: 60,
    studentsCount: 28,
    rating: 4.6,
    avgProgress: 68
  },
  {
    id: 3,
    title: '英语口语练习',
    description: '通过情景对话，提升英语口语表达能力',
    category: '英语',
    status: 'draft',
    thumbnail: 'https://via.placeholder.com/300x180',
    duration: 30,
    studentsCount: 0,
    rating: 0,
    avgProgress: 0
  }
])

// 过滤课程
const filteredCourses = computed(() => {
  let filtered = courses.value
  
  if (searchQuery.value) {
    filtered = filtered.filter(course => 
      course.title.includes(searchQuery.value) || 
      course.description.includes(searchQuery.value)
    )
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(course => course.status === statusFilter.value)
  }
  
  return filtered
})

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap = {
    active: 'success',
    completed: 'info',
    draft: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    active: '进行中',
    completed: '已完成',
    draft: '草稿'
  }
  return textMap[status] || '未知'
}

// 创建课程
const createCourse = () => {
  showCreateDialog.value = true
}

// 导入课程
const importCourse = () => {
  ElMessage.info('课程导入功能开发中')
}

// 保存课程
const saveCourse = () => {
  if (!newCourse.value.title) {
    ElMessage.warning('请输入课程标题')
    return
  }
  
  ElMessage.success('课程创建成功')
  showCreateDialog.value = false
  
  // 重置表单
  newCourse.value = {
    title: '',
    description: '',
    category: '',
    level: 1
  }
}

// 编辑课程
const editCourse = (course: any) => {
  ElMessage.info(`编辑课程：${course.title}`)
}

// 查看数据分析
const viewAnalytics = (course: any) => {
  ElMessage.info(`查看课程"${course.title}"的数据分析`)
}

// 处理课程操作
const handleCourseAction = (command: string, course: any) => {
  switch (command) {
    case 'edit':
      editCourse(course)
      break
    case 'duplicate':
      ElMessage.success(`课程"${course.title}"已复制`)
      break
    case 'export':
      ElMessage.success(`课程"${course.title}"已导出`)
      break
    case 'delete':
      ElMessage.warning(`确定要删除课程"${course.title}"吗？`)
      break
  }
}
</script>

<style scoped>
.courses-view {
  padding: 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-left {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.courses-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.course-card {
  border: 1px solid #eee;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
}

.course-thumbnail {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.course-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-duration {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.course-content {
  padding: 16px;
}

.course-content h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.course-content p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.course-progress {
  margin-bottom: 16px;
}

.progress-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
}

.course-actions {
  display: flex;
  gap: 8px;
}

.course-actions .el-button {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 15px;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
  }
}
</style>
