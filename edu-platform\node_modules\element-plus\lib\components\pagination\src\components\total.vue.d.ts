declare const _default: import("vue").DefineComponent<{
    readonly total: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 1000, boolean>;
}, {}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly total: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 1000, boolean>;
}>>, {
    readonly total: number;
}>;
export default _default;
