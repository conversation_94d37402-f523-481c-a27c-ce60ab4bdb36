<template>
  <div class="content-view">
    <div class="page-header">
      <h2>内容管理</h2>
      <p>管理课程、AI模型和学习内容</p>
    </div>
    
    <div class="content-tabs">
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="课程管理" name="courses">
          <div class="courses-section">
            <div class="section-header">
              <h3>课程列表</h3>
              <el-button type="primary">
                <el-icon><Plus /></el-icon>
                新增课程
              </el-button>
            </div>
            <el-table :data="courses" style="width: 100%">
              <el-table-column prop="title" label="课程名称" />
              <el-table-column prop="category" label="分类" />
              <el-table-column prop="level" label="难度等级" />
              <el-table-column prop="studentsCount" label="学习人数" />
              <el-table-column label="操作">
                <template #default>
                  <el-button type="primary" size="small">编辑</el-button>
                  <el-button type="danger" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="AI模型管理" name="ai">
          <div class="ai-section">
            <div class="section-header">
              <h3>AI历史人物</h3>
            </div>
            <div class="ai-figures">
              <div v-for="figure in historicalFigures" :key="figure.id" class="figure-card">
                <el-avatar :size="60" :src="figure.avatar" />
                <h4>{{ figure.name }}</h4>
                <p>{{ figure.description }}</p>
                <el-button type="primary" size="small">调优</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { coursesData, historicalFigures } from '@/mock/data'

const activeTab = ref('courses')
const courses = ref(coursesData.list.slice(0, 10))
</script>

<style scoped>
.content-view {
  padding: 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.content-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.ai-figures {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.figure-card {
  text-align: center;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.figure-card h4 {
  margin: 10px 0 5px 0;
  color: #333;
}

.figure-card p {
  margin: 0 0 15px 0;
  color: #666;
  font-size: 14px;
}
</style>
