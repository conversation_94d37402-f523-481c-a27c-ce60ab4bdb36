<template>
  <div class="volunteer-dashboard">
    <!-- 欢迎卡片 -->
    <div class="welcome-card">
      <div class="welcome-content">
        <h2>欢迎回来，{{ volunteerInfo.name }}！</h2>
        <p>今天又是充满爱心的一天，让我们一起帮助孩子们成长吧！</p>
      </div>
      <div class="welcome-stats">
        <div class="stat-item">
          <div class="stat-value">{{ volunteerStats.totalHours }}</div>
          <div class="stat-label">总服务时长(小时)</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ volunteerStats.helpedChildren }}</div>
          <div class="stat-label">帮助的孩子</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ volunteerStats.rating }}</div>
          <div class="stat-label">评分</div>
        </div>
      </div>
    </div>

    <!-- 今日任务 -->
    <div class="section">
      <div class="section-header">
        <h3>今日任务</h3>
        <el-tag type="info" size="small">{{ todayTasks.length }} 个任务</el-tag>
      </div>
      <div class="tasks-list">
        <div 
          v-for="task in todayTasks" 
          :key="task.id"
          class="task-item"
          :class="{ completed: task.completed }"
        >
          <div class="task-info">
            <div class="task-title">{{ task.title }}</div>
            <div class="task-time">{{ task.time }}</div>
          </div>
          <div class="task-actions">
            <el-button 
              v-if="!task.completed"
              type="primary" 
              size="small"
              @click="completeTask(task)"
            >
              开始
            </el-button>
            <el-tag v-else type="success" size="small">已完成</el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 我的孩子 -->
    <div class="section">
      <div class="section-header">
        <h3>我的孩子</h3>
        <el-button type="text" @click="$router.push('/volunteer/children')">
          查看全部
        </el-button>
      </div>
      <div class="children-grid">
        <div 
          v-for="child in myChildren" 
          :key="child.id"
          class="child-card"
          @click="chatWithChild(child)"
        >
          <el-avatar :size="60" :src="child.avatar" />
          <div class="child-info">
            <div class="child-name">{{ child.name }}</div>
            <div class="child-age">{{ child.age }}岁</div>
            <div class="child-mood">
              <el-tag :type="getMoodType(child.mood)" size="small">
                {{ getMoodText(child.mood) }}
              </el-tag>
            </div>
          </div>
          <div class="child-status">
            <el-badge 
              :value="child.unreadMessages" 
              :hidden="child.unreadMessages === 0"
              class="message-badge"
            >
              <el-icon size="20" color="#409EFF"><ChatDotRound /></el-icon>
            </el-badge>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="section">
      <div class="section-header">
        <h3>最近活动</h3>
      </div>
      <div class="activities-list">
        <div 
          v-for="activity in recentActivities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <el-icon :color="getActivityColor(activity.type)">
              <component :is="getActivityIcon(activity.type)" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 培训推荐 -->
    <div class="section">
      <div class="section-header">
        <h3>推荐培训</h3>
        <el-button type="text" @click="$router.push('/volunteer/training')">
          查看更多
        </el-button>
      </div>
      <div class="training-cards">
        <div 
          v-for="training in recommendedTraining" 
          :key="training.id"
          class="training-card"
        >
          <div class="training-thumbnail">
            <img :src="training.thumbnail" :alt="training.title" />
          </div>
          <div class="training-content">
            <div class="training-title">{{ training.title }}</div>
            <div class="training-description">{{ training.description }}</div>
            <div class="training-meta">
              <span class="training-duration">{{ training.duration }}分钟</span>
              <el-rate 
                v-model="training.rating" 
                disabled 
                size="small"
                show-score
              />
            </div>
            <el-button type="primary" size="small" @click="startTraining(training)">
              开始学习
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  ChatDotRound, Reading, Trophy, Heart, 
  MessageBox, Clock 
} from '@element-plus/icons-vue'
import { childrenData } from '@/mock/data'
import { ElMessage } from 'element-plus'

// 志愿者信息
const volunteerInfo = ref({
  name: '张老师'
})

// 志愿者统计
const volunteerStats = ref({
  totalHours: 156,
  helpedChildren: 12,
  rating: 4.8
})

// 今日任务
const todayTasks = ref([
  {
    id: 1,
    title: '与小明进行心理辅导',
    time: '14:00-15:00',
    completed: false
  },
  {
    id: 2,
    title: '参加志愿者培训',
    time: '16:00-17:00',
    completed: true
  },
  {
    id: 3,
    title: '填写服务记录',
    time: '17:30-18:00',
    completed: false
  }
])

// 我的孩子
const myChildren = ref(
  childrenData.list.slice(0, 4).map(child => ({
    ...child,
    unreadMessages: Math.floor(Math.random() * 5)
  }))
)

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: 'chat',
    title: '与小明聊天',
    description: '进行了30分钟的学习指导',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 2,
    type: 'training',
    title: '完成培训课程',
    description: '《儿童心理学基础》课程学习完成',
    time: new Date(Date.now() - 4 * 60 * 60 * 1000)
  },
  {
    id: 3,
    type: 'achievement',
    title: '获得成就',
    description: '获得"爱心志愿者"徽章',
    time: new Date(Date.now() - 6 * 60 * 60 * 1000)
  }
])

// 推荐培训
const recommendedTraining = ref([
  {
    id: 1,
    title: '儿童沟通技巧',
    description: '学习如何更好地与不同年龄段的儿童沟通',
    thumbnail: 'https://via.placeholder.com/200x120',
    duration: 45,
    rating: 4.5
  },
  {
    id: 2,
    title: '心理危机干预',
    description: '识别和处理儿童心理危机的方法',
    thumbnail: 'https://via.placeholder.com/200x120',
    duration: 60,
    rating: 4.8
  }
])

// 获取心情类型
const getMoodType = (mood: string) => {
  const typeMap = {
    happy: 'success',
    calm: 'primary',
    excited: 'warning',
    sad: 'danger',
    confused: 'info'
  }
  return typeMap[mood] || 'info'
}

// 获取心情文本
const getMoodText = (mood: string) => {
  const textMap = {
    happy: '开心',
    calm: '平静',
    excited: '兴奋',
    sad: '难过',
    confused: '困惑'
  }
  return textMap[mood] || '未知'
}

// 获取活动图标
const getActivityIcon = (type: string) => {
  const iconMap = {
    chat: ChatDotRound,
    training: Reading,
    achievement: Trophy
  }
  return iconMap[type] || MessageBox
}

// 获取活动颜色
const getActivityColor = (type: string) => {
  const colorMap = {
    chat: '#409EFF',
    training: '#67C23A',
    achievement: '#E6A23C'
  }
  return colorMap[type] || '#909399'
}

// 完成任务
const completeTask = (task: any) => {
  task.completed = true
  ElMessage.success(`任务"${task.title}"已开始`)
}

// 与孩子聊天
const chatWithChild = (child: any) => {
  ElMessage.success(`开始与${child.name}聊天`)
}

// 开始培训
const startTraining = (training: any) => {
  ElMessage.success(`开始学习"${training.title}"`)
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) {
    return '刚刚'
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.volunteer-dashboard {
  padding: 0;
}

.welcome-card {
  background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
}

.welcome-content p {
  margin: 0;
  opacity: 0.9;
}

.welcome-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.task-item:hover {
  border-color: #67C23A;
  background: #f0f9ff;
}

.task-item.completed {
  opacity: 0.6;
  background: #f9f9f9;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.task-time {
  font-size: 14px;
  color: #666;
}

.children-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.child-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.child-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #67C23A;
}

.child-info {
  text-align: center;
  margin: 12px 0;
}

.child-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.child-age {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.child-status {
  position: absolute;
  top: 15px;
  right: 15px;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f7fa;
  flex-shrink: 0;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

.training-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.training-card {
  border: 1px solid #eee;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.training-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.training-thumbnail {
  height: 120px;
  overflow: hidden;
}

.training-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.training-content {
  padding: 16px;
}

.training-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.training-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.4;
}

.training-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.training-duration {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-card {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .welcome-stats {
    justify-content: center;
  }
  
  .children-grid {
    grid-template-columns: 1fr;
  }
  
  .training-cards {
    grid-template-columns: 1fr;
  }
}
</style>
