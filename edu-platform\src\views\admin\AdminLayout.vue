<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
        <div class="logo">
          <el-icon v-if="isCollapse" size="24" color="#409EFF"><Monitor /></el-icon>
          <template v-else>
            <el-icon size="24" color="#409EFF"><Monitor /></el-icon>
            <span class="logo-text">管理后台</span>
          </template>
        </div>

        <el-menu
          :default-active="$route.name"
          class="sidebar-menu"
          :collapse="isCollapse"
          :router="true"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#409EFF"
        >
          <el-menu-item index="admin-dashboard" route="/admin/dashboard">
            <el-icon><Monitor /></el-icon>
            <template #title>数据大屏</template>
          </el-menu-item>

          <el-sub-menu index="users">
            <template #title>
              <el-icon><User /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="admin-users" route="/admin/users">
              <el-icon><UserFilled /></el-icon>
              <template #title>用户列表</template>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="content">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>内容管理</span>
            </template>
            <el-menu-item index="admin-content" route="/admin/content">
              <el-icon><Reading /></el-icon>
              <template #title>课程管理</template>
            </el-menu-item>
          </el-sub-menu>

          <el-menu-item index="admin-analytics" route="/admin/analytics">
            <el-icon><DataAnalysis /></el-icon>
            <template #title>数据分析</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              type="text"
              @click="toggleCollapse"
              class="collapse-btn"
            >
              <el-icon size="20">
                <Expand v-if="isCollapse" />
                <Fold v-else />
              </el-icon>
            </el-button>

            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/admin' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ getBreadcrumbText() }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <div class="header-right">
            <!-- 通知 -->
            <el-badge :value="notifications.length" class="notification-badge">
              <el-button type="text" @click="showNotifications = true">
                <el-icon size="18"><Bell /></el-icon>
              </el-button>
            </el-badge>

            <!-- 用户菜单 -->
            <el-dropdown @command="handleUserCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="adminUser.avatar" />
                <span class="username">{{ adminUser.name }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主要内容区域 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>

    <!-- 通知抽屉 -->
    <el-drawer
      v-model="showNotifications"
      title="系统通知"
      direction="rtl"
      size="400px"
    >
      <div class="notifications-list">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-item"
          :class="{ unread: !notification.read }"
        >
          <div class="notification-icon">
            <el-icon
              :color="getNotificationColor(notification.type)"
              size="20"
            >
              <component :is="getNotificationIcon(notification.type)" />
            </el-icon>
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.time) }}</div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  Monitor, User, UserFilled, Document, Reading,
  DataAnalysis, Expand, Fold, Bell, ArrowDown, Warning,
  InfoFilled, SuccessFilled
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const route = useRoute()

// 响应式数据
const isCollapse = ref(false)
const showNotifications = ref(false)

// 管理员信息
const adminUser = ref({
  name: '管理员',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
})

// 通知数据
const notifications = ref([
  {
    id: 1,
    type: 'warning',
    title: '系统警告',
    message: '检测到异常用户行为，请及时处理',
    time: new Date(Date.now() - 30 * 60 * 1000),
    read: false
  },
  {
    id: 2,
    type: 'info',
    title: '系统更新',
    message: '系统将在今晚23:00进行维护更新',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: false
  },
  {
    id: 3,
    type: 'success',
    title: '数据备份完成',
    message: '今日数据备份已成功完成',
    time: new Date(Date.now() - 4 * 60 * 60 * 1000),
    read: true
  }
])

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 获取面包屑文本
const getBreadcrumbText = () => {
  const routeMap = {
    'admin-dashboard': '数据大屏',
    'admin-users': '用户管理',
    'admin-content': '内容管理',
    'admin-analytics': '数据分析'
  }
  return routeMap[route.name as string] || '未知页面'
}

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人设置功能开发中')
      break
    case 'logout':
      ElMessage.success('退出登录成功')
      // 这里可以添加退出登录逻辑
      break
  }
}

// 获取通知图标
const getNotificationIcon = (type: string) => {
  const iconMap = {
    warning: Warning,
    info: InfoFilled,
    success: SuccessFilled
  }
  return iconMap[type] || InfoFilled
}

// 获取通知颜色
const getNotificationColor = (type: string) => {
  const colorMap = {
    warning: '#E6A23C',
    info: '#409EFF',
    success: '#67C23A'
  }
  return colorMap[type] || '#409EFF'
}

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.sidebar {
  background: #001529;
  transition: width 0.3s ease;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: white;
  border-bottom: 1px solid #1f1f1f;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.sidebar-menu {
  border-right: none;
  height: calc(100vh - 64px);
}

.header {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.collapse-btn {
  padding: 8px !important;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-badge {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #333;
}

.main-content {
  background: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

.notifications-list {
  padding: 0;
}

.notification-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.notification-item:hover {
  background: #f5f7fa;
}

.notification-item.unread {
  background: #f0f9ff;
  border-left: 3px solid #409EFF;
}

.notification-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f7fa;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-left {
    gap: 10px;
  }

  .header-right {
    gap: 10px;
  }

  .username {
    display: none;
  }
}
</style>
