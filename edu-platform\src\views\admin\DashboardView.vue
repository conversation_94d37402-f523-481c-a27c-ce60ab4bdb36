<template>
  <div class="dashboard-view">
    <!-- 实时监控卡片 -->
    <div class="monitor-cards">
      <div class="monitor-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon color="#409EFF"><User /></el-icon>
            在线用户
          </div>
          <div class="card-value">{{ realtimeData.onlineUsers }}</div>
        </div>
        <div class="card-trend">
          <el-icon color="#67C23A"><TrendCharts /></el-icon>
          <span class="trend-text positive">+12%</span>
        </div>
      </div>

      <div class="monitor-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon color="#67C23A"><ChatDotRound /></el-icon>
            活跃会话
          </div>
          <div class="card-value">{{ realtimeData.activeSessions }}</div>
        </div>
        <div class="card-trend">
          <el-icon color="#67C23A"><TrendCharts /></el-icon>
          <span class="trend-text positive">+8%</span>
        </div>
      </div>

      <div class="monitor-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon color="#E6A23C"><Message /></el-icon>
            消息/分钟
          </div>
          <div class="card-value">{{ realtimeData.messagesPerMinute }}</div>
        </div>
        <div class="card-trend">
          <el-icon color="#F56C6C"><TrendCharts /></el-icon>
          <span class="trend-text negative">-3%</span>
        </div>
      </div>

      <div class="monitor-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon color="#909399"><Clock /></el-icon>
            响应时间
          </div>
          <div class="card-value">{{ realtimeData.responseTime }}ms</div>
        </div>
        <div class="card-trend">
          <el-icon color="#67C23A"><TrendCharts /></el-icon>
          <span class="trend-text positive">-15ms</span>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 用户增长趋势 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长趋势</h3>
            <el-radio-group v-model="userGrowthPeriod" size="small">
              <el-radio-button label="7d">7天</el-radio-button>
              <el-radio-button label="30d">30天</el-radio-button>
              <el-radio-button label="90d">90天</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-container">
            <v-chart :option="userGrowthOption" style="height: 300px;" />
          </div>
        </div>

        <!-- 学习活跃度 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>学习活跃度分布</h3>
          </div>
          <div class="chart-container">
            <v-chart :option="activityOption" style="height: 300px;" />
          </div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 心理健康指标 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>心理健康指标</h3>
            <el-tag type="warning" size="small">{{ riskAlerts.length }} 个风险预警</el-tag>
          </div>
          <div class="chart-container">
            <v-chart :option="mentalHealthOption" style="height: 300px;" />
          </div>
        </div>

        <!-- 系统性能监控 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>系统性能监控</h3>
          </div>
          <div class="chart-container">
            <v-chart :option="systemPerformanceOption" style="height: 300px;" />
          </div>
        </div>
      </div>
    </div>

    <!-- 风险预警列表 -->
    <div class="alerts-section">
      <div class="section-header">
        <h3>风险预警</h3>
        <el-button type="primary" size="small" @click="refreshAlerts">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <div class="alerts-list">
        <div
          v-for="alert in riskAlerts"
          :key="alert.userId"
          class="alert-item"
          :class="getRiskLevelClass(alert.riskLevel)"
        >
          <div class="alert-icon">
            <el-icon size="20">
              <Warning v-if="alert.riskLevel === 'high'" />
              <InfoFilled v-else-if="alert.riskLevel === 'medium'" />
              <SuccessFilled v-else />
            </el-icon>
          </div>
          <div class="alert-content">
            <div class="alert-title">
              用户 {{ alert.userName }} - {{ getRiskLevelText(alert.riskLevel) }}风险
            </div>
            <div class="alert-indicators">
              <el-tag
                v-for="indicator in alert.indicators"
                :key="indicator"
                size="small"
                type="warning"
              >
                {{ indicator }}
              </el-tag>
            </div>
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
          </div>
          <div class="alert-actions">
            <el-button type="primary" size="small" @click="handleAlert(alert)">
              处理
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="quick-stats">
      <div class="stat-item">
        <div class="stat-label">总用户数</div>
        <div class="stat-value">{{ statisticsData.totalUsers }}</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">活跃用户</div>
        <div class="stat-value">{{ statisticsData.activeUsers }}</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">总课程数</div>
        <div class="stat-value">{{ statisticsData.totalCourses }}</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">完成会话</div>
        <div class="stat-value">{{ statisticsData.completedSessions }}</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">平均学习时长</div>
        <div class="stat-value">{{ statisticsData.averageStudyTime }}分钟</div>
      </div>
      <div class="stat-item">
        <div class="stat-label">满意度评分</div>
        <div class="stat-value">{{ statisticsData.satisfactionScore }}/5</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart, GaugeChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  User, ChatDotRound, Message, Clock, TrendCharts,
  Refresh, Warning, InfoFilled, SuccessFilled
} from '@element-plus/icons-vue'
import { realtimeData, statisticsData, mentalHealthData } from '@/mock/data'
import { ElMessage } from 'element-plus'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  GaugeChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const userGrowthPeriod = ref('30d')
const riskAlerts = ref(mentalHealthData.riskAlerts)

// 用户增长图表配置
const userGrowthOption = ref({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['新增用户', '活跃用户']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '新增用户',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210],
      smooth: true,
      itemStyle: { color: '#409EFF' }
    },
    {
      name: '活跃用户',
      type: 'line',
      data: [220, 182, 191, 234, 290, 330, 310],
      smooth: true,
      itemStyle: { color: '#67C23A' }
    }
  ]
})

// 学习活跃度图表配置
const activityOption = ref({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '活跃度分布',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '高活跃' },
        { value: 735, name: '中活跃' },
        { value: 580, name: '低活跃' },
        { value: 484, name: '不活跃' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

// 心理健康指标图表配置
const mentalHealthOption = ref({
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      name: '情绪分布',
      type: 'pie',
      radius: ['40%', '70%'],
      data: [
        { value: mentalHealthData.moodDistribution.happy, name: '开心' },
        { value: mentalHealthData.moodDistribution.calm, name: '平静' },
        { value: mentalHealthData.moodDistribution.excited, name: '兴奋' },
        { value: mentalHealthData.moodDistribution.sad, name: '难过' },
        { value: mentalHealthData.moodDistribution.confused, name: '困惑' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

// 系统性能监控图表配置
const systemPerformanceOption = ref({
  tooltip: {
    formatter: '{a} <br/>{b} : {c}%'
  },
  series: [
    {
      name: 'CPU使用率',
      type: 'gauge',
      detail: { formatter: '{value}%' },
      data: [{ value: realtimeData.systemLoad, name: 'CPU' }],
      axisLine: {
        lineStyle: {
          color: [
            [0.3, '#67C23A'],
            [0.7, '#E6A23C'],
            [1, '#F56C6C']
          ]
        }
      }
    }
  ]
})

// 获取风险等级样式类
const getRiskLevelClass = (level: string) => {
  const classMap = {
    high: 'risk-high',
    medium: 'risk-medium',
    low: 'risk-low'
  }
  return classMap[level] || 'risk-low'
}

// 获取风险等级文本
const getRiskLevelText = (level: string) => {
  const textMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[level] || '未知'
}

// 处理预警
const handleAlert = (alert: any) => {
  ElMessage.success(`正在处理用户 ${alert.userName} 的风险预警`)
}

// 刷新预警
const refreshAlerts = () => {
  ElMessage.success('预警数据已刷新')
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

// 组件挂载时启动实时数据更新
onMounted(() => {
  // 模拟实时数据更新
  setInterval(() => {
    realtimeData.onlineUsers += Math.floor(Math.random() * 10 - 5)
    realtimeData.activeSessions += Math.floor(Math.random() * 6 - 3)
    realtimeData.messagesPerMinute += Math.floor(Math.random() * 8 - 4)
    realtimeData.responseTime += Math.floor(Math.random() * 20 - 10)

    // 确保数据在合理范围内
    realtimeData.onlineUsers = Math.max(50, Math.min(300, realtimeData.onlineUsers))
    realtimeData.activeSessions = Math.max(20, Math.min(150, realtimeData.activeSessions))
    realtimeData.messagesPerMinute = Math.max(10, Math.min(80, realtimeData.messagesPerMinute))
    realtimeData.responseTime = Math.max(50, Math.min(800, realtimeData.responseTime))
  }, 5000)
})
</script>

<style scoped>
.dashboard-view {
  padding: 0;
}

.monitor-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.monitor-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.monitor-card:hover {
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.trend-text.positive {
  color: #67C23A;
}

.trend-text.negative {
  color: #F56C6C;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.alerts-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
}

.alert-item.risk-high {
  background: #fef0f0;
  border-left-color: #F56C6C;
}

.alert-item.risk-medium {
  background: #fdf6ec;
  border-left-color: #E6A23C;
}

.alert-item.risk-low {
  background: #f0f9ff;
  border-left-color: #409EFF;
}

.alert-icon {
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.alert-indicators {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.alert-time {
  font-size: 12px;
  color: #999;
}

.quick-stats {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .monitor-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-row {
    grid-template-columns: 1fr;
  }

  .quick-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .monitor-cards {
    grid-template-columns: 1fr;
  }

  .quick-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
