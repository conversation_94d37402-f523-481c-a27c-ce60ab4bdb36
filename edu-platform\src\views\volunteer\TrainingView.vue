<template>
  <div class="training-view">
    <div class="page-header">
      <h2>培训中心</h2>
      <p>提升你的志愿服务技能</p>
    </div>
    
    <div class="training-categories">
      <el-button
        v-for="category in categories"
        :key="category"
        :type="selectedCategory === category ? 'primary' : 'default'"
        @click="selectedCategory = category"
      >
        {{ category }}
      </el-button>
    </div>
    
    <div class="training-grid">
      <div 
        v-for="training in filteredTraining" 
        :key="training.id"
        class="training-card"
      >
        <div class="training-thumbnail">
          <img :src="training.thumbnail" :alt="training.title" />
          <div class="training-duration">{{ training.duration }}分钟</div>
        </div>
        <div class="training-content">
          <h3>{{ training.title }}</h3>
          <p>{{ training.description }}</p>
          <div class="training-meta">
            <el-rate 
              v-model="training.rating" 
              disabled 
              size="small"
              show-score
            />
            <span class="training-students">{{ training.students }}人学习</span>
          </div>
          <div class="training-progress" v-if="training.progress > 0">
            <el-progress 
              :percentage="training.progress" 
              :stroke-width="6"
            />
            <span class="progress-text">已完成 {{ training.progress }}%</span>
          </div>
          <el-button 
            type="primary" 
            @click="startTraining(training)"
            :disabled="training.progress === 100"
          >
            {{ training.progress === 0 ? '开始学习' : training.progress === 100 ? '已完成' : '继续学习' }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

const selectedCategory = ref('全部')

const categories = ['全部', '心理学基础', '沟通技巧', '危机干预', '儿童发展']

const trainingList = ref([
  {
    id: 1,
    title: '儿童心理学基础',
    description: '了解儿童心理发展的基本规律和特点',
    category: '心理学基础',
    thumbnail: 'https://via.placeholder.com/300x180',
    duration: 60,
    rating: 4.8,
    students: 1250,
    progress: 75
  },
  {
    id: 2,
    title: '有效沟通技巧',
    description: '学习与不同年龄段儿童的沟通方法',
    category: '沟通技巧',
    thumbnail: 'https://via.placeholder.com/300x180',
    duration: 45,
    rating: 4.6,
    students: 980,
    progress: 0
  },
  {
    id: 3,
    title: '心理危机识别与干预',
    description: '识别儿童心理危机信号并进行适当干预',
    category: '危机干预',
    thumbnail: 'https://via.placeholder.com/300x180',
    duration: 90,
    rating: 4.9,
    students: 756,
    progress: 100
  },
  {
    id: 4,
    title: '儿童认知发展理论',
    description: '深入了解皮亚杰等理论家的儿童认知发展观点',
    category: '儿童发展',
    thumbnail: 'https://via.placeholder.com/300x180',
    duration: 75,
    rating: 4.7,
    students: 642,
    progress: 30
  }
])

const filteredTraining = computed(() => {
  if (selectedCategory.value === '全部') {
    return trainingList.value
  }
  return trainingList.value.filter(training => training.category === selectedCategory.value)
})

const startTraining = (training: any) => {
  if (training.progress === 100) {
    ElMessage.info('该课程已完成')
    return
  }
  ElMessage.success(`开始学习"${training.title}"`)
}
</script>

<style scoped>
.training-view {
  padding: 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.training-categories {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.training-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.training-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.training-card:hover {
  transform: translateY(-2px);
}

.training-thumbnail {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.training-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.training-duration {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.training-content {
  padding: 20px;
}

.training-content h3 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 18px;
}

.training-content p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.training-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.training-students {
  font-size: 12px;
  color: #999;
}

.training-progress {
  margin-bottom: 16px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  display: block;
}
</style>
