<template>
  <div class="home-view">
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">儿童成长平台</h1>
        <p class="hero-subtitle">集虚拟身份社交、AI对话学习、心理帮扶于一体的儿童成长平台</p>
        <div class="hero-features">
          <div class="feature-item">
            <el-icon size="24" color="#409EFF"><ChatDotRound /></el-icon>
            <span>AI历史人物对话</span>
          </div>
          <div class="feature-item">
            <el-icon size="24" color="#67C23A"><Reading /></el-icon>
            <span>个性化学习</span>
          </div>
          <div class="feature-item">
            <el-icon size="24" color="#E6A23C"><User /></el-icon>
            <span>心理健康关怀</span>
          </div>
        </div>
      </div>
    </div>

    <div class="platforms-section">
      <h2 class="section-title">选择您的身份</h2>
      <div class="platforms-grid">
        <!-- 儿童端 -->
        <div class="platform-card child-platform" @click="navigateTo('/child')">
          <div class="platform-icon">
            <el-icon size="48" color="#409EFF"><Star /></el-icon>
          </div>
          <h3>儿童端</h3>
          <p>与AI历史人物对话，参与趣味学习，获得成长陪伴</p>
          <ul class="platform-features">
            <li>AI历史人物对话</li>
            <li>志愿者聊天</li>
            <li>课程学习</li>
            <li>每日挑战</li>
            <li>成就系统</li>
          </ul>
          <el-button type="primary" size="large">
            进入儿童端
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>

        <!-- 志愿者端 -->
        <div class="platform-card volunteer-platform" @click="navigateTo('/volunteer')">
          <div class="platform-icon">
            <el-icon size="48" color="#67C23A"><User /></el-icon>
          </div>
          <h3>志愿者端</h3>
          <p>参与儿童成长陪伴，提供专业指导和心理支持</p>
          <ul class="platform-features">
            <li>儿童匹配管理</li>
            <li>专业培训</li>
            <li>服务记录</li>
            <li>沟通工具</li>
            <li>成长追踪</li>
          </ul>
          <el-button type="success" size="large">
            进入志愿者端
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>

        <!-- 管理端 -->
        <div class="platform-card admin-platform" @click="navigateTo('/admin')">
          <div class="platform-icon">
            <el-icon size="48" color="#E6A23C"><Monitor /></el-icon>
          </div>
          <h3>管理端</h3>
          <p>平台运营管理，数据分析监控，内容审核维护</p>
          <ul class="platform-features">
            <li>数据大屏</li>
            <li>用户管理</li>
            <li>内容管理</li>
            <li>风险预警</li>
            <li>数据分析</li>
          </ul>
          <el-button type="warning" size="large">
            进入管理端
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <div class="demo-info">
      <el-alert
        title="演示说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p>这是一个功能完整的演示项目，展示了儿童成长平台的核心功能和界面设计。</p>
          <p>所有数据均为模拟数据，用于演示目的。实际生产环境需要后端API支持。</p>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ChatDotRound, Reading, User, Star, Monitor, ArrowRight } from '@element-plus/icons-vue'

const router = useRouter()

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style scoped>
.home-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.hero-section {
  padding: 80px 20px;
  text-align: center;
  color: white;
}

.hero-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 20px;
  margin-bottom: 40px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.platforms-section {
  padding: 60px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.section-title {
  text-align: center;
  font-size: 32px;
  color: #333;
  margin-bottom: 50px;
  font-weight: 600;
}

.platforms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.platform-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.platform-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.child-platform:hover {
  border-color: #409EFF;
}

.volunteer-platform:hover {
  border-color: #67C23A;
}

.admin-platform:hover {
  border-color: #E6A23C;
}

.platform-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(64, 158, 255, 0.1);
}

.volunteer-platform .platform-icon {
  background: rgba(103, 194, 58, 0.1);
}

.admin-platform .platform-icon {
  background: rgba(230, 162, 60, 0.1);
}

.platform-card h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
}

.platform-card p {
  font-size: 16px;
  color: #666;
  margin-bottom: 25px;
  line-height: 1.6;
}

.platform-features {
  list-style: none;
  padding: 0;
  margin: 0 0 30px 0;
  text-align: left;
}

.platform-features li {
  padding: 8px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
}

.platform-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #67C23A;
  font-weight: bold;
}

.demo-info {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .hero-features {
    flex-direction: column;
    gap: 20px;
  }

  .platforms-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .platform-card {
    padding: 30px 20px;
  }

  .section-title {
    font-size: 28px;
  }
}
</style>
