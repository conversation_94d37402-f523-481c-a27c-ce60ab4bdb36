{"version": 3, "sources": ["../../mockjs/dist/mock.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Mock\"] = factory();\n\telse\n\t\troot[\"Mock\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n\n\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global require, module, window */\n\tvar Handler = __webpack_require__(1)\n\tvar Util = __webpack_require__(3)\n\tvar Random = __webpack_require__(5)\n\tvar RE = __webpack_require__(20)\n\tvar toJSONSchema = __webpack_require__(23)\n\tvar valid = __webpack_require__(25)\n\n\tvar XHR\n\tif (typeof window !== 'undefined') XHR = __webpack_require__(27)\n\n\t/*!\n\t    Mock - 模拟请求 & 模拟数据\n\t    https://github.com/nuysoft/Mock\n\t    墨智 <EMAIL> <EMAIL>\n\t*/\n\tvar Mock = {\n\t    Handler: Handler,\n\t    Random: Random,\n\t    Util: Util,\n\t    XHR: XHR,\n\t    RE: RE,\n\t    toJSONSchema: toJSONSchema,\n\t    valid: valid,\n\t    heredoc: Util.heredoc,\n\t    setup: function(settings) {\n\t        return XHR.setup(settings)\n\t    },\n\t    _mocked: {}\n\t}\n\n\tMock.version = '1.0.1-beta3'\n\n\t// 避免循环依赖\n\tif (XHR) XHR.Mock = Mock\n\n\t/*\n\t    * Mock.mock( template )\n\t    * Mock.mock( function() )\n\t    * Mock.mock( rurl, template )\n\t    * Mock.mock( rurl, function(options) )\n\t    * Mock.mock( rurl, rtype, template )\n\t    * Mock.mock( rurl, rtype, function(options) )\n\n\t    根据数据模板生成模拟数据。\n\t*/\n\tMock.mock = function(rurl, rtype, template) {\n\t    // Mock.mock(template)\n\t    if (arguments.length === 1) {\n\t        return Handler.gen(rurl)\n\t    }\n\t    // Mock.mock(rurl, template)\n\t    if (arguments.length === 2) {\n\t        template = rtype\n\t        rtype = undefined\n\t    }\n\t    // 拦截 XHR\n\t    if (XHR) window.XMLHttpRequest = XHR\n\t    Mock._mocked[rurl + (rtype || '')] = {\n\t        rurl: rurl,\n\t        rtype: rtype,\n\t        template: template\n\t    }\n\t    return Mock\n\t}\n\n\tmodule.exports = Mock\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* \n\t    ## Handler\n\n\t    处理数据模板。\n\t    \n\t    * Handler.gen( template, name?, context? )\n\n\t        入口方法。\n\n\t    * Data Template Definition, DTD\n\t        \n\t        处理数据模板定义。\n\n\t        * Handler.array( options )\n\t        * Handler.object( options )\n\t        * Handler.number( options )\n\t        * Handler.boolean( options )\n\t        * Handler.string( options )\n\t        * Handler.function( options )\n\t        * Handler.regexp( options )\n\t        \n\t        处理路径（相对和绝对）。\n\n\t        * Handler.getValueByKeyPath( key, options )\n\n\t    * Data Placeholder Definition, DPD\n\n\t        处理数据占位符定义\n\n\t        * Handler.placeholder( placeholder, context, templateContext, options )\n\n\t*/\n\n\tvar Constant = __webpack_require__(2)\n\tvar Util = __webpack_require__(3)\n\tvar Parser = __webpack_require__(4)\n\tvar Random = __webpack_require__(5)\n\tvar RE = __webpack_require__(20)\n\n\tvar Handler = {\n\t    extend: Util.extend\n\t}\n\n\t/*\n\t    template        属性值（即数据模板）\n\t    name            属性名\n\t    context         数据上下文，生成后的数据\n\t    templateContext 模板上下文，\n\n\t    Handle.gen(template, name, options)\n\t    context\n\t        currentContext, templateCurrentContext, \n\t        path, templatePath\n\t        root, templateRoot\n\t*/\n\tHandler.gen = function(template, name, context) {\n\t    /* jshint -W041 */\n\t    name = name == undefined ? '' : (name + '')\n\n\t    context = context || {}\n\t    context = {\n\t            // 当前访问路径，只有属性名，不包括生成规则\n\t            path: context.path || [Constant.GUID],\n\t            templatePath: context.templatePath || [Constant.GUID++],\n\t            // 最终属性值的上下文\n\t            currentContext: context.currentContext,\n\t            // 属性值模板的上下文\n\t            templateCurrentContext: context.templateCurrentContext || template,\n\t            // 最终值的根\n\t            root: context.root || context.currentContext,\n\t            // 模板的根\n\t            templateRoot: context.templateRoot || context.templateCurrentContext || template\n\t        }\n\t        // console.log('path:', context.path.join('.'), template)\n\n\t    var rule = Parser.parse(name)\n\t    var type = Util.type(template)\n\t    var data\n\n\t    if (Handler[type]) {\n\t        data = Handler[type]({\n\t            // 属性值类型\n\t            type: type,\n\t            // 属性值模板\n\t            template: template,\n\t            // 属性名 + 生成规则\n\t            name: name,\n\t            // 属性名\n\t            parsedName: name ? name.replace(Constant.RE_KEY, '$1') : name,\n\n\t            // 解析后的生成规则\n\t            rule: rule,\n\t            // 相关上下文\n\t            context: context\n\t        })\n\n\t        if (!context.root) context.root = data\n\t        return data\n\t    }\n\n\t    return template\n\t}\n\n\tHandler.extend({\n\t    array: function(options) {\n\t        var result = [],\n\t            i, ii;\n\n\t        // 'name|1': []\n\t        // 'name|count': []\n\t        // 'name|min-max': []\n\t        if (options.template.length === 0) return result\n\n\t        // 'arr': [{ 'email': '@EMAIL' }, { 'email': '@EMAIL' }]\n\t        if (!options.rule.parameters) {\n\t            for (i = 0; i < options.template.length; i++) {\n\t                options.context.path.push(i)\n\t                options.context.templatePath.push(i)\n\t                result.push(\n\t                    Handler.gen(options.template[i], i, {\n\t                        path: options.context.path,\n\t                        templatePath: options.context.templatePath,\n\t                        currentContext: result,\n\t                        templateCurrentContext: options.template,\n\t                        root: options.context.root || result,\n\t                        templateRoot: options.context.templateRoot || options.template\n\t                    })\n\t                )\n\t                options.context.path.pop()\n\t                options.context.templatePath.pop()\n\t            }\n\t        } else {\n\t            // 'method|1': ['GET', 'POST', 'HEAD', 'DELETE']\n\t            if (options.rule.min === 1 && options.rule.max === undefined) {\n\t                // fix #17\n\t                options.context.path.push(options.name)\n\t                options.context.templatePath.push(options.name)\n\t                result = Random.pick(\n\t                    Handler.gen(options.template, undefined, {\n\t                        path: options.context.path,\n\t                        templatePath: options.context.templatePath,\n\t                        currentContext: result,\n\t                        templateCurrentContext: options.template,\n\t                        root: options.context.root || result,\n\t                        templateRoot: options.context.templateRoot || options.template\n\t                    })\n\t                )\n\t                options.context.path.pop()\n\t                options.context.templatePath.pop()\n\t            } else {\n\t                // 'data|+1': [{}, {}]\n\t                if (options.rule.parameters[2]) {\n\t                    options.template.__order_index = options.template.__order_index || 0\n\n\t                    options.context.path.push(options.name)\n\t                    options.context.templatePath.push(options.name)\n\t                    result = Handler.gen(options.template, undefined, {\n\t                        path: options.context.path,\n\t                        templatePath: options.context.templatePath,\n\t                        currentContext: result,\n\t                        templateCurrentContext: options.template,\n\t                        root: options.context.root || result,\n\t                        templateRoot: options.context.templateRoot || options.template\n\t                    })[\n\t                        options.template.__order_index % options.template.length\n\t                    ]\n\n\t                    options.template.__order_index += +options.rule.parameters[2]\n\n\t                    options.context.path.pop()\n\t                    options.context.templatePath.pop()\n\n\t                } else {\n\t                    // 'data|1-10': [{}]\n\t                    for (i = 0; i < options.rule.count; i++) {\n\t                        // 'data|1-10': [{}, {}]\n\t                        for (ii = 0; ii < options.template.length; ii++) {\n\t                            options.context.path.push(result.length)\n\t                            options.context.templatePath.push(ii)\n\t                            result.push(\n\t                                Handler.gen(options.template[ii], result.length, {\n\t                                    path: options.context.path,\n\t                                    templatePath: options.context.templatePath,\n\t                                    currentContext: result,\n\t                                    templateCurrentContext: options.template,\n\t                                    root: options.context.root || result,\n\t                                    templateRoot: options.context.templateRoot || options.template\n\t                                })\n\t                            )\n\t                            options.context.path.pop()\n\t                            options.context.templatePath.pop()\n\t                        }\n\t                    }\n\t                }\n\t            }\n\t        }\n\t        return result\n\t    },\n\t    object: function(options) {\n\t        var result = {},\n\t            keys, fnKeys, key, parsedKey, inc, i;\n\n\t        // 'obj|min-max': {}\n\t        /* jshint -W041 */\n\t        if (options.rule.min != undefined) {\n\t            keys = Util.keys(options.template)\n\t            keys = Random.shuffle(keys)\n\t            keys = keys.slice(0, options.rule.count)\n\t            for (i = 0; i < keys.length; i++) {\n\t                key = keys[i]\n\t                parsedKey = key.replace(Constant.RE_KEY, '$1')\n\t                options.context.path.push(parsedKey)\n\t                options.context.templatePath.push(key)\n\t                result[parsedKey] = Handler.gen(options.template[key], key, {\n\t                    path: options.context.path,\n\t                    templatePath: options.context.templatePath,\n\t                    currentContext: result,\n\t                    templateCurrentContext: options.template,\n\t                    root: options.context.root || result,\n\t                    templateRoot: options.context.templateRoot || options.template\n\t                })\n\t                options.context.path.pop()\n\t                options.context.templatePath.pop()\n\t            }\n\n\t        } else {\n\t            // 'obj': {}\n\t            keys = []\n\t            fnKeys = [] // #25 改变了非函数属性的顺序，查找起来不方便\n\t            for (key in options.template) {\n\t                (typeof options.template[key] === 'function' ? fnKeys : keys).push(key)\n\t            }\n\t            keys = keys.concat(fnKeys)\n\n\t            /*\n\t                会改变非函数属性的顺序\n\t                keys = Util.keys(options.template)\n\t                keys.sort(function(a, b) {\n\t                    var afn = typeof options.template[a] === 'function'\n\t                    var bfn = typeof options.template[b] === 'function'\n\t                    if (afn === bfn) return 0\n\t                    if (afn && !bfn) return 1\n\t                    if (!afn && bfn) return -1\n\t                })\n\t            */\n\n\t            for (i = 0; i < keys.length; i++) {\n\t                key = keys[i]\n\t                parsedKey = key.replace(Constant.RE_KEY, '$1')\n\t                options.context.path.push(parsedKey)\n\t                options.context.templatePath.push(key)\n\t                result[parsedKey] = Handler.gen(options.template[key], key, {\n\t                    path: options.context.path,\n\t                    templatePath: options.context.templatePath,\n\t                    currentContext: result,\n\t                    templateCurrentContext: options.template,\n\t                    root: options.context.root || result,\n\t                    templateRoot: options.context.templateRoot || options.template\n\t                })\n\t                options.context.path.pop()\n\t                options.context.templatePath.pop()\n\t                    // 'id|+1': 1\n\t                inc = key.match(Constant.RE_KEY)\n\t                if (inc && inc[2] && Util.type(options.template[key]) === 'number') {\n\t                    options.template[key] += parseInt(inc[2], 10)\n\t                }\n\t            }\n\t        }\n\t        return result\n\t    },\n\t    number: function(options) {\n\t        var result, parts;\n\t        if (options.rule.decimal) { // float\n\t            options.template += ''\n\t            parts = options.template.split('.')\n\t                // 'float1|.1-10': 10,\n\t                // 'float2|1-100.1-10': 1,\n\t                // 'float3|999.1-10': 1,\n\t                // 'float4|.3-10': 123.123,\n\t            parts[0] = options.rule.range ? options.rule.count : parts[0]\n\t            parts[1] = (parts[1] || '').slice(0, options.rule.dcount)\n\t            while (parts[1].length < options.rule.dcount) {\n\t                parts[1] += (\n\t                    // 最后一位不能为 0：如果最后一位为 0，会被 JS 引擎忽略掉。\n\t                    (parts[1].length < options.rule.dcount - 1) ? Random.character('number') : Random.character('123456789')\n\t                )\n\t            }\n\t            result = parseFloat(parts.join('.'), 10)\n\t        } else { // integer\n\t            // 'grade1|1-100': 1,\n\t            result = options.rule.range && !options.rule.parameters[2] ? options.rule.count : options.template\n\t        }\n\t        return result\n\t    },\n\t    boolean: function(options) {\n\t        var result;\n\t        // 'prop|multiple': false, 当前值是相反值的概率倍数\n\t        // 'prop|probability-probability': false, 当前值与相反值的概率\n\t        result = options.rule.parameters ? Random.bool(options.rule.min, options.rule.max, options.template) : options.template\n\t        return result\n\t    },\n\t    string: function(options) {\n\t        var result = '',\n\t            i, placeholders, ph, phed;\n\t        if (options.template.length) {\n\n\t            //  'foo': '★',\n\t            /* jshint -W041 */\n\t            if (options.rule.count == undefined) {\n\t                result += options.template\n\t            }\n\n\t            // 'star|1-5': '★',\n\t            for (i = 0; i < options.rule.count; i++) {\n\t                result += options.template\n\t            }\n\t            // 'email|1-10': '@EMAIL, ',\n\t            placeholders = result.match(Constant.RE_PLACEHOLDER) || [] // A-Z_0-9 > \\w_\n\t            for (i = 0; i < placeholders.length; i++) {\n\t                ph = placeholders[i]\n\n\t                // 遇到转义斜杠，不需要解析占位符\n\t                if (/^\\\\/.test(ph)) {\n\t                    placeholders.splice(i--, 1)\n\t                    continue\n\t                }\n\n\t                phed = Handler.placeholder(ph, options.context.currentContext, options.context.templateCurrentContext, options)\n\n\t                // 只有一个占位符，并且没有其他字符\n\t                if (placeholders.length === 1 && ph === result && typeof phed !== typeof result) { // \n\t                    result = phed\n\t                    break\n\n\t                    if (Util.isNumeric(phed)) {\n\t                        result = parseFloat(phed, 10)\n\t                        break\n\t                    }\n\t                    if (/^(true|false)$/.test(phed)) {\n\t                        result = phed === 'true' ? true :\n\t                            phed === 'false' ? false :\n\t                            phed // 已经是布尔值\n\t                        break\n\t                    }\n\t                }\n\t                result = result.replace(ph, phed)\n\t            }\n\n\t        } else {\n\t            // 'ASCII|1-10': '',\n\t            // 'ASCII': '',\n\t            result = options.rule.range ? Random.string(options.rule.count) : options.template\n\t        }\n\t        return result\n\t    },\n\t    'function': function(options) {\n\t        // ( context, options )\n\t        return options.template.call(options.context.currentContext, options)\n\t    },\n\t    'regexp': function(options) {\n\t        var source = ''\n\n\t        // 'name': /regexp/,\n\t        /* jshint -W041 */\n\t        if (options.rule.count == undefined) {\n\t            source += options.template.source // regexp.source\n\t        }\n\n\t        // 'name|1-5': /regexp/,\n\t        for (var i = 0; i < options.rule.count; i++) {\n\t            source += options.template.source\n\t        }\n\n\t        return RE.Handler.gen(\n\t            RE.Parser.parse(\n\t                source\n\t            )\n\t        )\n\t    }\n\t})\n\n\tHandler.extend({\n\t    _all: function() {\n\t        var re = {};\n\t        for (var key in Random) re[key.toLowerCase()] = key\n\t        return re\n\t    },\n\t    // 处理占位符，转换为最终值\n\t    placeholder: function(placeholder, obj, templateContext, options) {\n\t        // console.log(options.context.path)\n\t        // 1 key, 2 params\n\t        Constant.RE_PLACEHOLDER.exec('')\n\t        var parts = Constant.RE_PLACEHOLDER.exec(placeholder),\n\t            key = parts && parts[1],\n\t            lkey = key && key.toLowerCase(),\n\t            okey = this._all()[lkey],\n\t            params = parts && parts[2] || ''\n\t        var pathParts = this.splitPathToArray(key)\n\n\t        // 解析占位符的参数\n\t        try {\n\t            // 1. 尝试保持参数的类型\n\t            /*\n\t                #24 [Window Firefox 30.0 引用 占位符 抛错](https://github.com/nuysoft/Mock/issues/24)\n\t                [BX9056: 各浏览器下 window.eval 方法的执行上下文存在差异](http://www.w3help.org/zh-cn/causes/BX9056)\n\t                应该属于 Window Firefox 30.0 的 BUG\n\t            */\n\t            /* jshint -W061 */\n\t            params = eval('(function(){ return [].splice.call(arguments, 0 ) })(' + params + ')')\n\t        } catch (error) {\n\t            // 2. 如果失败，只能解析为字符串\n\t            // console.error(error)\n\t            // if (error instanceof ReferenceError) params = parts[2].split(/,\\s*/);\n\t            // else throw error\n\t            params = parts[2].split(/,\\s*/)\n\t        }\n\n\t        // 占位符优先引用数据模板中的属性\n\t        if (obj && (key in obj)) return obj[key]\n\n\t        // @index @key\n\t        // if (Constant.RE_INDEX.test(key)) return +options.name\n\t        // if (Constant.RE_KEY.test(key)) return options.name\n\n\t        // 绝对路径 or 相对路径\n\t        if (\n\t            key.charAt(0) === '/' ||\n\t            pathParts.length > 1\n\t        ) return this.getValueByKeyPath(key, options)\n\n\t        // 递归引用数据模板中的属性\n\t        if (templateContext &&\n\t            (typeof templateContext === 'object') &&\n\t            (key in templateContext) &&\n\t            (placeholder !== templateContext[key]) // fix #15 避免自己依赖自己\n\t        ) {\n\t            // 先计算被引用的属性值\n\t            templateContext[key] = Handler.gen(templateContext[key], key, {\n\t                currentContext: obj,\n\t                templateCurrentContext: templateContext\n\t            })\n\t            return templateContext[key]\n\t        }\n\n\t        // 如果未找到，则原样返回\n\t        if (!(key in Random) && !(lkey in Random) && !(okey in Random)) return placeholder\n\n\t        // 递归解析参数中的占位符\n\t        for (var i = 0; i < params.length; i++) {\n\t            Constant.RE_PLACEHOLDER.exec('')\n\t            if (Constant.RE_PLACEHOLDER.test(params[i])) {\n\t                params[i] = Handler.placeholder(params[i], obj, templateContext, options)\n\t            }\n\t        }\n\n\t        var handle = Random[key] || Random[lkey] || Random[okey]\n\t        switch (Util.type(handle)) {\n\t            case 'array':\n\t                // 自动从数组中取一个，例如 @areas\n\t                return Random.pick(handle)\n\t            case 'function':\n\t                // 执行占位符方法（大多数情况）\n\t                handle.options = options\n\t                var re = handle.apply(Random, params)\n\t                if (re === undefined) re = '' // 因为是在字符串中，所以默认为空字符串。\n\t                delete handle.options\n\t                return re\n\t        }\n\t    },\n\t    getValueByKeyPath: function(key, options) {\n\t        var originalKey = key\n\t        var keyPathParts = this.splitPathToArray(key)\n\t        var absolutePathParts = []\n\n\t        // 绝对路径\n\t        if (key.charAt(0) === '/') {\n\t            absolutePathParts = [options.context.path[0]].concat(\n\t                this.normalizePath(keyPathParts)\n\t            )\n\t        } else {\n\t            // 相对路径\n\t            if (keyPathParts.length > 1) {\n\t                absolutePathParts = options.context.path.slice(0)\n\t                absolutePathParts.pop()\n\t                absolutePathParts = this.normalizePath(\n\t                    absolutePathParts.concat(keyPathParts)\n\t                )\n\n\t            }\n\t        }\n\n\t        try {\n\t            key = keyPathParts[keyPathParts.length - 1]\n\t            var currentContext = options.context.root\n\t            var templateCurrentContext = options.context.templateRoot\n\t            for (var i = 1; i < absolutePathParts.length - 1; i++) {\n\t                currentContext = currentContext[absolutePathParts[i]]\n\t                templateCurrentContext = templateCurrentContext[absolutePathParts[i]]\n\t            }\n\t            // 引用的值已经计算好\n\t            if (currentContext && (key in currentContext)) return currentContext[key]\n\t    \n\t            // 尚未计算，递归引用数据模板中的属性\n\t            if (templateCurrentContext &&\n\t                (typeof templateCurrentContext === 'object') &&\n\t                (key in templateCurrentContext) &&\n\t                (originalKey !== templateCurrentContext[key]) // fix #15 避免自己依赖自己\n\t            ) {\n\t                // 先计算被引用的属性值\n\t                templateCurrentContext[key] = Handler.gen(templateCurrentContext[key], key, {\n\t                    currentContext: currentContext,\n\t                    templateCurrentContext: templateCurrentContext\n\t                })\n\t                return templateCurrentContext[key]\n\t            }\n\t        } catch(err) { }\n\n\t        return '@' + keyPathParts.join('/')\n\t    },\n\t    // https://github.com/kissyteam/kissy/blob/master/src/path/src/path.js\n\t    normalizePath: function(pathParts) {\n\t        var newPathParts = []\n\t        for (var i = 0; i < pathParts.length; i++) {\n\t            switch (pathParts[i]) {\n\t                case '..':\n\t                    newPathParts.pop()\n\t                    break\n\t                case '.':\n\t                    break\n\t                default:\n\t                    newPathParts.push(pathParts[i])\n\t            }\n\t        }\n\t        return newPathParts\n\t    },\n\t    splitPathToArray: function(path) {\n\t        var parts = path.split(/\\/+/);\n\t        if (!parts[parts.length - 1]) parts = parts.slice(0, -1)\n\t        if (!parts[0]) parts = parts.slice(1)\n\t        return parts;\n\t    }\n\t})\n\n\tmodule.exports = Handler\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Constant\n\n\t    常量集合。\n\t */\n\t/*\n\t    RE_KEY\n\t        'name|min-max': value\n\t        'name|count': value\n\t        'name|min-max.dmin-dmax': value\n\t        'name|min-max.dcount': value\n\t        'name|count.dmin-dmax': value\n\t        'name|count.dcount': value\n\t        'name|+step': value\n\n\t        1 name, 2 step, 3 range [ min, max ], 4 drange [ dmin, dmax ]\n\n\t    RE_PLACEHOLDER\n\t        placeholder(*)\n\n\t    [正则查看工具](http://www.regexper.com/)\n\n\t    #26 生成规则 支持 负数，例如 number|-100-100\n\t*/\n\tmodule.exports = {\n\t    GUID: 1,\n\t    RE_KEY: /(.+)\\|(?:\\+(\\d+)|([\\+\\-]?\\d+-?[\\+\\-]?\\d*)?(?:\\.(\\d+-?\\d*))?)/,\n\t    RE_RANGE: /([\\+\\-]?\\d+)-?([\\+\\-]?\\d+)?/,\n\t    RE_PLACEHOLDER: /\\\\*@([^@#%&()\\?\\s]+)(?:\\((.*?)\\))?/g\n\t    // /\\\\*@([^@#%&()\\?\\s\\/\\.]+)(?:\\((.*?)\\))?/g\n\t    // RE_INDEX: /^index$/,\n\t    // RE_KEY: /^key$/\n\t}\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Utilities\n\t*/\n\tvar Util = {}\n\n\tUtil.extend = function extend() {\n\t    var target = arguments[0] || {},\n\t        i = 1,\n\t        length = arguments.length,\n\t        options, name, src, copy, clone\n\n\t    if (length === 1) {\n\t        target = this\n\t        i = 0\n\t    }\n\n\t    for (; i < length; i++) {\n\t        options = arguments[i]\n\t        if (!options) continue\n\n\t        for (name in options) {\n\t            src = target[name]\n\t            copy = options[name]\n\n\t            if (target === copy) continue\n\t            if (copy === undefined) continue\n\n\t            if (Util.isArray(copy) || Util.isObject(copy)) {\n\t                if (Util.isArray(copy)) clone = src && Util.isArray(src) ? src : []\n\t                if (Util.isObject(copy)) clone = src && Util.isObject(src) ? src : {}\n\n\t                target[name] = Util.extend(clone, copy)\n\t            } else {\n\t                target[name] = copy\n\t            }\n\t        }\n\t    }\n\n\t    return target\n\t}\n\n\tUtil.each = function each(obj, iterator, context) {\n\t    var i, key\n\t    if (this.type(obj) === 'number') {\n\t        for (i = 0; i < obj; i++) {\n\t            iterator(i, i)\n\t        }\n\t    } else if (obj.length === +obj.length) {\n\t        for (i = 0; i < obj.length; i++) {\n\t            if (iterator.call(context, obj[i], i, obj) === false) break\n\t        }\n\t    } else {\n\t        for (key in obj) {\n\t            if (iterator.call(context, obj[key], key, obj) === false) break\n\t        }\n\t    }\n\t}\n\n\tUtil.type = function type(obj) {\n\t    return (obj === null || obj === undefined) ? String(obj) : Object.prototype.toString.call(obj).match(/\\[object (\\w+)\\]/)[1].toLowerCase()\n\t}\n\n\tUtil.each('String Object Array RegExp Function'.split(' '), function(value) {\n\t    Util['is' + value] = function(obj) {\n\t        return Util.type(obj) === value.toLowerCase()\n\t    }\n\t})\n\n\tUtil.isObjectOrArray = function(value) {\n\t    return Util.isObject(value) || Util.isArray(value)\n\t}\n\n\tUtil.isNumeric = function(value) {\n\t    return !isNaN(parseFloat(value)) && isFinite(value)\n\t}\n\n\tUtil.keys = function(obj) {\n\t    var keys = [];\n\t    for (var key in obj) {\n\t        if (obj.hasOwnProperty(key)) keys.push(key)\n\t    }\n\t    return keys;\n\t}\n\tUtil.values = function(obj) {\n\t    var values = [];\n\t    for (var key in obj) {\n\t        if (obj.hasOwnProperty(key)) values.push(obj[key])\n\t    }\n\t    return values;\n\t}\n\n\t/*\n\t    ### Mock.heredoc(fn)\n\n\t    * Mock.heredoc(fn)\n\n\t    以直观、安全的方式书写（多行）HTML 模板。\n\n\t    **使用示例**如下所示：\n\n\t        var tpl = Mock.heredoc(function() {\n\t            /*!\n\t        {{email}}{{age}}\n\t        <!-- Mock { \n\t            email: '@EMAIL',\n\t            age: '@INT(1,100)'\n\t        } -->\n\t            *\\/\n\t        })\n\t    \n\t    **相关阅读**\n\t    * [Creating multiline strings in JavaScript](http://stackoverflow.com/questions/805107/creating-multiline-strings-in-javascript)、\n\t*/\n\tUtil.heredoc = function heredoc(fn) {\n\t    // 1. 移除起始的 function(){ /*!\n\t    // 2. 移除末尾的 */ }\n\t    // 3. 移除起始和末尾的空格\n\t    return fn.toString()\n\t        .replace(/^[^\\/]+\\/\\*!?/, '')\n\t        .replace(/\\*\\/[^\\/]+$/, '')\n\t        .replace(/^[\\s\\xA0]+/, '').replace(/[\\s\\xA0]+$/, '') // .trim()\n\t}\n\n\tUtil.noop = function() {}\n\n\tmodule.exports = Util\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t\t## Parser\n\n\t\t解析数据模板（属性名部分）。\n\n\t\t* Parser.parse( name )\n\t\t\t\n\t\t\t```json\n\t\t\t{\n\t\t\t\tparameters: [ name, inc, range, decimal ],\n\t\t\t\trnage: [ min , max ],\n\n\t\t\t\tmin: min,\n\t\t\t\tmax: max,\n\t\t\t\tcount : count,\n\n\t\t\t\tdecimal: decimal,\n\t\t\t\tdmin: dmin,\n\t\t\t\tdmax: dmax,\n\t\t\t\tdcount: dcount\n\t\t\t}\n\t\t\t```\n\t */\n\n\tvar Constant = __webpack_require__(2)\n\tvar Random = __webpack_require__(5)\n\n\t/* jshint -W041 */\n\tmodule.exports = {\n\t\tparse: function(name) {\n\t\t\tname = name == undefined ? '' : (name + '')\n\n\t\t\tvar parameters = (name || '').match(Constant.RE_KEY)\n\n\t\t\tvar range = parameters && parameters[3] && parameters[3].match(Constant.RE_RANGE)\n\t\t\tvar min = range && range[1] && parseInt(range[1], 10) // || 1\n\t\t\tvar max = range && range[2] && parseInt(range[2], 10) // || 1\n\t\t\t\t// repeat || min-max || 1\n\t\t\t\t// var count = range ? !range[2] && parseInt(range[1], 10) || Random.integer(min, max) : 1\n\t\t\tvar count = range ? !range[2] ? parseInt(range[1], 10) : Random.integer(min, max) : undefined\n\n\t\t\tvar decimal = parameters && parameters[4] && parameters[4].match(Constant.RE_RANGE)\n\t\t\tvar dmin = decimal && decimal[1] && parseInt(decimal[1], 10) // || 0,\n\t\t\tvar dmax = decimal && decimal[2] && parseInt(decimal[2], 10) // || 0,\n\t\t\t\t// int || dmin-dmax || 0\n\t\t\tvar dcount = decimal ? !decimal[2] && parseInt(decimal[1], 10) || Random.integer(dmin, dmax) : undefined\n\n\t\t\tvar result = {\n\t\t\t\t// 1 name, 2 inc, 3 range, 4 decimal\n\t\t\t\tparameters: parameters,\n\t\t\t\t// 1 min, 2 max\n\t\t\t\trange: range,\n\t\t\t\tmin: min,\n\t\t\t\tmax: max,\n\t\t\t\t// min-max\n\t\t\t\tcount: count,\n\t\t\t\t// 是否有 decimal\n\t\t\t\tdecimal: decimal,\n\t\t\t\tdmin: dmin,\n\t\t\t\tdmax: dmax,\n\t\t\t\t// dmin-dimax\n\t\t\t\tdcount: dcount\n\t\t\t}\n\n\t\t\tfor (var r in result) {\n\t\t\t\tif (result[r] != undefined) return result\n\t\t\t}\n\n\t\t\treturn {}\n\t\t}\n\t}\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## Mock.Random\n\t    \n\t    工具类，用于生成各种随机数据。\n\t*/\n\n\tvar Util = __webpack_require__(3)\n\n\tvar Random = {\n\t    extend: Util.extend\n\t}\n\n\tRandom.extend(__webpack_require__(6))\n\tRandom.extend(__webpack_require__(7))\n\tRandom.extend(__webpack_require__(8))\n\tRandom.extend(__webpack_require__(10))\n\tRandom.extend(__webpack_require__(13))\n\tRandom.extend(__webpack_require__(15))\n\tRandom.extend(__webpack_require__(16))\n\tRandom.extend(__webpack_require__(17))\n\tRandom.extend(__webpack_require__(14))\n\tRandom.extend(__webpack_require__(19))\n\n\tmodule.exports = Random\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Basics\n\t*/\n\tmodule.exports = {\n\t    // 返回一个随机的布尔值。\n\t    boolean: function(min, max, cur) {\n\t        if (cur !== undefined) {\n\t            min = typeof min !== 'undefined' && !isNaN(min) ? parseInt(min, 10) : 1\n\t            max = typeof max !== 'undefined' && !isNaN(max) ? parseInt(max, 10) : 1\n\t            return Math.random() > 1.0 / (min + max) * min ? !cur : cur\n\t        }\n\n\t        return Math.random() >= 0.5\n\t    },\n\t    bool: function(min, max, cur) {\n\t        return this.boolean(min, max, cur)\n\t    },\n\t    // 返回一个随机的自然数（大于等于 0 的整数）。\n\t    natural: function(min, max) {\n\t        min = typeof min !== 'undefined' ? parseInt(min, 10) : 0\n\t        max = typeof max !== 'undefined' ? parseInt(max, 10) : 9007199254740992 // 2^53\n\t        return Math.round(Math.random() * (max - min)) + min\n\t    },\n\t    // 返回一个随机的整数。\n\t    integer: function(min, max) {\n\t        min = typeof min !== 'undefined' ? parseInt(min, 10) : -9007199254740992\n\t        max = typeof max !== 'undefined' ? parseInt(max, 10) : 9007199254740992 // 2^53\n\t        return Math.round(Math.random() * (max - min)) + min\n\t    },\n\t    int: function(min, max) {\n\t        return this.integer(min, max)\n\t    },\n\t    // 返回一个随机的浮点数。\n\t    float: function(min, max, dmin, dmax) {\n\t        dmin = dmin === undefined ? 0 : dmin\n\t        dmin = Math.max(Math.min(dmin, 17), 0)\n\t        dmax = dmax === undefined ? 17 : dmax\n\t        dmax = Math.max(Math.min(dmax, 17), 0)\n\t        var ret = this.integer(min, max) + '.';\n\t        for (var i = 0, dcount = this.natural(dmin, dmax); i < dcount; i++) {\n\t            ret += (\n\t                // 最后一位不能为 0：如果最后一位为 0，会被 JS 引擎忽略掉。\n\t                (i < dcount - 1) ? this.character('number') : this.character('123456789')\n\t            )\n\t        }\n\t        return parseFloat(ret, 10)\n\t    },\n\t    // 返回一个随机字符。\n\t    character: function(pool) {\n\t        var pools = {\n\t            lower: 'abcdefghijklmnopqrstuvwxyz',\n\t            upper: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',\n\t            number: '0123456789',\n\t            symbol: '!@#$%^&*()[]'\n\t        }\n\t        pools.alpha = pools.lower + pools.upper\n\t        pools['undefined'] = pools.lower + pools.upper + pools.number + pools.symbol\n\n\t        pool = pools[('' + pool).toLowerCase()] || pool\n\t        return pool.charAt(this.natural(0, pool.length - 1))\n\t    },\n\t    char: function(pool) {\n\t        return this.character(pool)\n\t    },\n\t    // 返回一个随机字符串。\n\t    string: function(pool, min, max) {\n\t        var len\n\t        switch (arguments.length) {\n\t            case 0: // ()\n\t                len = this.natural(3, 7)\n\t                break\n\t            case 1: // ( length )\n\t                len = pool\n\t                pool = undefined\n\t                break\n\t            case 2:\n\t                // ( pool, length )\n\t                if (typeof arguments[0] === 'string') {\n\t                    len = min\n\t                } else {\n\t                    // ( min, max )\n\t                    len = this.natural(pool, min)\n\t                    pool = undefined\n\t                }\n\t                break\n\t            case 3:\n\t                len = this.natural(min, max)\n\t                break\n\t        }\n\n\t        var text = ''\n\t        for (var i = 0; i < len; i++) {\n\t            text += this.character(pool)\n\t        }\n\n\t        return text\n\t    },\n\t    str: function( /*pool, min, max*/ ) {\n\t        return this.string.apply(this, arguments)\n\t    },\n\t    // 返回一个整型数组。\n\t    range: function(start, stop, step) {\n\t        // range( stop )\n\t        if (arguments.length <= 1) {\n\t            stop = start || 0;\n\t            start = 0;\n\t        }\n\t        // range( start, stop )\n\t        step = arguments[2] || 1;\n\n\t        start = +start\n\t        stop = +stop\n\t        step = +step\n\n\t        var len = Math.max(Math.ceil((stop - start) / step), 0);\n\t        var idx = 0;\n\t        var range = new Array(len);\n\n\t        while (idx < len) {\n\t            range[idx++] = start;\n\t            start += step;\n\t        }\n\n\t        return range;\n\t    }\n\t}\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Date\n\t*/\n\tvar patternLetters = {\n\t    yyyy: 'getFullYear',\n\t    yy: function(date) {\n\t        return ('' + date.getFullYear()).slice(2)\n\t    },\n\t    y: 'yy',\n\n\t    MM: function(date) {\n\t        var m = date.getMonth() + 1\n\t        return m < 10 ? '0' + m : m\n\t    },\n\t    M: function(date) {\n\t        return date.getMonth() + 1\n\t    },\n\n\t    dd: function(date) {\n\t        var d = date.getDate()\n\t        return d < 10 ? '0' + d : d\n\t    },\n\t    d: 'getDate',\n\n\t    HH: function(date) {\n\t        var h = date.getHours()\n\t        return h < 10 ? '0' + h : h\n\t    },\n\t    H: 'getHours',\n\t    hh: function(date) {\n\t        var h = date.getHours() % 12\n\t        return h < 10 ? '0' + h : h\n\t    },\n\t    h: function(date) {\n\t        return date.getHours() % 12\n\t    },\n\n\t    mm: function(date) {\n\t        var m = date.getMinutes()\n\t        return m < 10 ? '0' + m : m\n\t    },\n\t    m: 'getMinutes',\n\n\t    ss: function(date) {\n\t        var s = date.getSeconds()\n\t        return s < 10 ? '0' + s : s\n\t    },\n\t    s: 'getSeconds',\n\n\t    SS: function(date) {\n\t        var ms = date.getMilliseconds()\n\t        return ms < 10 && '00' + ms || ms < 100 && '0' + ms || ms\n\t    },\n\t    S: 'getMilliseconds',\n\n\t    A: function(date) {\n\t        return date.getHours() < 12 ? 'AM' : 'PM'\n\t    },\n\t    a: function(date) {\n\t        return date.getHours() < 12 ? 'am' : 'pm'\n\t    },\n\t    T: 'getTime'\n\t}\n\tmodule.exports = {\n\t    // 日期占位符集合。\n\t    _patternLetters: patternLetters,\n\t    // 日期占位符正则。\n\t    _rformat: new RegExp((function() {\n\t        var re = []\n\t        for (var i in patternLetters) re.push(i)\n\t        return '(' + re.join('|') + ')'\n\t    })(), 'g'),\n\t    // 格式化日期。\n\t    _formatDate: function(date, format) {\n\t        return format.replace(this._rformat, function creatNewSubString($0, flag) {\n\t            return typeof patternLetters[flag] === 'function' ? patternLetters[flag](date) :\n\t                patternLetters[flag] in patternLetters ? creatNewSubString($0, patternLetters[flag]) :\n\t                date[patternLetters[flag]]()\n\t        })\n\t    },\n\t    // 生成一个随机的 Date 对象。\n\t    _randomDate: function(min, max) { // min, max\n\t        min = min === undefined ? new Date(0) : min\n\t        max = max === undefined ? new Date() : max\n\t        return new Date(Math.random() * (max.getTime() - min.getTime()))\n\t    },\n\t    // 返回一个随机的日期字符串。\n\t    date: function(format) {\n\t        format = format || 'yyyy-MM-dd'\n\t        return this._formatDate(this._randomDate(), format)\n\t    },\n\t    // 返回一个随机的时间字符串。\n\t    time: function(format) {\n\t        format = format || 'HH:mm:ss'\n\t        return this._formatDate(this._randomDate(), format)\n\t    },\n\t    // 返回一个随机的日期和时间字符串。\n\t    datetime: function(format) {\n\t        format = format || 'yyyy-MM-dd HH:mm:ss'\n\t        return this._formatDate(this._randomDate(), format)\n\t    },\n\t    // 返回当前的日期和时间字符串。\n\t    now: function(unit, format) {\n\t        // now(unit) now(format)\n\t        if (arguments.length === 1) {\n\t            // now(format)\n\t            if (!/year|month|day|hour|minute|second|week/.test(unit)) {\n\t                format = unit\n\t                unit = ''\n\t            }\n\t        }\n\t        unit = (unit || '').toLowerCase()\n\t        format = format || 'yyyy-MM-dd HH:mm:ss'\n\n\t        var date = new Date()\n\n\t        /* jshint -W086 */\n\t        // 参考自 http://momentjs.cn/docs/#/manipulating/start-of/\n\t        switch (unit) {\n\t            case 'year':\n\t                date.setMonth(0)\n\t            case 'month':\n\t                date.setDate(1)\n\t            case 'week':\n\t            case 'day':\n\t                date.setHours(0)\n\t            case 'hour':\n\t                date.setMinutes(0)\n\t            case 'minute':\n\t                date.setSeconds(0)\n\t            case 'second':\n\t                date.setMilliseconds(0)\n\t        }\n\t        switch (unit) {\n\t            case 'week':\n\t                date.setDate(date.getDate() - date.getDay())\n\t        }\n\n\t        return this._formatDate(date, format)\n\t    }\n\t}\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(module) {/* global document  */\n\t/*\n\t    ## Image\n\t*/\n\tmodule.exports = {\n\t    // 常见的广告宽高\n\t    _adSize: [\n\t        '300x250', '250x250', '240x400', '336x280', '180x150',\n\t        '720x300', '468x60', '234x60', '88x31', '120x90',\n\t        '120x60', '120x240', '125x125', '728x90', '160x600',\n\t        '120x600', '300x600'\n\t    ],\n\t    // 常见的屏幕宽高\n\t    _screenSize: [\n\t        '320x200', '320x240', '640x480', '800x480', '800x480',\n\t        '1024x600', '1024x768', '1280x800', '1440x900', '1920x1200',\n\t        '2560x1600'\n\t    ],\n\t    // 常见的视频宽高\n\t    _videoSize: ['720x480', '768x576', '1280x720', '1920x1080'],\n\t    /*\n\t        生成一个随机的图片地址。\n\n\t        替代图片源\n\t            http://fpoimg.com/\n\t        参考自 \n\t            http://rensanning.iteye.com/blog/1933310\n\t            http://code.tutsplus.com/articles/the-top-8-placeholders-for-web-designers--net-19485\n\t    */\n\t    image: function(size, background, foreground, format, text) {\n\t        // Random.image( size, background, foreground, text )\n\t        if (arguments.length === 4) {\n\t            text = format\n\t            format = undefined\n\t        }\n\t        // Random.image( size, background, text )\n\t        if (arguments.length === 3) {\n\t            text = foreground\n\t            foreground = undefined\n\t        }\n\t        // Random.image()\n\t        if (!size) size = this.pick(this._adSize)\n\n\t        if (background && ~background.indexOf('#')) background = background.slice(1)\n\t        if (foreground && ~foreground.indexOf('#')) foreground = foreground.slice(1)\n\n\t        // http://dummyimage.com/600x400/cc00cc/470047.png&text=hello\n\t        return 'http://dummyimage.com/' + size +\n\t            (background ? '/' + background : '') +\n\t            (foreground ? '/' + foreground : '') +\n\t            (format ? '.' + format : '') +\n\t            (text ? '&text=' + text : '')\n\t    },\n\t    img: function() {\n\t        return this.image.apply(this, arguments)\n\t    },\n\n\t    /*\n\t        BrandColors\n\t        http://brandcolors.net/\n\t        A collection of major brand color codes curated by Galen Gidman.\n\t        大牌公司的颜色集合\n\n\t        // 获取品牌和颜色\n\t        $('h2').each(function(index, item){\n\t            item = $(item)\n\t            console.log('\\'' + item.text() + '\\'', ':', '\\'' + item.next().text() + '\\'', ',')\n\t        })\n\t    */\n\t    _brandColors: {\n\t        '4ormat': '#fb0a2a',\n\t        '500px': '#02adea',\n\t        'About.me (blue)': '#00405d',\n\t        'About.me (yellow)': '#ffcc33',\n\t        'Addvocate': '#ff6138',\n\t        'Adobe': '#ff0000',\n\t        'Aim': '#fcd20b',\n\t        'Amazon': '#e47911',\n\t        'Android': '#a4c639',\n\t        'Angie\\'s List': '#7fbb00',\n\t        'AOL': '#0060a3',\n\t        'Atlassian': '#003366',\n\t        'Behance': '#053eff',\n\t        'Big Cartel': '#97b538',\n\t        'bitly': '#ee6123',\n\t        'Blogger': '#fc4f08',\n\t        'Boeing': '#0039a6',\n\t        'Booking.com': '#003580',\n\t        'Carbonmade': '#613854',\n\t        'Cheddar': '#ff7243',\n\t        'Code School': '#3d4944',\n\t        'Delicious': '#205cc0',\n\t        'Dell': '#3287c1',\n\t        'Designmoo': '#e54a4f',\n\t        'Deviantart': '#4e6252',\n\t        'Designer News': '#2d72da',\n\t        'Devour': '#fd0001',\n\t        'DEWALT': '#febd17',\n\t        'Disqus (blue)': '#59a3fc',\n\t        'Disqus (orange)': '#db7132',\n\t        'Dribbble': '#ea4c89',\n\t        'Dropbox': '#3d9ae8',\n\t        'Drupal': '#0c76ab',\n\t        'Dunked': '#2a323a',\n\t        'eBay': '#89c507',\n\t        'Ember': '#f05e1b',\n\t        'Engadget': '#00bdf6',\n\t        'Envato': '#528036',\n\t        'Etsy': '#eb6d20',\n\t        'Evernote': '#5ba525',\n\t        'Fab.com': '#dd0017',\n\t        'Facebook': '#3b5998',\n\t        'Firefox': '#e66000',\n\t        'Flickr (blue)': '#0063dc',\n\t        'Flickr (pink)': '#ff0084',\n\t        'Forrst': '#5b9a68',\n\t        'Foursquare': '#25a0ca',\n\t        'Garmin': '#007cc3',\n\t        'GetGlue': '#2d75a2',\n\t        'Gimmebar': '#f70078',\n\t        'GitHub': '#171515',\n\t        'Google Blue': '#0140ca',\n\t        'Google Green': '#16a61e',\n\t        'Google Red': '#dd1812',\n\t        'Google Yellow': '#fcca03',\n\t        'Google+': '#dd4b39',\n\t        'Grooveshark': '#f77f00',\n\t        'Groupon': '#82b548',\n\t        'Hacker News': '#ff6600',\n\t        'HelloWallet': '#0085ca',\n\t        'Heroku (light)': '#c7c5e6',\n\t        'Heroku (dark)': '#6567a5',\n\t        'HootSuite': '#003366',\n\t        'Houzz': '#73ba37',\n\t        'HTML5': '#ec6231',\n\t        'IKEA': '#ffcc33',\n\t        'IMDb': '#f3ce13',\n\t        'Instagram': '#3f729b',\n\t        'Intel': '#0071c5',\n\t        'Intuit': '#365ebf',\n\t        'Kickstarter': '#76cc1e',\n\t        'kippt': '#e03500',\n\t        'Kodery': '#00af81',\n\t        'LastFM': '#c3000d',\n\t        'LinkedIn': '#0e76a8',\n\t        'Livestream': '#cf0005',\n\t        'Lumo': '#576396',\n\t        'Mixpanel': '#a086d3',\n\t        'Meetup': '#e51937',\n\t        'Nokia': '#183693',\n\t        'NVIDIA': '#76b900',\n\t        'Opera': '#cc0f16',\n\t        'Path': '#e41f11',\n\t        'PayPal (dark)': '#1e477a',\n\t        'PayPal (light)': '#3b7bbf',\n\t        'Pinboard': '#0000e6',\n\t        'Pinterest': '#c8232c',\n\t        'PlayStation': '#665cbe',\n\t        'Pocket': '#ee4056',\n\t        'Prezi': '#318bff',\n\t        'Pusha': '#0f71b4',\n\t        'Quora': '#a82400',\n\t        'QUOTE.fm': '#66ceff',\n\t        'Rdio': '#008fd5',\n\t        'Readability': '#9c0000',\n\t        'Red Hat': '#cc0000',\n\t        'Resource': '#7eb400',\n\t        'Rockpack': '#0ba6ab',\n\t        'Roon': '#62b0d9',\n\t        'RSS': '#ee802f',\n\t        'Salesforce': '#1798c1',\n\t        'Samsung': '#0c4da2',\n\t        'Shopify': '#96bf48',\n\t        'Skype': '#00aff0',\n\t        'Snagajob': '#f47a20',\n\t        'Softonic': '#008ace',\n\t        'SoundCloud': '#ff7700',\n\t        'Space Box': '#f86960',\n\t        'Spotify': '#81b71a',\n\t        'Sprint': '#fee100',\n\t        'Squarespace': '#121212',\n\t        'StackOverflow': '#ef8236',\n\t        'Staples': '#cc0000',\n\t        'Status Chart': '#d7584f',\n\t        'Stripe': '#008cdd',\n\t        'StudyBlue': '#00afe1',\n\t        'StumbleUpon': '#f74425',\n\t        'T-Mobile': '#ea0a8e',\n\t        'Technorati': '#40a800',\n\t        'The Next Web': '#ef4423',\n\t        'Treehouse': '#5cb868',\n\t        'Trulia': '#5eab1f',\n\t        'Tumblr': '#34526f',\n\t        'Twitch.tv': '#6441a5',\n\t        'Twitter': '#00acee',\n\t        'TYPO3': '#ff8700',\n\t        'Ubuntu': '#dd4814',\n\t        'Ustream': '#3388ff',\n\t        'Verizon': '#ef1d1d',\n\t        'Vimeo': '#86c9ef',\n\t        'Vine': '#00a478',\n\t        'Virb': '#06afd8',\n\t        'Virgin Media': '#cc0000',\n\t        'Wooga': '#5b009c',\n\t        'WordPress (blue)': '#21759b',\n\t        'WordPress (orange)': '#d54e21',\n\t        'WordPress (grey)': '#464646',\n\t        'Wunderlist': '#2b88d9',\n\t        'XBOX': '#9bc848',\n\t        'XING': '#126567',\n\t        'Yahoo!': '#720e9e',\n\t        'Yandex': '#ffcc00',\n\t        'Yelp': '#c41200',\n\t        'YouTube': '#c4302b',\n\t        'Zalongo': '#5498dc',\n\t        'Zendesk': '#78a300',\n\t        'Zerply': '#9dcc7a',\n\t        'Zootool': '#5e8b1d'\n\t    },\n\t    _brandNames: function() {\n\t        var brands = [];\n\t        for (var b in this._brandColors) {\n\t            brands.push(b)\n\t        }\n\t        return brands\n\t    },\n\t    /*\n\t        生成一段随机的 Base64 图片编码。\n\n\t        https://github.com/imsky/holder\n\t        Holder renders image placeholders entirely on the client side.\n\n\t        dataImageHolder: function(size) {\n\t            return 'holder.js/' + size\n\t        },\n\t    */\n\t    dataImage: function(size, text) {\n\t        var canvas\n\t        if (typeof document !== 'undefined') {\n\t            canvas = document.createElement('canvas')\n\t        } else {\n\t            /*\n\t                https://github.com/Automattic/node-canvas\n\t                    npm install canvas --save\n\t                安装问题：\n\t                * http://stackoverflow.com/questions/22953206/gulp-issues-with-cario-install-command-not-found-when-trying-to-installing-canva\n\t                * https://github.com/Automattic/node-canvas/issues/415\n\t                * https://github.com/Automattic/node-canvas/wiki/_pages\n\n\t                PS：node-canvas 的安装过程实在是太繁琐了，所以不放入 package.json 的 dependencies。\n\t             */\n\t            var Canvas = module.require('canvas')\n\t            canvas = new Canvas()\n\t        }\n\n\t        var ctx = canvas && canvas.getContext && canvas.getContext(\"2d\")\n\t        if (!canvas || !ctx) return ''\n\n\t        if (!size) size = this.pick(this._adSize)\n\t        text = text !== undefined ? text : size\n\n\t        size = size.split('x')\n\n\t        var width = parseInt(size[0], 10),\n\t            height = parseInt(size[1], 10),\n\t            background = this._brandColors[this.pick(this._brandNames())],\n\t            foreground = '#FFF',\n\t            text_height = 14,\n\t            font = 'sans-serif';\n\n\t        canvas.width = width\n\t        canvas.height = height\n\t        ctx.textAlign = 'center'\n\t        ctx.textBaseline = 'middle'\n\t        ctx.fillStyle = background\n\t        ctx.fillRect(0, 0, width, height)\n\t        ctx.fillStyle = foreground\n\t        ctx.font = 'bold ' + text_height + 'px ' + font\n\t        ctx.fillText(text, (width / 2), (height / 2), width)\n\t        return canvas.toDataURL('image/png')\n\t    }\n\t}\n\t/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(9)(module)))\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = function(module) {\r\n\t\tif(!module.webpackPolyfill) {\r\n\t\t\tmodule.deprecate = function() {};\r\n\t\t\tmodule.paths = [];\r\n\t\t\t// module.parent = undefined by default\r\n\t\t\tmodule.children = [];\r\n\t\t\tmodule.webpackPolyfill = 1;\r\n\t\t}\r\n\t\treturn module;\r\n\t}\r\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## Color\n\n\t    http://llllll.li/randomColor/\n\t        A color generator for JavaScript.\n\t        randomColor generates attractive colors by default. More specifically, randomColor produces bright colors with a reasonably high saturation. This makes randomColor particularly useful for data visualizations and generative art.\n\n\t    http://randomcolour.com/\n\t        var bg_colour = Math.floor(Math.random() * 16777215).toString(16);\n\t        bg_colour = \"#\" + (\"000000\" + bg_colour).slice(-6);\n\t        document.bgColor = bg_colour;\n\t    \n\t    http://martin.ankerl.com/2009/12/09/how-to-create-random-colors-programmatically/\n\t        Creating random colors is actually more difficult than it seems. The randomness itself is easy, but aesthetically pleasing randomness is more difficult.\n\t        https://github.com/devongovett/color-generator\n\n\t    http://www.paulirish.com/2009/random-hex-color-code-snippets/\n\t        Random Hex Color Code Generator in JavaScript\n\n\t    http://chancejs.com/#color\n\t        chance.color()\n\t        // => '#79c157'\n\t        chance.color({format: 'hex'})\n\t        // => '#d67118'\n\t        chance.color({format: 'shorthex'})\n\t        // => '#60f'\n\t        chance.color({format: 'rgb'})\n\t        // => 'rgb(110,52,164)'\n\n\t    http://tool.c7sky.com/webcolor\n\t        网页设计常用色彩搭配表\n\t    \n\t    https://github.com/One-com/one-color\n\t        An OO-based JavaScript color parser/computation toolkit with support for RGB, HSV, HSL, CMYK, and alpha channels.\n\t        API 很赞\n\n\t    https://github.com/harthur/color\n\t        JavaScript color conversion and manipulation library\n\n\t    https://github.com/leaverou/css-colors\n\t        Share & convert CSS colors\n\t    http://leaverou.github.io/css-colors/#slategray\n\t        Type a CSS color keyword, #hex, hsl(), rgba(), whatever:\n\n\t    色调 hue\n\t        http://baike.baidu.com/view/23368.htm\n\t        色调指的是一幅画中画面色彩的总体倾向，是大的色彩效果。\n\t    饱和度 saturation\n\t        http://baike.baidu.com/view/189644.htm\n\t        饱和度是指色彩的鲜艳程度，也称色彩的纯度。饱和度取决于该色中含色成分和消色成分（灰色）的比例。含色成分越大，饱和度越大；消色成分越大，饱和度越小。\n\t    亮度 brightness\n\t        http://baike.baidu.com/view/34773.htm\n\t        亮度是指发光体（反光体）表面发光（反光）强弱的物理量。\n\t    照度 luminosity\n\t        物体被照亮的程度,采用单位面积所接受的光通量来表示,表示单位为勒[克斯](Lux,lx) ,即 1m / m2 。\n\n\t    http://stackoverflow.com/questions/1484506/random-color-generator-in-javascript\n\t        var letters = '0123456789ABCDEF'.split('')\n\t        var color = '#'\n\t        for (var i = 0; i < 6; i++) {\n\t            color += letters[Math.floor(Math.random() * 16)]\n\t        }\n\t        return color\n\t    \n\t        // 随机生成一个无脑的颜色，格式为 '#RRGGBB'。\n\t        // _brainlessColor()\n\t        var color = Math.floor(\n\t            Math.random() *\n\t            (16 * 16 * 16 * 16 * 16 * 16 - 1)\n\t        ).toString(16)\n\t        color = \"#\" + (\"000000\" + color).slice(-6)\n\t        return color.toUpperCase()\n\t*/\n\n\tvar Convert = __webpack_require__(11)\n\tvar DICT = __webpack_require__(12)\n\n\tmodule.exports = {\n\t    // 随机生成一个有吸引力的颜色，格式为 '#RRGGBB'。\n\t    color: function(name) {\n\t        if (name || DICT[name]) return DICT[name].nicer\n\t        return this.hex()\n\t    },\n\t    // #DAC0DE\n\t    hex: function() {\n\t        var hsv = this._goldenRatioColor()\n\t        var rgb = Convert.hsv2rgb(hsv)\n\t        var hex = Convert.rgb2hex(rgb[0], rgb[1], rgb[2])\n\t        return hex\n\t    },\n\t    // rgb(128,255,255)\n\t    rgb: function() {\n\t        var hsv = this._goldenRatioColor()\n\t        var rgb = Convert.hsv2rgb(hsv)\n\t        return 'rgb(' +\n\t            parseInt(rgb[0], 10) + ', ' +\n\t            parseInt(rgb[1], 10) + ', ' +\n\t            parseInt(rgb[2], 10) + ')'\n\t    },\n\t    // rgba(128,255,255,0.3)\n\t    rgba: function() {\n\t        var hsv = this._goldenRatioColor()\n\t        var rgb = Convert.hsv2rgb(hsv)\n\t        return 'rgba(' +\n\t            parseInt(rgb[0], 10) + ', ' +\n\t            parseInt(rgb[1], 10) + ', ' +\n\t            parseInt(rgb[2], 10) + ', ' +\n\t            Math.random().toFixed(2) + ')'\n\t    },\n\t    // hsl(300,80%,90%)\n\t    hsl: function() {\n\t        var hsv = this._goldenRatioColor()\n\t        var hsl = Convert.hsv2hsl(hsv)\n\t        return 'hsl(' +\n\t            parseInt(hsl[0], 10) + ', ' +\n\t            parseInt(hsl[1], 10) + ', ' +\n\t            parseInt(hsl[2], 10) + ')'\n\t    },\n\t    // http://martin.ankerl.com/2009/12/09/how-to-create-random-colors-programmatically/\n\t    // https://github.com/devongovett/color-generator/blob/master/index.js\n\t    // 随机生成一个有吸引力的颜色。\n\t    _goldenRatioColor: function(saturation, value) {\n\t        this._goldenRatio = 0.618033988749895\n\t        this._hue = this._hue || Math.random()\n\t        this._hue += this._goldenRatio\n\t        this._hue %= 1\n\n\t        if (typeof saturation !== \"number\") saturation = 0.5;\n\t        if (typeof value !== \"number\") value = 0.95;\n\n\t        return [\n\t            this._hue * 360,\n\t            saturation * 100,\n\t            value * 100\n\t        ]\n\t    }\n\t}\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Color Convert\n\n\t    http://blog.csdn.net/idfaya/article/details/6770414\n\t        颜色空间RGB与HSV(HSL)的转换\n\t*/\n\t// https://github.com/harthur/color-convert/blob/master/conversions.js\n\tmodule.exports = {\n\t\trgb2hsl: function rgb2hsl(rgb) {\n\t\t\tvar r = rgb[0] / 255,\n\t\t\t\tg = rgb[1] / 255,\n\t\t\t\tb = rgb[2] / 255,\n\t\t\t\tmin = Math.min(r, g, b),\n\t\t\t\tmax = Math.max(r, g, b),\n\t\t\t\tdelta = max - min,\n\t\t\t\th, s, l;\n\n\t\t\tif (max == min)\n\t\t\t\th = 0;\n\t\t\telse if (r == max)\n\t\t\t\th = (g - b) / delta;\n\t\t\telse if (g == max)\n\t\t\t\th = 2 + (b - r) / delta;\n\t\t\telse if (b == max)\n\t\t\t\th = 4 + (r - g) / delta;\n\n\t\t\th = Math.min(h * 60, 360);\n\n\t\t\tif (h < 0)\n\t\t\t\th += 360;\n\n\t\t\tl = (min + max) / 2;\n\n\t\t\tif (max == min)\n\t\t\t\ts = 0;\n\t\t\telse if (l <= 0.5)\n\t\t\t\ts = delta / (max + min);\n\t\t\telse\n\t\t\t\ts = delta / (2 - max - min);\n\n\t\t\treturn [h, s * 100, l * 100];\n\t\t},\n\t\trgb2hsv: function rgb2hsv(rgb) {\n\t\t\tvar r = rgb[0],\n\t\t\t\tg = rgb[1],\n\t\t\t\tb = rgb[2],\n\t\t\t\tmin = Math.min(r, g, b),\n\t\t\t\tmax = Math.max(r, g, b),\n\t\t\t\tdelta = max - min,\n\t\t\t\th, s, v;\n\n\t\t\tif (max === 0)\n\t\t\t\ts = 0;\n\t\t\telse\n\t\t\t\ts = (delta / max * 1000) / 10;\n\n\t\t\tif (max == min)\n\t\t\t\th = 0;\n\t\t\telse if (r == max)\n\t\t\t\th = (g - b) / delta;\n\t\t\telse if (g == max)\n\t\t\t\th = 2 + (b - r) / delta;\n\t\t\telse if (b == max)\n\t\t\t\th = 4 + (r - g) / delta;\n\n\t\t\th = Math.min(h * 60, 360);\n\n\t\t\tif (h < 0)\n\t\t\t\th += 360;\n\n\t\t\tv = ((max / 255) * 1000) / 10;\n\n\t\t\treturn [h, s, v];\n\t\t},\n\t\thsl2rgb: function hsl2rgb(hsl) {\n\t\t\tvar h = hsl[0] / 360,\n\t\t\t\ts = hsl[1] / 100,\n\t\t\t\tl = hsl[2] / 100,\n\t\t\t\tt1, t2, t3, rgb, val;\n\n\t\t\tif (s === 0) {\n\t\t\t\tval = l * 255;\n\t\t\t\treturn [val, val, val];\n\t\t\t}\n\n\t\t\tif (l < 0.5)\n\t\t\t\tt2 = l * (1 + s);\n\t\t\telse\n\t\t\t\tt2 = l + s - l * s;\n\t\t\tt1 = 2 * l - t2;\n\n\t\t\trgb = [0, 0, 0];\n\t\t\tfor (var i = 0; i < 3; i++) {\n\t\t\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\t\t\tif (t3 < 0) t3++;\n\t\t\t\tif (t3 > 1) t3--;\n\n\t\t\t\tif (6 * t3 < 1)\n\t\t\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t\t\telse if (2 * t3 < 1)\n\t\t\t\t\tval = t2;\n\t\t\t\telse if (3 * t3 < 2)\n\t\t\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t\t\telse\n\t\t\t\t\tval = t1;\n\n\t\t\t\trgb[i] = val * 255;\n\t\t\t}\n\n\t\t\treturn rgb;\n\t\t},\n\t\thsl2hsv: function hsl2hsv(hsl) {\n\t\t\tvar h = hsl[0],\n\t\t\t\ts = hsl[1] / 100,\n\t\t\t\tl = hsl[2] / 100,\n\t\t\t\tsv, v;\n\t\t\tl *= 2;\n\t\t\ts *= (l <= 1) ? l : 2 - l;\n\t\t\tv = (l + s) / 2;\n\t\t\tsv = (2 * s) / (l + s);\n\t\t\treturn [h, sv * 100, v * 100];\n\t\t},\n\t\thsv2rgb: function hsv2rgb(hsv) {\n\t\t\tvar h = hsv[0] / 60\n\t\t\tvar s = hsv[1] / 100\n\t\t\tvar v = hsv[2] / 100\n\t\t\tvar hi = Math.floor(h) % 6\n\n\t\t\tvar f = h - Math.floor(h)\n\t\t\tvar p = 255 * v * (1 - s)\n\t\t\tvar q = 255 * v * (1 - (s * f))\n\t\t\tvar t = 255 * v * (1 - (s * (1 - f)))\n\n\t\t\tv = 255 * v\n\n\t\t\tswitch (hi) {\n\t\t\t\tcase 0:\n\t\t\t\t\treturn [v, t, p]\n\t\t\t\tcase 1:\n\t\t\t\t\treturn [q, v, p]\n\t\t\t\tcase 2:\n\t\t\t\t\treturn [p, v, t]\n\t\t\t\tcase 3:\n\t\t\t\t\treturn [p, q, v]\n\t\t\t\tcase 4:\n\t\t\t\t\treturn [t, p, v]\n\t\t\t\tcase 5:\n\t\t\t\t\treturn [v, p, q]\n\t\t\t}\n\t\t},\n\t\thsv2hsl: function hsv2hsl(hsv) {\n\t\t\tvar h = hsv[0],\n\t\t\t\ts = hsv[1] / 100,\n\t\t\t\tv = hsv[2] / 100,\n\t\t\t\tsl, l;\n\n\t\t\tl = (2 - s) * v;\n\t\t\tsl = s * v;\n\t\t\tsl /= (l <= 1) ? l : 2 - l;\n\t\t\tl /= 2;\n\t\t\treturn [h, sl * 100, l * 100];\n\t\t},\n\t\t// http://www.140byt.es/keywords/color\n\t\trgb2hex: function(\n\t\t\ta, // red, as a number from 0 to 255\n\t\t\tb, // green, as a number from 0 to 255\n\t\t\tc // blue, as a number from 0 to 255\n\t\t) {\n\t\t\treturn \"#\" + ((256 + a << 8 | b) << 8 | c).toString(16).slice(1)\n\t\t},\n\t\thex2rgb: function(\n\t\t\ta // take a \"#xxxxxx\" hex string,\n\t\t) {\n\t\t\ta = '0x' + a.slice(1).replace(a.length > 4 ? a : /./g, '$&$&') | 0;\n\t\t\treturn [a >> 16, a >> 8 & 255, a & 255]\n\t\t}\n\t}\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Color 字典数据\n\n\t    字典数据来源 [A nicer color palette for the web](http://clrs.cc/)\n\t*/\n\tmodule.exports = {\n\t    // name value nicer\n\t    navy: {\n\t        value: '#000080',\n\t        nicer: '#001F3F'\n\t    },\n\t    blue: {\n\t        value: '#0000ff',\n\t        nicer: '#0074D9'\n\t    },\n\t    aqua: {\n\t        value: '#00ffff',\n\t        nicer: '#7FDBFF'\n\t    },\n\t    teal: {\n\t        value: '#008080',\n\t        nicer: '#39CCCC'\n\t    },\n\t    olive: {\n\t        value: '#008000',\n\t        nicer: '#3D9970'\n\t    },\n\t    green: {\n\t        value: '#008000',\n\t        nicer: '#2ECC40'\n\t    },\n\t    lime: {\n\t        value: '#00ff00',\n\t        nicer: '#01FF70'\n\t    },\n\t    yellow: {\n\t        value: '#ffff00',\n\t        nicer: '#FFDC00'\n\t    },\n\t    orange: {\n\t        value: '#ffa500',\n\t        nicer: '#FF851B'\n\t    },\n\t    red: {\n\t        value: '#ff0000',\n\t        nicer: '#FF4136'\n\t    },\n\t    maroon: {\n\t        value: '#800000',\n\t        nicer: '#85144B'\n\t    },\n\t    fuchsia: {\n\t        value: '#ff00ff',\n\t        nicer: '#F012BE'\n\t    },\n\t    purple: {\n\t        value: '#800080',\n\t        nicer: '#B10DC9'\n\t    },\n\t    silver: {\n\t        value: '#c0c0c0',\n\t        nicer: '#DDDDDD'\n\t    },\n\t    gray: {\n\t        value: '#808080',\n\t        nicer: '#AAAAAA'\n\t    },\n\t    black: {\n\t        value: '#000000',\n\t        nicer: '#111111'\n\t    },\n\t    white: {\n\t        value: '#FFFFFF',\n\t        nicer: '#FFFFFF'\n\t    }\n\t}\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## Text\n\n\t    http://www.lipsum.com/\n\t*/\n\tvar Basic = __webpack_require__(6)\n\tvar Helper = __webpack_require__(14)\n\n\tfunction range(defaultMin, defaultMax, min, max) {\n\t    return min === undefined ? Basic.natural(defaultMin, defaultMax) : // ()\n\t        max === undefined ? min : // ( len )\n\t        Basic.natural(parseInt(min, 10), parseInt(max, 10)) // ( min, max )\n\t}\n\n\tmodule.exports = {\n\t    // 随机生成一段文本。\n\t    paragraph: function(min, max) {\n\t        var len = range(3, 7, min, max)\n\t        var result = []\n\t        for (var i = 0; i < len; i++) {\n\t            result.push(this.sentence())\n\t        }\n\t        return result.join(' ')\n\t    },\n\t    // \n\t    cparagraph: function(min, max) {\n\t        var len = range(3, 7, min, max)\n\t        var result = []\n\t        for (var i = 0; i < len; i++) {\n\t            result.push(this.csentence())\n\t        }\n\t        return result.join('')\n\t    },\n\t    // 随机生成一个句子，第一个单词的首字母大写。\n\t    sentence: function(min, max) {\n\t        var len = range(12, 18, min, max)\n\t        var result = []\n\t        for (var i = 0; i < len; i++) {\n\t            result.push(this.word())\n\t        }\n\t        return Helper.capitalize(result.join(' ')) + '.'\n\t    },\n\t    // 随机生成一个中文句子。\n\t    csentence: function(min, max) {\n\t        var len = range(12, 18, min, max)\n\t        var result = []\n\t        for (var i = 0; i < len; i++) {\n\t            result.push(this.cword())\n\t        }\n\n\t        return result.join('') + '。'\n\t    },\n\t    // 随机生成一个单词。\n\t    word: function(min, max) {\n\t        var len = range(3, 10, min, max)\n\t        var result = '';\n\t        for (var i = 0; i < len; i++) {\n\t            result += Basic.character('lower')\n\t        }\n\t        return result\n\t    },\n\t    // 随机生成一个或多个汉字。\n\t    cword: function(pool, min, max) {\n\t        // 最常用的 500 个汉字 http://baike.baidu.com/view/568436.htm\n\t        var DICT_KANZI = '的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞'\n\n\t        var len\n\t        switch (arguments.length) {\n\t            case 0: // ()\n\t                pool = DICT_KANZI\n\t                len = 1\n\t                break\n\t            case 1: // ( pool )\n\t                if (typeof arguments[0] === 'string') {\n\t                    len = 1\n\t                } else {\n\t                    // ( length )\n\t                    len = pool\n\t                    pool = DICT_KANZI\n\t                }\n\t                break\n\t            case 2:\n\t                // ( pool, length )\n\t                if (typeof arguments[0] === 'string') {\n\t                    len = min\n\t                } else {\n\t                    // ( min, max )\n\t                    len = this.natural(pool, min)\n\t                    pool = DICT_KANZI\n\t                }\n\t                break\n\t            case 3:\n\t                len = this.natural(min, max)\n\t                break\n\t        }\n\n\t        var result = ''\n\t        for (var i = 0; i < len; i++) {\n\t            result += pool.charAt(this.natural(0, pool.length - 1))\n\t        }\n\t        return result\n\t    },\n\t    // 随机生成一句标题，其中每个单词的首字母大写。\n\t    title: function(min, max) {\n\t        var len = range(3, 7, min, max)\n\t        var result = []\n\t        for (var i = 0; i < len; i++) {\n\t            result.push(this.capitalize(this.word()))\n\t        }\n\t        return result.join(' ')\n\t    },\n\t    // 随机生成一句中文标题。\n\t    ctitle: function(min, max) {\n\t        var len = range(3, 7, min, max)\n\t        var result = []\n\t        for (var i = 0; i < len; i++) {\n\t            result.push(this.cword())\n\t        }\n\t        return result.join('')\n\t    }\n\t}\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## Helpers\n\t*/\n\n\tvar Util = __webpack_require__(3)\n\n\tmodule.exports = {\n\t\t// 把字符串的第一个字母转换为大写。\n\t\tcapitalize: function(word) {\n\t\t\treturn (word + '').charAt(0).toUpperCase() + (word + '').substr(1)\n\t\t},\n\t\t// 把字符串转换为大写。\n\t\tupper: function(str) {\n\t\t\treturn (str + '').toUpperCase()\n\t\t},\n\t\t// 把字符串转换为小写。\n\t\tlower: function(str) {\n\t\t\treturn (str + '').toLowerCase()\n\t\t},\n\t\t// 从数组中随机选取一个元素，并返回。\n\t\tpick: function pick(arr, min, max) {\n\t\t\t// pick( item1, item2 ... )\n\t\t\tif (!Util.isArray(arr)) {\n\t\t\t\tarr = [].slice.call(arguments)\n\t\t\t\tmin = 1\n\t\t\t\tmax = 1\n\t\t\t} else {\n\t\t\t\t// pick( [ item1, item2 ... ] )\n\t\t\t\tif (min === undefined) min = 1\n\n\t\t\t\t// pick( [ item1, item2 ... ], count )\n\t\t\t\tif (max === undefined) max = min\n\t\t\t}\n\n\t\t\tif (min === 1 && max === 1) return arr[this.natural(0, arr.length - 1)]\n\n\t\t\t// pick( [ item1, item2 ... ], min, max )\n\t\t\treturn this.shuffle(arr, min, max)\n\n\t\t\t// 通过参数个数判断方法签名，扩展性太差！#90\n\t\t\t// switch (arguments.length) {\n\t\t\t// \tcase 1:\n\t\t\t// \t\t// pick( [ item1, item2 ... ] )\n\t\t\t// \t\treturn arr[this.natural(0, arr.length - 1)]\n\t\t\t// \tcase 2:\n\t\t\t// \t\t// pick( [ item1, item2 ... ], count )\n\t\t\t// \t\tmax = min\n\t\t\t// \t\t\t/* falls through */\n\t\t\t// \tcase 3:\n\t\t\t// \t\t// pick( [ item1, item2 ... ], min, max )\n\t\t\t// \t\treturn this.shuffle(arr, min, max)\n\t\t\t// }\n\t\t},\n\t\t/*\n\t\t    打乱数组中元素的顺序，并返回。\n\t\t    Given an array, scramble the order and return it.\n\n\t\t    其他的实现思路：\n\t\t        // https://code.google.com/p/jslibs/wiki/JavascriptTips\n\t\t        result = result.sort(function() {\n\t\t            return Math.random() - 0.5\n\t\t        })\n\t\t*/\n\t\tshuffle: function shuffle(arr, min, max) {\n\t\t\tarr = arr || []\n\t\t\tvar old = arr.slice(0),\n\t\t\t\tresult = [],\n\t\t\t\tindex = 0,\n\t\t\t\tlength = old.length;\n\t\t\tfor (var i = 0; i < length; i++) {\n\t\t\t\tindex = this.natural(0, old.length - 1)\n\t\t\t\tresult.push(old[index])\n\t\t\t\told.splice(index, 1)\n\t\t\t}\n\t\t\tswitch (arguments.length) {\n\t\t\t\tcase 0:\n\t\t\t\tcase 1:\n\t\t\t\t\treturn result\n\t\t\t\tcase 2:\n\t\t\t\t\tmax = min\n\t\t\t\t\t\t/* falls through */\n\t\t\t\tcase 3:\n\t\t\t\t\tmin = parseInt(min, 10)\n\t\t\t\t\tmax = parseInt(max, 10)\n\t\t\t\t\treturn result.slice(0, this.natural(min, max))\n\t\t\t}\n\t\t},\n\t\t/*\n\t\t    * Random.order(item, item)\n\t\t    * Random.order([item, item ...])\n\n\t\t    顺序获取数组中的元素\n\n\t\t    [JSON导入数组支持数组数据录入](https://github.com/thx/RAP/issues/22)\n\n\t\t    不支持单独调用！\n\t\t*/\n\t\torder: function order(array) {\n\t\t\torder.cache = order.cache || {}\n\n\t\t\tif (arguments.length > 1) array = [].slice.call(arguments, 0)\n\n\t\t\t// options.context.path/templatePath\n\t\t\tvar options = order.options\n\t\t\tvar templatePath = options.context.templatePath.join('.')\n\n\t\t\tvar cache = (\n\t\t\t\torder.cache[templatePath] = order.cache[templatePath] || {\n\t\t\t\t\tindex: 0,\n\t\t\t\t\tarray: array\n\t\t\t\t}\n\t\t\t)\n\n\t\t\treturn cache.array[cache.index++ % cache.array.length]\n\t\t}\n\t}\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Name\n\n\t    [Beyond the Top 1000 Names](http://www.ssa.gov/oact/babynames/limits.html)\n\t*/\n\tmodule.exports = {\n\t\t// 随机生成一个常见的英文名。\n\t\tfirst: function() {\n\t\t\tvar names = [\n\t\t\t\t// male\n\t\t\t\t\"James\", \"John\", \"Robert\", \"Michael\", \"William\",\n\t\t\t\t\"David\", \"Richard\", \"Charles\", \"Joseph\", \"Thomas\",\n\t\t\t\t\"Christopher\", \"Daniel\", \"Paul\", \"Mark\", \"Donald\",\n\t\t\t\t\"George\", \"Kenneth\", \"Steven\", \"Edward\", \"Brian\",\n\t\t\t\t\"Ronald\", \"Anthony\", \"Kevin\", \"Jason\", \"Matthew\",\n\t\t\t\t\"Gary\", \"Timothy\", \"Jose\", \"Larry\", \"Jeffrey\",\n\t\t\t\t\"Frank\", \"Scott\", \"Eric\"\n\t\t\t].concat([\n\t\t\t\t// female\n\t\t\t\t\"Mary\", \"Patricia\", \"Linda\", \"Barbara\", \"Elizabeth\",\n\t\t\t\t\"Jennifer\", \"Maria\", \"Susan\", \"Margaret\", \"Dorothy\",\n\t\t\t\t\"Lisa\", \"Nancy\", \"Karen\", \"Betty\", \"Helen\",\n\t\t\t\t\"Sandra\", \"Donna\", \"Carol\", \"Ruth\", \"Sharon\",\n\t\t\t\t\"Michelle\", \"Laura\", \"Sarah\", \"Kimberly\", \"Deborah\",\n\t\t\t\t\"Jessica\", \"Shirley\", \"Cynthia\", \"Angela\", \"Melissa\",\n\t\t\t\t\"Brenda\", \"Amy\", \"Anna\"\n\t\t\t])\n\t\t\treturn this.pick(names)\n\t\t\t\t// or this.capitalize(this.word())\n\t\t},\n\t\t// 随机生成一个常见的英文姓。\n\t\tlast: function() {\n\t\t\tvar names = [\n\t\t\t\t\"Smith\", \"Johnson\", \"Williams\", \"Brown\", \"Jones\",\n\t\t\t\t\"Miller\", \"Davis\", \"Garcia\", \"Rodriguez\", \"Wilson\",\n\t\t\t\t\"Martinez\", \"Anderson\", \"Taylor\", \"Thomas\", \"Hernandez\",\n\t\t\t\t\"Moore\", \"Martin\", \"Jackson\", \"Thompson\", \"White\",\n\t\t\t\t\"Lopez\", \"Lee\", \"Gonzalez\", \"Harris\", \"Clark\",\n\t\t\t\t\"Lewis\", \"Robinson\", \"Walker\", \"Perez\", \"Hall\",\n\t\t\t\t\"Young\", \"Allen\"\n\t\t\t]\n\t\t\treturn this.pick(names)\n\t\t\t\t// or this.capitalize(this.word())\n\t\t},\n\t\t// 随机生成一个常见的英文姓名。\n\t\tname: function(middle) {\n\t\t\treturn this.first() + ' ' +\n\t\t\t\t(middle ? this.first() + ' ' : '') +\n\t\t\t\tthis.last()\n\t\t},\n\t\t/*\n\t\t    随机生成一个常见的中文姓。\n\t\t    [世界常用姓氏排行](http://baike.baidu.com/view/1719115.htm)\n\t\t    [玄派网 - 网络小说创作辅助平台](http://xuanpai.sinaapp.com/)\n\t\t */\n\t\tcfirst: function() {\n\t\t\tvar names = (\n\t\t\t\t'王 李 张 刘 陈 杨 赵 黄 周 吴 ' +\n\t\t\t\t'徐 孙 胡 朱 高 林 何 郭 马 罗 ' +\n\t\t\t\t'梁 宋 郑 谢 韩 唐 冯 于 董 萧 ' +\n\t\t\t\t'程 曹 袁 邓 许 傅 沈 曾 彭 吕 ' +\n\t\t\t\t'苏 卢 蒋 蔡 贾 丁 魏 薛 叶 阎 ' +\n\t\t\t\t'余 潘 杜 戴 夏 锺 汪 田 任 姜 ' +\n\t\t\t\t'范 方 石 姚 谭 廖 邹 熊 金 陆 ' +\n\t\t\t\t'郝 孔 白 崔 康 毛 邱 秦 江 史 ' +\n\t\t\t\t'顾 侯 邵 孟 龙 万 段 雷 钱 汤 ' +\n\t\t\t\t'尹 黎 易 常 武 乔 贺 赖 龚 文'\n\t\t\t).split(' ')\n\t\t\treturn this.pick(names)\n\t\t},\n\t\t/*\n\t\t    随机生成一个常见的中文名。\n\t\t    [中国最常见名字前50名_三九算命网](http://www.name999.net/xingming/xingshi/20131004/48.html)\n\t\t */\n\t\tclast: function() {\n\t\t\tvar names = (\n\t\t\t\t'伟 芳 娜 秀英 敏 静 丽 强 磊 军 ' +\n\t\t\t\t'洋 勇 艳 杰 娟 涛 明 超 秀兰 霞 ' +\n\t\t\t\t'平 刚 桂英'\n\t\t\t).split(' ')\n\t\t\treturn this.pick(names)\n\t\t},\n\t\t// 随机生成一个常见的中文姓名。\n\t\tcname: function() {\n\t\t\treturn this.cfirst() + this.clast()\n\t\t}\n\t}\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Web\n\t*/\n\tmodule.exports = {\n\t    /*\n\t        随机生成一个 URL。\n\n\t        [URL 规范](http://www.w3.org/Addressing/URL/url-spec.txt)\n\t            http                    Hypertext Transfer Protocol \n\t            ftp                     File Transfer protocol \n\t            gopher                  The Gopher protocol \n\t            mailto                  Electronic mail address \n\t            mid                     Message identifiers for electronic mail \n\t            cid                     Content identifiers for MIME body part \n\t            news                    Usenet news \n\t            nntp                    Usenet news for local NNTP access only \n\t            prospero                Access using the prospero protocols \n\t            telnet rlogin tn3270    Reference to interactive sessions\n\t            wais                    Wide Area Information Servers \n\t    */\n\t    url: function(protocol, host) {\n\t        return (protocol || this.protocol()) + '://' + // protocol?\n\t            (host || this.domain()) + // host?\n\t            '/' + this.word()\n\t    },\n\t    // 随机生成一个 URL 协议。\n\t    protocol: function() {\n\t        return this.pick(\n\t            // 协议簇\n\t            'http ftp gopher mailto mid cid news nntp prospero telnet rlogin tn3270 wais'.split(' ')\n\t        )\n\t    },\n\t    // 随机生成一个域名。\n\t    domain: function(tld) {\n\t        return this.word() + '.' + (tld || this.tld())\n\t    },\n\t    /*\n\t        随机生成一个顶级域名。\n\t        国际顶级域名 international top-level domain-names, iTLDs\n\t        国家顶级域名 national top-level domainnames, nTLDs\n\t        [域名后缀大全](http://www.163ns.com/zixun/post/4417.html)\n\t    */\n\t    tld: function() { // Top Level Domain\n\t        return this.pick(\n\t            (\n\t                // 域名后缀\n\t                'com net org edu gov int mil cn ' +\n\t                // 国内域名\n\t                'com.cn net.cn gov.cn org.cn ' +\n\t                // 中文国内域名\n\t                '中国 中国互联.公司 中国互联.网络 ' +\n\t                // 新国际域名\n\t                'tel biz cc tv info name hk mobi asia cd travel pro museum coop aero ' +\n\t                // 世界各国域名后缀\n\t                'ad ae af ag ai al am an ao aq ar as at au aw az ba bb bd be bf bg bh bi bj bm bn bo br bs bt bv bw by bz ca cc cf cg ch ci ck cl cm cn co cq cr cu cv cx cy cz de dj dk dm do dz ec ee eg eh es et ev fi fj fk fm fo fr ga gb gd ge gf gh gi gl gm gn gp gr gt gu gw gy hk hm hn hr ht hu id ie il in io iq ir is it jm jo jp ke kg kh ki km kn kp kr kw ky kz la lb lc li lk lr ls lt lu lv ly ma mc md mg mh ml mm mn mo mp mq mr ms mt mv mw mx my mz na nc ne nf ng ni nl no np nr nt nu nz om qa pa pe pf pg ph pk pl pm pn pr pt pw py re ro ru rw sa sb sc sd se sg sh si sj sk sl sm sn so sr st su sy sz tc td tf tg th tj tk tm tn to tp tr tt tv tw tz ua ug uk us uy va vc ve vg vn vu wf ws ye yu za zm zr zw'\n\t            ).split(' ')\n\t        )\n\t    },\n\t    // 随机生成一个邮件地址。\n\t    email: function(domain) {\n\t        return this.character('lower') + '.' + this.word() + '@' +\n\t            (\n\t                domain ||\n\t                (this.word() + '.' + this.tld())\n\t            )\n\t            // return this.character('lower') + '.' + this.last().toLowerCase() + '@' + this.last().toLowerCase() + '.' + this.tld()\n\t            // return this.word() + '@' + (domain || this.domain())\n\t    },\n\t    // 随机生成一个 IP 地址。\n\t    ip: function() {\n\t        return this.natural(0, 255) + '.' +\n\t            this.natural(0, 255) + '.' +\n\t            this.natural(0, 255) + '.' +\n\t            this.natural(0, 255)\n\t    }\n\t}\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## Address\n\t*/\n\n\tvar DICT = __webpack_require__(18)\n\tvar REGION = ['东北', '华北', '华东', '华中', '华南', '西南', '西北']\n\n\tmodule.exports = {\n\t    // 随机生成一个大区。\n\t    region: function() {\n\t        return this.pick(REGION)\n\t    },\n\t    // 随机生成一个（中国）省（或直辖市、自治区、特别行政区）。\n\t    province: function() {\n\t        return this.pick(DICT).name\n\t    },\n\t    // 随机生成一个（中国）市。\n\t    city: function(prefix) {\n\t        var province = this.pick(DICT)\n\t        var city = this.pick(province.children)\n\t        return prefix ? [province.name, city.name].join(' ') : city.name\n\t    },\n\t    // 随机生成一个（中国）县。\n\t    county: function(prefix) {\n\t        var province = this.pick(DICT)\n\t        var city = this.pick(province.children)\n\t        var county = this.pick(city.children) || {\n\t            name: '-'\n\t        }\n\t        return prefix ? [province.name, city.name, county.name].join(' ') : county.name\n\t    },\n\t    // 随机生成一个邮政编码（六位数字）。\n\t    zip: function(len) {\n\t        var zip = ''\n\t        for (var i = 0; i < (len || 6); i++) zip += this.natural(0, 9)\n\t        return zip\n\t    }\n\n\t    // address: function() {},\n\t    // phone: function() {},\n\t    // areacode: function() {},\n\t    // street: function() {},\n\t    // street_suffixes: function() {},\n\t    // street_suffix: function() {},\n\t    // states: function() {},\n\t    // state: function() {},\n\t}\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports) {\n\n\t/*\n\t    ## Address 字典数据\n\n\t    字典数据来源 http://www.atatech.org/articles/30028?rnd=254259856\n\n\t    国标 省（市）级行政区划码表\n\n\t    华北   北京市 天津市 河北省 山西省 内蒙古自治区\n\t    东北   辽宁省 吉林省 黑龙江省\n\t    华东   上海市 江苏省 浙江省 安徽省 福建省 江西省 山东省\n\t    华南   广东省 广西壮族自治区 海南省\n\t    华中   河南省 湖北省 湖南省\n\t    西南   重庆市 四川省 贵州省 云南省 西藏自治区\n\t    西北   陕西省 甘肃省 青海省 宁夏回族自治区 新疆维吾尔自治区\n\t    港澳台 香港特别行政区 澳门特别行政区 台湾省\n\t    \n\t    **排序**\n\t    \n\t    ```js\n\t    var map = {}\n\t    _.each(_.keys(REGIONS),function(id){\n\t      map[id] = REGIONS[ID]\n\t    })\n\t    JSON.stringify(map)\n\t    ```\n\t*/\n\tvar DICT = {\n\t    \"110000\": \"北京\",\n\t    \"110100\": \"北京市\",\n\t    \"110101\": \"东城区\",\n\t    \"110102\": \"西城区\",\n\t    \"110105\": \"朝阳区\",\n\t    \"110106\": \"丰台区\",\n\t    \"110107\": \"石景山区\",\n\t    \"110108\": \"海淀区\",\n\t    \"110109\": \"门头沟区\",\n\t    \"110111\": \"房山区\",\n\t    \"110112\": \"通州区\",\n\t    \"110113\": \"顺义区\",\n\t    \"110114\": \"昌平区\",\n\t    \"110115\": \"大兴区\",\n\t    \"110116\": \"怀柔区\",\n\t    \"110117\": \"平谷区\",\n\t    \"110228\": \"密云县\",\n\t    \"110229\": \"延庆县\",\n\t    \"110230\": \"其它区\",\n\t    \"120000\": \"天津\",\n\t    \"120100\": \"天津市\",\n\t    \"120101\": \"和平区\",\n\t    \"120102\": \"河东区\",\n\t    \"120103\": \"河西区\",\n\t    \"120104\": \"南开区\",\n\t    \"120105\": \"河北区\",\n\t    \"120106\": \"红桥区\",\n\t    \"120110\": \"东丽区\",\n\t    \"120111\": \"西青区\",\n\t    \"120112\": \"津南区\",\n\t    \"120113\": \"北辰区\",\n\t    \"120114\": \"武清区\",\n\t    \"120115\": \"宝坻区\",\n\t    \"120116\": \"滨海新区\",\n\t    \"120221\": \"宁河县\",\n\t    \"120223\": \"静海县\",\n\t    \"120225\": \"蓟县\",\n\t    \"120226\": \"其它区\",\n\t    \"130000\": \"河北省\",\n\t    \"130100\": \"石家庄市\",\n\t    \"130102\": \"长安区\",\n\t    \"130103\": \"桥东区\",\n\t    \"130104\": \"桥西区\",\n\t    \"130105\": \"新华区\",\n\t    \"130107\": \"井陉矿区\",\n\t    \"130108\": \"裕华区\",\n\t    \"130121\": \"井陉县\",\n\t    \"130123\": \"正定县\",\n\t    \"130124\": \"栾城县\",\n\t    \"130125\": \"行唐县\",\n\t    \"130126\": \"灵寿县\",\n\t    \"130127\": \"高邑县\",\n\t    \"130128\": \"深泽县\",\n\t    \"130129\": \"赞皇县\",\n\t    \"130130\": \"无极县\",\n\t    \"130131\": \"平山县\",\n\t    \"130132\": \"元氏县\",\n\t    \"130133\": \"赵县\",\n\t    \"130181\": \"辛集市\",\n\t    \"130182\": \"藁城市\",\n\t    \"130183\": \"晋州市\",\n\t    \"130184\": \"新乐市\",\n\t    \"130185\": \"鹿泉市\",\n\t    \"130186\": \"其它区\",\n\t    \"130200\": \"唐山市\",\n\t    \"130202\": \"路南区\",\n\t    \"130203\": \"路北区\",\n\t    \"130204\": \"古冶区\",\n\t    \"130205\": \"开平区\",\n\t    \"130207\": \"丰南区\",\n\t    \"130208\": \"丰润区\",\n\t    \"130223\": \"滦县\",\n\t    \"130224\": \"滦南县\",\n\t    \"130225\": \"乐亭县\",\n\t    \"130227\": \"迁西县\",\n\t    \"130229\": \"玉田县\",\n\t    \"130230\": \"曹妃甸区\",\n\t    \"130281\": \"遵化市\",\n\t    \"130283\": \"迁安市\",\n\t    \"130284\": \"其它区\",\n\t    \"130300\": \"秦皇岛市\",\n\t    \"130302\": \"海港区\",\n\t    \"130303\": \"山海关区\",\n\t    \"130304\": \"北戴河区\",\n\t    \"130321\": \"青龙满族自治县\",\n\t    \"130322\": \"昌黎县\",\n\t    \"130323\": \"抚宁县\",\n\t    \"130324\": \"卢龙县\",\n\t    \"130398\": \"其它区\",\n\t    \"130400\": \"邯郸市\",\n\t    \"130402\": \"邯山区\",\n\t    \"130403\": \"丛台区\",\n\t    \"130404\": \"复兴区\",\n\t    \"130406\": \"峰峰矿区\",\n\t    \"130421\": \"邯郸县\",\n\t    \"130423\": \"临漳县\",\n\t    \"130424\": \"成安县\",\n\t    \"130425\": \"大名县\",\n\t    \"130426\": \"涉县\",\n\t    \"130427\": \"磁县\",\n\t    \"130428\": \"肥乡县\",\n\t    \"130429\": \"永年县\",\n\t    \"130430\": \"邱县\",\n\t    \"130431\": \"鸡泽县\",\n\t    \"130432\": \"广平县\",\n\t    \"130433\": \"馆陶县\",\n\t    \"130434\": \"魏县\",\n\t    \"130435\": \"曲周县\",\n\t    \"130481\": \"武安市\",\n\t    \"130482\": \"其它区\",\n\t    \"130500\": \"邢台市\",\n\t    \"130502\": \"桥东区\",\n\t    \"130503\": \"桥西区\",\n\t    \"130521\": \"邢台县\",\n\t    \"130522\": \"临城县\",\n\t    \"130523\": \"内丘县\",\n\t    \"130524\": \"柏乡县\",\n\t    \"130525\": \"隆尧县\",\n\t    \"130526\": \"任县\",\n\t    \"130527\": \"南和县\",\n\t    \"130528\": \"宁晋县\",\n\t    \"130529\": \"巨鹿县\",\n\t    \"130530\": \"新河县\",\n\t    \"130531\": \"广宗县\",\n\t    \"130532\": \"平乡县\",\n\t    \"130533\": \"威县\",\n\t    \"130534\": \"清河县\",\n\t    \"130535\": \"临西县\",\n\t    \"130581\": \"南宫市\",\n\t    \"130582\": \"沙河市\",\n\t    \"130583\": \"其它区\",\n\t    \"130600\": \"保定市\",\n\t    \"130602\": \"新市区\",\n\t    \"130603\": \"北市区\",\n\t    \"130604\": \"南市区\",\n\t    \"130621\": \"满城县\",\n\t    \"130622\": \"清苑县\",\n\t    \"130623\": \"涞水县\",\n\t    \"130624\": \"阜平县\",\n\t    \"130625\": \"徐水县\",\n\t    \"130626\": \"定兴县\",\n\t    \"130627\": \"唐县\",\n\t    \"130628\": \"高阳县\",\n\t    \"130629\": \"容城县\",\n\t    \"130630\": \"涞源县\",\n\t    \"130631\": \"望都县\",\n\t    \"130632\": \"安新县\",\n\t    \"130633\": \"易县\",\n\t    \"130634\": \"曲阳县\",\n\t    \"130635\": \"蠡县\",\n\t    \"130636\": \"顺平县\",\n\t    \"130637\": \"博野县\",\n\t    \"130638\": \"雄县\",\n\t    \"130681\": \"涿州市\",\n\t    \"130682\": \"定州市\",\n\t    \"130683\": \"安国市\",\n\t    \"130684\": \"高碑店市\",\n\t    \"130699\": \"其它区\",\n\t    \"130700\": \"张家口市\",\n\t    \"130702\": \"桥东区\",\n\t    \"130703\": \"桥西区\",\n\t    \"130705\": \"宣化区\",\n\t    \"130706\": \"下花园区\",\n\t    \"130721\": \"宣化县\",\n\t    \"130722\": \"张北县\",\n\t    \"130723\": \"康保县\",\n\t    \"130724\": \"沽源县\",\n\t    \"130725\": \"尚义县\",\n\t    \"130726\": \"蔚县\",\n\t    \"130727\": \"阳原县\",\n\t    \"130728\": \"怀安县\",\n\t    \"130729\": \"万全县\",\n\t    \"130730\": \"怀来县\",\n\t    \"130731\": \"涿鹿县\",\n\t    \"130732\": \"赤城县\",\n\t    \"130733\": \"崇礼县\",\n\t    \"130734\": \"其它区\",\n\t    \"130800\": \"承德市\",\n\t    \"130802\": \"双桥区\",\n\t    \"130803\": \"双滦区\",\n\t    \"130804\": \"鹰手营子矿区\",\n\t    \"130821\": \"承德县\",\n\t    \"130822\": \"兴隆县\",\n\t    \"130823\": \"平泉县\",\n\t    \"130824\": \"滦平县\",\n\t    \"130825\": \"隆化县\",\n\t    \"130826\": \"丰宁满族自治县\",\n\t    \"130827\": \"宽城满族自治县\",\n\t    \"130828\": \"围场满族蒙古族自治县\",\n\t    \"130829\": \"其它区\",\n\t    \"130900\": \"沧州市\",\n\t    \"130902\": \"新华区\",\n\t    \"130903\": \"运河区\",\n\t    \"130921\": \"沧县\",\n\t    \"130922\": \"青县\",\n\t    \"130923\": \"东光县\",\n\t    \"130924\": \"海兴县\",\n\t    \"130925\": \"盐山县\",\n\t    \"130926\": \"肃宁县\",\n\t    \"130927\": \"南皮县\",\n\t    \"130928\": \"吴桥县\",\n\t    \"130929\": \"献县\",\n\t    \"130930\": \"孟村回族自治县\",\n\t    \"130981\": \"泊头市\",\n\t    \"130982\": \"任丘市\",\n\t    \"130983\": \"黄骅市\",\n\t    \"130984\": \"河间市\",\n\t    \"130985\": \"其它区\",\n\t    \"131000\": \"廊坊市\",\n\t    \"131002\": \"安次区\",\n\t    \"131003\": \"广阳区\",\n\t    \"131022\": \"固安县\",\n\t    \"131023\": \"永清县\",\n\t    \"131024\": \"香河县\",\n\t    \"131025\": \"大城县\",\n\t    \"131026\": \"文安县\",\n\t    \"131028\": \"大厂回族自治县\",\n\t    \"131081\": \"霸州市\",\n\t    \"131082\": \"三河市\",\n\t    \"131083\": \"其它区\",\n\t    \"131100\": \"衡水市\",\n\t    \"131102\": \"桃城区\",\n\t    \"131121\": \"枣强县\",\n\t    \"131122\": \"武邑县\",\n\t    \"131123\": \"武强县\",\n\t    \"131124\": \"饶阳县\",\n\t    \"131125\": \"安平县\",\n\t    \"131126\": \"故城县\",\n\t    \"131127\": \"景县\",\n\t    \"131128\": \"阜城县\",\n\t    \"131181\": \"冀州市\",\n\t    \"131182\": \"深州市\",\n\t    \"131183\": \"其它区\",\n\t    \"140000\": \"山西省\",\n\t    \"140100\": \"太原市\",\n\t    \"140105\": \"小店区\",\n\t    \"140106\": \"迎泽区\",\n\t    \"140107\": \"杏花岭区\",\n\t    \"140108\": \"尖草坪区\",\n\t    \"140109\": \"万柏林区\",\n\t    \"140110\": \"晋源区\",\n\t    \"140121\": \"清徐县\",\n\t    \"140122\": \"阳曲县\",\n\t    \"140123\": \"娄烦县\",\n\t    \"140181\": \"古交市\",\n\t    \"140182\": \"其它区\",\n\t    \"140200\": \"大同市\",\n\t    \"140202\": \"城区\",\n\t    \"140203\": \"矿区\",\n\t    \"140211\": \"南郊区\",\n\t    \"140212\": \"新荣区\",\n\t    \"140221\": \"阳高县\",\n\t    \"140222\": \"天镇县\",\n\t    \"140223\": \"广灵县\",\n\t    \"140224\": \"灵丘县\",\n\t    \"140225\": \"浑源县\",\n\t    \"140226\": \"左云县\",\n\t    \"140227\": \"大同县\",\n\t    \"140228\": \"其它区\",\n\t    \"140300\": \"阳泉市\",\n\t    \"140302\": \"城区\",\n\t    \"140303\": \"矿区\",\n\t    \"140311\": \"郊区\",\n\t    \"140321\": \"平定县\",\n\t    \"140322\": \"盂县\",\n\t    \"140323\": \"其它区\",\n\t    \"140400\": \"长治市\",\n\t    \"140421\": \"长治县\",\n\t    \"140423\": \"襄垣县\",\n\t    \"140424\": \"屯留县\",\n\t    \"140425\": \"平顺县\",\n\t    \"140426\": \"黎城县\",\n\t    \"140427\": \"壶关县\",\n\t    \"140428\": \"长子县\",\n\t    \"140429\": \"武乡县\",\n\t    \"140430\": \"沁县\",\n\t    \"140431\": \"沁源县\",\n\t    \"140481\": \"潞城市\",\n\t    \"140482\": \"城区\",\n\t    \"140483\": \"郊区\",\n\t    \"140485\": \"其它区\",\n\t    \"140500\": \"晋城市\",\n\t    \"140502\": \"城区\",\n\t    \"140521\": \"沁水县\",\n\t    \"140522\": \"阳城县\",\n\t    \"140524\": \"陵川县\",\n\t    \"140525\": \"泽州县\",\n\t    \"140581\": \"高平市\",\n\t    \"140582\": \"其它区\",\n\t    \"140600\": \"朔州市\",\n\t    \"140602\": \"朔城区\",\n\t    \"140603\": \"平鲁区\",\n\t    \"140621\": \"山阴县\",\n\t    \"140622\": \"应县\",\n\t    \"140623\": \"右玉县\",\n\t    \"140624\": \"怀仁县\",\n\t    \"140625\": \"其它区\",\n\t    \"140700\": \"晋中市\",\n\t    \"140702\": \"榆次区\",\n\t    \"140721\": \"榆社县\",\n\t    \"140722\": \"左权县\",\n\t    \"140723\": \"和顺县\",\n\t    \"140724\": \"昔阳县\",\n\t    \"140725\": \"寿阳县\",\n\t    \"140726\": \"太谷县\",\n\t    \"140727\": \"祁县\",\n\t    \"140728\": \"平遥县\",\n\t    \"140729\": \"灵石县\",\n\t    \"140781\": \"介休市\",\n\t    \"140782\": \"其它区\",\n\t    \"140800\": \"运城市\",\n\t    \"140802\": \"盐湖区\",\n\t    \"140821\": \"临猗县\",\n\t    \"140822\": \"万荣县\",\n\t    \"140823\": \"闻喜县\",\n\t    \"140824\": \"稷山县\",\n\t    \"140825\": \"新绛县\",\n\t    \"140826\": \"绛县\",\n\t    \"140827\": \"垣曲县\",\n\t    \"140828\": \"夏县\",\n\t    \"140829\": \"平陆县\",\n\t    \"140830\": \"芮城县\",\n\t    \"140881\": \"永济市\",\n\t    \"140882\": \"河津市\",\n\t    \"140883\": \"其它区\",\n\t    \"140900\": \"忻州市\",\n\t    \"140902\": \"忻府区\",\n\t    \"140921\": \"定襄县\",\n\t    \"140922\": \"五台县\",\n\t    \"140923\": \"代县\",\n\t    \"140924\": \"繁峙县\",\n\t    \"140925\": \"宁武县\",\n\t    \"140926\": \"静乐县\",\n\t    \"140927\": \"神池县\",\n\t    \"140928\": \"五寨县\",\n\t    \"140929\": \"岢岚县\",\n\t    \"140930\": \"河曲县\",\n\t    \"140931\": \"保德县\",\n\t    \"140932\": \"偏关县\",\n\t    \"140981\": \"原平市\",\n\t    \"140982\": \"其它区\",\n\t    \"141000\": \"临汾市\",\n\t    \"141002\": \"尧都区\",\n\t    \"141021\": \"曲沃县\",\n\t    \"141022\": \"翼城县\",\n\t    \"141023\": \"襄汾县\",\n\t    \"141024\": \"洪洞县\",\n\t    \"141025\": \"古县\",\n\t    \"141026\": \"安泽县\",\n\t    \"141027\": \"浮山县\",\n\t    \"141028\": \"吉县\",\n\t    \"141029\": \"乡宁县\",\n\t    \"141030\": \"大宁县\",\n\t    \"141031\": \"隰县\",\n\t    \"141032\": \"永和县\",\n\t    \"141033\": \"蒲县\",\n\t    \"141034\": \"汾西县\",\n\t    \"141081\": \"侯马市\",\n\t    \"141082\": \"霍州市\",\n\t    \"141083\": \"其它区\",\n\t    \"141100\": \"吕梁市\",\n\t    \"141102\": \"离石区\",\n\t    \"141121\": \"文水县\",\n\t    \"141122\": \"交城县\",\n\t    \"141123\": \"兴县\",\n\t    \"141124\": \"临县\",\n\t    \"141125\": \"柳林县\",\n\t    \"141126\": \"石楼县\",\n\t    \"141127\": \"岚县\",\n\t    \"141128\": \"方山县\",\n\t    \"141129\": \"中阳县\",\n\t    \"141130\": \"交口县\",\n\t    \"141181\": \"孝义市\",\n\t    \"141182\": \"汾阳市\",\n\t    \"141183\": \"其它区\",\n\t    \"150000\": \"内蒙古自治区\",\n\t    \"150100\": \"呼和浩特市\",\n\t    \"150102\": \"新城区\",\n\t    \"150103\": \"回民区\",\n\t    \"150104\": \"玉泉区\",\n\t    \"150105\": \"赛罕区\",\n\t    \"150121\": \"土默特左旗\",\n\t    \"150122\": \"托克托县\",\n\t    \"150123\": \"和林格尔县\",\n\t    \"150124\": \"清水河县\",\n\t    \"150125\": \"武川县\",\n\t    \"150126\": \"其它区\",\n\t    \"150200\": \"包头市\",\n\t    \"150202\": \"东河区\",\n\t    \"150203\": \"昆都仑区\",\n\t    \"150204\": \"青山区\",\n\t    \"150205\": \"石拐区\",\n\t    \"150206\": \"白云鄂博矿区\",\n\t    \"150207\": \"九原区\",\n\t    \"150221\": \"土默特右旗\",\n\t    \"150222\": \"固阳县\",\n\t    \"150223\": \"达尔罕茂明安联合旗\",\n\t    \"150224\": \"其它区\",\n\t    \"150300\": \"乌海市\",\n\t    \"150302\": \"海勃湾区\",\n\t    \"150303\": \"海南区\",\n\t    \"150304\": \"乌达区\",\n\t    \"150305\": \"其它区\",\n\t    \"150400\": \"赤峰市\",\n\t    \"150402\": \"红山区\",\n\t    \"150403\": \"元宝山区\",\n\t    \"150404\": \"松山区\",\n\t    \"150421\": \"阿鲁科尔沁旗\",\n\t    \"150422\": \"巴林左旗\",\n\t    \"150423\": \"巴林右旗\",\n\t    \"150424\": \"林西县\",\n\t    \"150425\": \"克什克腾旗\",\n\t    \"150426\": \"翁牛特旗\",\n\t    \"150428\": \"喀喇沁旗\",\n\t    \"150429\": \"宁城县\",\n\t    \"150430\": \"敖汉旗\",\n\t    \"150431\": \"其它区\",\n\t    \"150500\": \"通辽市\",\n\t    \"150502\": \"科尔沁区\",\n\t    \"150521\": \"科尔沁左翼中旗\",\n\t    \"150522\": \"科尔沁左翼后旗\",\n\t    \"150523\": \"开鲁县\",\n\t    \"150524\": \"库伦旗\",\n\t    \"150525\": \"奈曼旗\",\n\t    \"150526\": \"扎鲁特旗\",\n\t    \"150581\": \"霍林郭勒市\",\n\t    \"150582\": \"其它区\",\n\t    \"150600\": \"鄂尔多斯市\",\n\t    \"150602\": \"东胜区\",\n\t    \"150621\": \"达拉特旗\",\n\t    \"150622\": \"准格尔旗\",\n\t    \"150623\": \"鄂托克前旗\",\n\t    \"150624\": \"鄂托克旗\",\n\t    \"150625\": \"杭锦旗\",\n\t    \"150626\": \"乌审旗\",\n\t    \"150627\": \"伊金霍洛旗\",\n\t    \"150628\": \"其它区\",\n\t    \"150700\": \"呼伦贝尔市\",\n\t    \"150702\": \"海拉尔区\",\n\t    \"150703\": \"扎赉诺尔区\",\n\t    \"150721\": \"阿荣旗\",\n\t    \"150722\": \"莫力达瓦达斡尔族自治旗\",\n\t    \"150723\": \"鄂伦春自治旗\",\n\t    \"150724\": \"鄂温克族自治旗\",\n\t    \"150725\": \"陈巴尔虎旗\",\n\t    \"150726\": \"新巴尔虎左旗\",\n\t    \"150727\": \"新巴尔虎右旗\",\n\t    \"150781\": \"满洲里市\",\n\t    \"150782\": \"牙克石市\",\n\t    \"150783\": \"扎兰屯市\",\n\t    \"150784\": \"额尔古纳市\",\n\t    \"150785\": \"根河市\",\n\t    \"150786\": \"其它区\",\n\t    \"150800\": \"巴彦淖尔市\",\n\t    \"150802\": \"临河区\",\n\t    \"150821\": \"五原县\",\n\t    \"150822\": \"磴口县\",\n\t    \"150823\": \"乌拉特前旗\",\n\t    \"150824\": \"乌拉特中旗\",\n\t    \"150825\": \"乌拉特后旗\",\n\t    \"150826\": \"杭锦后旗\",\n\t    \"150827\": \"其它区\",\n\t    \"150900\": \"乌兰察布市\",\n\t    \"150902\": \"集宁区\",\n\t    \"150921\": \"卓资县\",\n\t    \"150922\": \"化德县\",\n\t    \"150923\": \"商都县\",\n\t    \"150924\": \"兴和县\",\n\t    \"150925\": \"凉城县\",\n\t    \"150926\": \"察哈尔右翼前旗\",\n\t    \"150927\": \"察哈尔右翼中旗\",\n\t    \"150928\": \"察哈尔右翼后旗\",\n\t    \"150929\": \"四子王旗\",\n\t    \"150981\": \"丰镇市\",\n\t    \"150982\": \"其它区\",\n\t    \"152200\": \"兴安盟\",\n\t    \"152201\": \"乌兰浩特市\",\n\t    \"152202\": \"阿尔山市\",\n\t    \"152221\": \"科尔沁右翼前旗\",\n\t    \"152222\": \"科尔沁右翼中旗\",\n\t    \"152223\": \"扎赉特旗\",\n\t    \"152224\": \"突泉县\",\n\t    \"152225\": \"其它区\",\n\t    \"152500\": \"锡林郭勒盟\",\n\t    \"152501\": \"二连浩特市\",\n\t    \"152502\": \"锡林浩特市\",\n\t    \"152522\": \"阿巴嘎旗\",\n\t    \"152523\": \"苏尼特左旗\",\n\t    \"152524\": \"苏尼特右旗\",\n\t    \"152525\": \"东乌珠穆沁旗\",\n\t    \"152526\": \"西乌珠穆沁旗\",\n\t    \"152527\": \"太仆寺旗\",\n\t    \"152528\": \"镶黄旗\",\n\t    \"152529\": \"正镶白旗\",\n\t    \"152530\": \"正蓝旗\",\n\t    \"152531\": \"多伦县\",\n\t    \"152532\": \"其它区\",\n\t    \"152900\": \"阿拉善盟\",\n\t    \"152921\": \"阿拉善左旗\",\n\t    \"152922\": \"阿拉善右旗\",\n\t    \"152923\": \"额济纳旗\",\n\t    \"152924\": \"其它区\",\n\t    \"210000\": \"辽宁省\",\n\t    \"210100\": \"沈阳市\",\n\t    \"210102\": \"和平区\",\n\t    \"210103\": \"沈河区\",\n\t    \"210104\": \"大东区\",\n\t    \"210105\": \"皇姑区\",\n\t    \"210106\": \"铁西区\",\n\t    \"210111\": \"苏家屯区\",\n\t    \"210112\": \"东陵区\",\n\t    \"210113\": \"新城子区\",\n\t    \"210114\": \"于洪区\",\n\t    \"210122\": \"辽中县\",\n\t    \"210123\": \"康平县\",\n\t    \"210124\": \"法库县\",\n\t    \"210181\": \"新民市\",\n\t    \"210184\": \"沈北新区\",\n\t    \"210185\": \"其它区\",\n\t    \"210200\": \"大连市\",\n\t    \"210202\": \"中山区\",\n\t    \"210203\": \"西岗区\",\n\t    \"210204\": \"沙河口区\",\n\t    \"210211\": \"甘井子区\",\n\t    \"210212\": \"旅顺口区\",\n\t    \"210213\": \"金州区\",\n\t    \"210224\": \"长海县\",\n\t    \"210281\": \"瓦房店市\",\n\t    \"210282\": \"普兰店市\",\n\t    \"210283\": \"庄河市\",\n\t    \"210298\": \"其它区\",\n\t    \"210300\": \"鞍山市\",\n\t    \"210302\": \"铁东区\",\n\t    \"210303\": \"铁西区\",\n\t    \"210304\": \"立山区\",\n\t    \"210311\": \"千山区\",\n\t    \"210321\": \"台安县\",\n\t    \"210323\": \"岫岩满族自治县\",\n\t    \"210381\": \"海城市\",\n\t    \"210382\": \"其它区\",\n\t    \"210400\": \"抚顺市\",\n\t    \"210402\": \"新抚区\",\n\t    \"210403\": \"东洲区\",\n\t    \"210404\": \"望花区\",\n\t    \"210411\": \"顺城区\",\n\t    \"210421\": \"抚顺县\",\n\t    \"210422\": \"新宾满族自治县\",\n\t    \"210423\": \"清原满族自治县\",\n\t    \"210424\": \"其它区\",\n\t    \"210500\": \"本溪市\",\n\t    \"210502\": \"平山区\",\n\t    \"210503\": \"溪湖区\",\n\t    \"210504\": \"明山区\",\n\t    \"210505\": \"南芬区\",\n\t    \"210521\": \"本溪满族自治县\",\n\t    \"210522\": \"桓仁满族自治县\",\n\t    \"210523\": \"其它区\",\n\t    \"210600\": \"丹东市\",\n\t    \"210602\": \"元宝区\",\n\t    \"210603\": \"振兴区\",\n\t    \"210604\": \"振安区\",\n\t    \"210624\": \"宽甸满族自治县\",\n\t    \"210681\": \"东港市\",\n\t    \"210682\": \"凤城市\",\n\t    \"210683\": \"其它区\",\n\t    \"210700\": \"锦州市\",\n\t    \"210702\": \"古塔区\",\n\t    \"210703\": \"凌河区\",\n\t    \"210711\": \"太和区\",\n\t    \"210726\": \"黑山县\",\n\t    \"210727\": \"义县\",\n\t    \"210781\": \"凌海市\",\n\t    \"210782\": \"北镇市\",\n\t    \"210783\": \"其它区\",\n\t    \"210800\": \"营口市\",\n\t    \"210802\": \"站前区\",\n\t    \"210803\": \"西市区\",\n\t    \"210804\": \"鲅鱼圈区\",\n\t    \"210811\": \"老边区\",\n\t    \"210881\": \"盖州市\",\n\t    \"210882\": \"大石桥市\",\n\t    \"210883\": \"其它区\",\n\t    \"210900\": \"阜新市\",\n\t    \"210902\": \"海州区\",\n\t    \"210903\": \"新邱区\",\n\t    \"210904\": \"太平区\",\n\t    \"210905\": \"清河门区\",\n\t    \"210911\": \"细河区\",\n\t    \"210921\": \"阜新蒙古族自治县\",\n\t    \"210922\": \"彰武县\",\n\t    \"210923\": \"其它区\",\n\t    \"211000\": \"辽阳市\",\n\t    \"211002\": \"白塔区\",\n\t    \"211003\": \"文圣区\",\n\t    \"211004\": \"宏伟区\",\n\t    \"211005\": \"弓长岭区\",\n\t    \"211011\": \"太子河区\",\n\t    \"211021\": \"辽阳县\",\n\t    \"211081\": \"灯塔市\",\n\t    \"211082\": \"其它区\",\n\t    \"211100\": \"盘锦市\",\n\t    \"211102\": \"双台子区\",\n\t    \"211103\": \"兴隆台区\",\n\t    \"211121\": \"大洼县\",\n\t    \"211122\": \"盘山县\",\n\t    \"211123\": \"其它区\",\n\t    \"211200\": \"铁岭市\",\n\t    \"211202\": \"银州区\",\n\t    \"211204\": \"清河区\",\n\t    \"211221\": \"铁岭县\",\n\t    \"211223\": \"西丰县\",\n\t    \"211224\": \"昌图县\",\n\t    \"211281\": \"调兵山市\",\n\t    \"211282\": \"开原市\",\n\t    \"211283\": \"其它区\",\n\t    \"211300\": \"朝阳市\",\n\t    \"211302\": \"双塔区\",\n\t    \"211303\": \"龙城区\",\n\t    \"211321\": \"朝阳县\",\n\t    \"211322\": \"建平县\",\n\t    \"211324\": \"喀喇沁左翼蒙古族自治县\",\n\t    \"211381\": \"北票市\",\n\t    \"211382\": \"凌源市\",\n\t    \"211383\": \"其它区\",\n\t    \"211400\": \"葫芦岛市\",\n\t    \"211402\": \"连山区\",\n\t    \"211403\": \"龙港区\",\n\t    \"211404\": \"南票区\",\n\t    \"211421\": \"绥中县\",\n\t    \"211422\": \"建昌县\",\n\t    \"211481\": \"兴城市\",\n\t    \"211482\": \"其它区\",\n\t    \"220000\": \"吉林省\",\n\t    \"220100\": \"长春市\",\n\t    \"220102\": \"南关区\",\n\t    \"220103\": \"宽城区\",\n\t    \"220104\": \"朝阳区\",\n\t    \"220105\": \"二道区\",\n\t    \"220106\": \"绿园区\",\n\t    \"220112\": \"双阳区\",\n\t    \"220122\": \"农安县\",\n\t    \"220181\": \"九台市\",\n\t    \"220182\": \"榆树市\",\n\t    \"220183\": \"德惠市\",\n\t    \"220188\": \"其它区\",\n\t    \"220200\": \"吉林市\",\n\t    \"220202\": \"昌邑区\",\n\t    \"220203\": \"龙潭区\",\n\t    \"220204\": \"船营区\",\n\t    \"220211\": \"丰满区\",\n\t    \"220221\": \"永吉县\",\n\t    \"220281\": \"蛟河市\",\n\t    \"220282\": \"桦甸市\",\n\t    \"220283\": \"舒兰市\",\n\t    \"220284\": \"磐石市\",\n\t    \"220285\": \"其它区\",\n\t    \"220300\": \"四平市\",\n\t    \"220302\": \"铁西区\",\n\t    \"220303\": \"铁东区\",\n\t    \"220322\": \"梨树县\",\n\t    \"220323\": \"伊通满族自治县\",\n\t    \"220381\": \"公主岭市\",\n\t    \"220382\": \"双辽市\",\n\t    \"220383\": \"其它区\",\n\t    \"220400\": \"辽源市\",\n\t    \"220402\": \"龙山区\",\n\t    \"220403\": \"西安区\",\n\t    \"220421\": \"东丰县\",\n\t    \"220422\": \"东辽县\",\n\t    \"220423\": \"其它区\",\n\t    \"220500\": \"通化市\",\n\t    \"220502\": \"东昌区\",\n\t    \"220503\": \"二道江区\",\n\t    \"220521\": \"通化县\",\n\t    \"220523\": \"辉南县\",\n\t    \"220524\": \"柳河县\",\n\t    \"220581\": \"梅河口市\",\n\t    \"220582\": \"集安市\",\n\t    \"220583\": \"其它区\",\n\t    \"220600\": \"白山市\",\n\t    \"220602\": \"浑江区\",\n\t    \"220621\": \"抚松县\",\n\t    \"220622\": \"靖宇县\",\n\t    \"220623\": \"长白朝鲜族自治县\",\n\t    \"220625\": \"江源区\",\n\t    \"220681\": \"临江市\",\n\t    \"220682\": \"其它区\",\n\t    \"220700\": \"松原市\",\n\t    \"220702\": \"宁江区\",\n\t    \"220721\": \"前郭尔罗斯蒙古族自治县\",\n\t    \"220722\": \"长岭县\",\n\t    \"220723\": \"乾安县\",\n\t    \"220724\": \"扶余市\",\n\t    \"220725\": \"其它区\",\n\t    \"220800\": \"白城市\",\n\t    \"220802\": \"洮北区\",\n\t    \"220821\": \"镇赉县\",\n\t    \"220822\": \"通榆县\",\n\t    \"220881\": \"洮南市\",\n\t    \"220882\": \"大安市\",\n\t    \"220883\": \"其它区\",\n\t    \"222400\": \"延边朝鲜族自治州\",\n\t    \"222401\": \"延吉市\",\n\t    \"222402\": \"图们市\",\n\t    \"222403\": \"敦化市\",\n\t    \"222404\": \"珲春市\",\n\t    \"222405\": \"龙井市\",\n\t    \"222406\": \"和龙市\",\n\t    \"222424\": \"汪清县\",\n\t    \"222426\": \"安图县\",\n\t    \"222427\": \"其它区\",\n\t    \"230000\": \"黑龙江省\",\n\t    \"230100\": \"哈尔滨市\",\n\t    \"230102\": \"道里区\",\n\t    \"230103\": \"南岗区\",\n\t    \"230104\": \"道外区\",\n\t    \"230106\": \"香坊区\",\n\t    \"230108\": \"平房区\",\n\t    \"230109\": \"松北区\",\n\t    \"230111\": \"呼兰区\",\n\t    \"230123\": \"依兰县\",\n\t    \"230124\": \"方正县\",\n\t    \"230125\": \"宾县\",\n\t    \"230126\": \"巴彦县\",\n\t    \"230127\": \"木兰县\",\n\t    \"230128\": \"通河县\",\n\t    \"230129\": \"延寿县\",\n\t    \"230181\": \"阿城区\",\n\t    \"230182\": \"双城市\",\n\t    \"230183\": \"尚志市\",\n\t    \"230184\": \"五常市\",\n\t    \"230186\": \"其它区\",\n\t    \"230200\": \"齐齐哈尔市\",\n\t    \"230202\": \"龙沙区\",\n\t    \"230203\": \"建华区\",\n\t    \"230204\": \"铁锋区\",\n\t    \"230205\": \"昂昂溪区\",\n\t    \"230206\": \"富拉尔基区\",\n\t    \"230207\": \"碾子山区\",\n\t    \"230208\": \"梅里斯达斡尔族区\",\n\t    \"230221\": \"龙江县\",\n\t    \"230223\": \"依安县\",\n\t    \"230224\": \"泰来县\",\n\t    \"230225\": \"甘南县\",\n\t    \"230227\": \"富裕县\",\n\t    \"230229\": \"克山县\",\n\t    \"230230\": \"克东县\",\n\t    \"230231\": \"拜泉县\",\n\t    \"230281\": \"讷河市\",\n\t    \"230282\": \"其它区\",\n\t    \"230300\": \"鸡西市\",\n\t    \"230302\": \"鸡冠区\",\n\t    \"230303\": \"恒山区\",\n\t    \"230304\": \"滴道区\",\n\t    \"230305\": \"梨树区\",\n\t    \"230306\": \"城子河区\",\n\t    \"230307\": \"麻山区\",\n\t    \"230321\": \"鸡东县\",\n\t    \"230381\": \"虎林市\",\n\t    \"230382\": \"密山市\",\n\t    \"230383\": \"其它区\",\n\t    \"230400\": \"鹤岗市\",\n\t    \"230402\": \"向阳区\",\n\t    \"230403\": \"工农区\",\n\t    \"230404\": \"南山区\",\n\t    \"230405\": \"兴安区\",\n\t    \"230406\": \"东山区\",\n\t    \"230407\": \"兴山区\",\n\t    \"230421\": \"萝北县\",\n\t    \"230422\": \"绥滨县\",\n\t    \"230423\": \"其它区\",\n\t    \"230500\": \"双鸭山市\",\n\t    \"230502\": \"尖山区\",\n\t    \"230503\": \"岭东区\",\n\t    \"230505\": \"四方台区\",\n\t    \"230506\": \"宝山区\",\n\t    \"230521\": \"集贤县\",\n\t    \"230522\": \"友谊县\",\n\t    \"230523\": \"宝清县\",\n\t    \"230524\": \"饶河县\",\n\t    \"230525\": \"其它区\",\n\t    \"230600\": \"大庆市\",\n\t    \"230602\": \"萨尔图区\",\n\t    \"230603\": \"龙凤区\",\n\t    \"230604\": \"让胡路区\",\n\t    \"230605\": \"红岗区\",\n\t    \"230606\": \"大同区\",\n\t    \"230621\": \"肇州县\",\n\t    \"230622\": \"肇源县\",\n\t    \"230623\": \"林甸县\",\n\t    \"230624\": \"杜尔伯特蒙古族自治县\",\n\t    \"230625\": \"其它区\",\n\t    \"230700\": \"伊春市\",\n\t    \"230702\": \"伊春区\",\n\t    \"230703\": \"南岔区\",\n\t    \"230704\": \"友好区\",\n\t    \"230705\": \"西林区\",\n\t    \"230706\": \"翠峦区\",\n\t    \"230707\": \"新青区\",\n\t    \"230708\": \"美溪区\",\n\t    \"230709\": \"金山屯区\",\n\t    \"230710\": \"五营区\",\n\t    \"230711\": \"乌马河区\",\n\t    \"230712\": \"汤旺河区\",\n\t    \"230713\": \"带岭区\",\n\t    \"230714\": \"乌伊岭区\",\n\t    \"230715\": \"红星区\",\n\t    \"230716\": \"上甘岭区\",\n\t    \"230722\": \"嘉荫县\",\n\t    \"230781\": \"铁力市\",\n\t    \"230782\": \"其它区\",\n\t    \"230800\": \"佳木斯市\",\n\t    \"230803\": \"向阳区\",\n\t    \"230804\": \"前进区\",\n\t    \"230805\": \"东风区\",\n\t    \"230811\": \"郊区\",\n\t    \"230822\": \"桦南县\",\n\t    \"230826\": \"桦川县\",\n\t    \"230828\": \"汤原县\",\n\t    \"230833\": \"抚远县\",\n\t    \"230881\": \"同江市\",\n\t    \"230882\": \"富锦市\",\n\t    \"230883\": \"其它区\",\n\t    \"230900\": \"七台河市\",\n\t    \"230902\": \"新兴区\",\n\t    \"230903\": \"桃山区\",\n\t    \"230904\": \"茄子河区\",\n\t    \"230921\": \"勃利县\",\n\t    \"230922\": \"其它区\",\n\t    \"231000\": \"牡丹江市\",\n\t    \"231002\": \"东安区\",\n\t    \"231003\": \"阳明区\",\n\t    \"231004\": \"爱民区\",\n\t    \"231005\": \"西安区\",\n\t    \"231024\": \"东宁县\",\n\t    \"231025\": \"林口县\",\n\t    \"231081\": \"绥芬河市\",\n\t    \"231083\": \"海林市\",\n\t    \"231084\": \"宁安市\",\n\t    \"231085\": \"穆棱市\",\n\t    \"231086\": \"其它区\",\n\t    \"231100\": \"黑河市\",\n\t    \"231102\": \"爱辉区\",\n\t    \"231121\": \"嫩江县\",\n\t    \"231123\": \"逊克县\",\n\t    \"231124\": \"孙吴县\",\n\t    \"231181\": \"北安市\",\n\t    \"231182\": \"五大连池市\",\n\t    \"231183\": \"其它区\",\n\t    \"231200\": \"绥化市\",\n\t    \"231202\": \"北林区\",\n\t    \"231221\": \"望奎县\",\n\t    \"231222\": \"兰西县\",\n\t    \"231223\": \"青冈县\",\n\t    \"231224\": \"庆安县\",\n\t    \"231225\": \"明水县\",\n\t    \"231226\": \"绥棱县\",\n\t    \"231281\": \"安达市\",\n\t    \"231282\": \"肇东市\",\n\t    \"231283\": \"海伦市\",\n\t    \"231284\": \"其它区\",\n\t    \"232700\": \"大兴安岭地区\",\n\t    \"232702\": \"松岭区\",\n\t    \"232703\": \"新林区\",\n\t    \"232704\": \"呼中区\",\n\t    \"232721\": \"呼玛县\",\n\t    \"232722\": \"塔河县\",\n\t    \"232723\": \"漠河县\",\n\t    \"232724\": \"加格达奇区\",\n\t    \"232725\": \"其它区\",\n\t    \"310000\": \"上海\",\n\t    \"310100\": \"上海市\",\n\t    \"310101\": \"黄浦区\",\n\t    \"310104\": \"徐汇区\",\n\t    \"310105\": \"长宁区\",\n\t    \"310106\": \"静安区\",\n\t    \"310107\": \"普陀区\",\n\t    \"310108\": \"闸北区\",\n\t    \"310109\": \"虹口区\",\n\t    \"310110\": \"杨浦区\",\n\t    \"310112\": \"闵行区\",\n\t    \"310113\": \"宝山区\",\n\t    \"310114\": \"嘉定区\",\n\t    \"310115\": \"浦东新区\",\n\t    \"310116\": \"金山区\",\n\t    \"310117\": \"松江区\",\n\t    \"310118\": \"青浦区\",\n\t    \"310120\": \"奉贤区\",\n\t    \"310230\": \"崇明县\",\n\t    \"310231\": \"其它区\",\n\t    \"320000\": \"江苏省\",\n\t    \"320100\": \"南京市\",\n\t    \"320102\": \"玄武区\",\n\t    \"320104\": \"秦淮区\",\n\t    \"320105\": \"建邺区\",\n\t    \"320106\": \"鼓楼区\",\n\t    \"320111\": \"浦口区\",\n\t    \"320113\": \"栖霞区\",\n\t    \"320114\": \"雨花台区\",\n\t    \"320115\": \"江宁区\",\n\t    \"320116\": \"六合区\",\n\t    \"320124\": \"溧水区\",\n\t    \"320125\": \"高淳区\",\n\t    \"320126\": \"其它区\",\n\t    \"320200\": \"无锡市\",\n\t    \"320202\": \"崇安区\",\n\t    \"320203\": \"南长区\",\n\t    \"320204\": \"北塘区\",\n\t    \"320205\": \"锡山区\",\n\t    \"320206\": \"惠山区\",\n\t    \"320211\": \"滨湖区\",\n\t    \"320281\": \"江阴市\",\n\t    \"320282\": \"宜兴市\",\n\t    \"320297\": \"其它区\",\n\t    \"320300\": \"徐州市\",\n\t    \"320302\": \"鼓楼区\",\n\t    \"320303\": \"云龙区\",\n\t    \"320305\": \"贾汪区\",\n\t    \"320311\": \"泉山区\",\n\t    \"320321\": \"丰县\",\n\t    \"320322\": \"沛县\",\n\t    \"320323\": \"铜山区\",\n\t    \"320324\": \"睢宁县\",\n\t    \"320381\": \"新沂市\",\n\t    \"320382\": \"邳州市\",\n\t    \"320383\": \"其它区\",\n\t    \"320400\": \"常州市\",\n\t    \"320402\": \"天宁区\",\n\t    \"320404\": \"钟楼区\",\n\t    \"320405\": \"戚墅堰区\",\n\t    \"320411\": \"新北区\",\n\t    \"320412\": \"武进区\",\n\t    \"320481\": \"溧阳市\",\n\t    \"320482\": \"金坛市\",\n\t    \"320483\": \"其它区\",\n\t    \"320500\": \"苏州市\",\n\t    \"320505\": \"虎丘区\",\n\t    \"320506\": \"吴中区\",\n\t    \"320507\": \"相城区\",\n\t    \"320508\": \"姑苏区\",\n\t    \"320581\": \"常熟市\",\n\t    \"320582\": \"张家港市\",\n\t    \"320583\": \"昆山市\",\n\t    \"320584\": \"吴江区\",\n\t    \"320585\": \"太仓市\",\n\t    \"320596\": \"其它区\",\n\t    \"320600\": \"南通市\",\n\t    \"320602\": \"崇川区\",\n\t    \"320611\": \"港闸区\",\n\t    \"320612\": \"通州区\",\n\t    \"320621\": \"海安县\",\n\t    \"320623\": \"如东县\",\n\t    \"320681\": \"启东市\",\n\t    \"320682\": \"如皋市\",\n\t    \"320684\": \"海门市\",\n\t    \"320694\": \"其它区\",\n\t    \"320700\": \"连云港市\",\n\t    \"320703\": \"连云区\",\n\t    \"320705\": \"新浦区\",\n\t    \"320706\": \"海州区\",\n\t    \"320721\": \"赣榆县\",\n\t    \"320722\": \"东海县\",\n\t    \"320723\": \"灌云县\",\n\t    \"320724\": \"灌南县\",\n\t    \"320725\": \"其它区\",\n\t    \"320800\": \"淮安市\",\n\t    \"320802\": \"清河区\",\n\t    \"320803\": \"淮安区\",\n\t    \"320804\": \"淮阴区\",\n\t    \"320811\": \"清浦区\",\n\t    \"320826\": \"涟水县\",\n\t    \"320829\": \"洪泽县\",\n\t    \"320830\": \"盱眙县\",\n\t    \"320831\": \"金湖县\",\n\t    \"320832\": \"其它区\",\n\t    \"320900\": \"盐城市\",\n\t    \"320902\": \"亭湖区\",\n\t    \"320903\": \"盐都区\",\n\t    \"320921\": \"响水县\",\n\t    \"320922\": \"滨海县\",\n\t    \"320923\": \"阜宁县\",\n\t    \"320924\": \"射阳县\",\n\t    \"320925\": \"建湖县\",\n\t    \"320981\": \"东台市\",\n\t    \"320982\": \"大丰市\",\n\t    \"320983\": \"其它区\",\n\t    \"321000\": \"扬州市\",\n\t    \"321002\": \"广陵区\",\n\t    \"321003\": \"邗江区\",\n\t    \"321023\": \"宝应县\",\n\t    \"321081\": \"仪征市\",\n\t    \"321084\": \"高邮市\",\n\t    \"321088\": \"江都区\",\n\t    \"321093\": \"其它区\",\n\t    \"321100\": \"镇江市\",\n\t    \"321102\": \"京口区\",\n\t    \"321111\": \"润州区\",\n\t    \"321112\": \"丹徒区\",\n\t    \"321181\": \"丹阳市\",\n\t    \"321182\": \"扬中市\",\n\t    \"321183\": \"句容市\",\n\t    \"321184\": \"其它区\",\n\t    \"321200\": \"泰州市\",\n\t    \"321202\": \"海陵区\",\n\t    \"321203\": \"高港区\",\n\t    \"321281\": \"兴化市\",\n\t    \"321282\": \"靖江市\",\n\t    \"321283\": \"泰兴市\",\n\t    \"321284\": \"姜堰区\",\n\t    \"321285\": \"其它区\",\n\t    \"321300\": \"宿迁市\",\n\t    \"321302\": \"宿城区\",\n\t    \"321311\": \"宿豫区\",\n\t    \"321322\": \"沭阳县\",\n\t    \"321323\": \"泗阳县\",\n\t    \"321324\": \"泗洪县\",\n\t    \"321325\": \"其它区\",\n\t    \"330000\": \"浙江省\",\n\t    \"330100\": \"杭州市\",\n\t    \"330102\": \"上城区\",\n\t    \"330103\": \"下城区\",\n\t    \"330104\": \"江干区\",\n\t    \"330105\": \"拱墅区\",\n\t    \"330106\": \"西湖区\",\n\t    \"330108\": \"滨江区\",\n\t    \"330109\": \"萧山区\",\n\t    \"330110\": \"余杭区\",\n\t    \"330122\": \"桐庐县\",\n\t    \"330127\": \"淳安县\",\n\t    \"330182\": \"建德市\",\n\t    \"330183\": \"富阳市\",\n\t    \"330185\": \"临安市\",\n\t    \"330186\": \"其它区\",\n\t    \"330200\": \"宁波市\",\n\t    \"330203\": \"海曙区\",\n\t    \"330204\": \"江东区\",\n\t    \"330205\": \"江北区\",\n\t    \"330206\": \"北仑区\",\n\t    \"330211\": \"镇海区\",\n\t    \"330212\": \"鄞州区\",\n\t    \"330225\": \"象山县\",\n\t    \"330226\": \"宁海县\",\n\t    \"330281\": \"余姚市\",\n\t    \"330282\": \"慈溪市\",\n\t    \"330283\": \"奉化市\",\n\t    \"330284\": \"其它区\",\n\t    \"330300\": \"温州市\",\n\t    \"330302\": \"鹿城区\",\n\t    \"330303\": \"龙湾区\",\n\t    \"330304\": \"瓯海区\",\n\t    \"330322\": \"洞头县\",\n\t    \"330324\": \"永嘉县\",\n\t    \"330326\": \"平阳县\",\n\t    \"330327\": \"苍南县\",\n\t    \"330328\": \"文成县\",\n\t    \"330329\": \"泰顺县\",\n\t    \"330381\": \"瑞安市\",\n\t    \"330382\": \"乐清市\",\n\t    \"330383\": \"其它区\",\n\t    \"330400\": \"嘉兴市\",\n\t    \"330402\": \"南湖区\",\n\t    \"330411\": \"秀洲区\",\n\t    \"330421\": \"嘉善县\",\n\t    \"330424\": \"海盐县\",\n\t    \"330481\": \"海宁市\",\n\t    \"330482\": \"平湖市\",\n\t    \"330483\": \"桐乡市\",\n\t    \"330484\": \"其它区\",\n\t    \"330500\": \"湖州市\",\n\t    \"330502\": \"吴兴区\",\n\t    \"330503\": \"南浔区\",\n\t    \"330521\": \"德清县\",\n\t    \"330522\": \"长兴县\",\n\t    \"330523\": \"安吉县\",\n\t    \"330524\": \"其它区\",\n\t    \"330600\": \"绍兴市\",\n\t    \"330602\": \"越城区\",\n\t    \"330621\": \"绍兴县\",\n\t    \"330624\": \"新昌县\",\n\t    \"330681\": \"诸暨市\",\n\t    \"330682\": \"上虞市\",\n\t    \"330683\": \"嵊州市\",\n\t    \"330684\": \"其它区\",\n\t    \"330700\": \"金华市\",\n\t    \"330702\": \"婺城区\",\n\t    \"330703\": \"金东区\",\n\t    \"330723\": \"武义县\",\n\t    \"330726\": \"浦江县\",\n\t    \"330727\": \"磐安县\",\n\t    \"330781\": \"兰溪市\",\n\t    \"330782\": \"义乌市\",\n\t    \"330783\": \"东阳市\",\n\t    \"330784\": \"永康市\",\n\t    \"330785\": \"其它区\",\n\t    \"330800\": \"衢州市\",\n\t    \"330802\": \"柯城区\",\n\t    \"330803\": \"衢江区\",\n\t    \"330822\": \"常山县\",\n\t    \"330824\": \"开化县\",\n\t    \"330825\": \"龙游县\",\n\t    \"330881\": \"江山市\",\n\t    \"330882\": \"其它区\",\n\t    \"330900\": \"舟山市\",\n\t    \"330902\": \"定海区\",\n\t    \"330903\": \"普陀区\",\n\t    \"330921\": \"岱山县\",\n\t    \"330922\": \"嵊泗县\",\n\t    \"330923\": \"其它区\",\n\t    \"331000\": \"台州市\",\n\t    \"331002\": \"椒江区\",\n\t    \"331003\": \"黄岩区\",\n\t    \"331004\": \"路桥区\",\n\t    \"331021\": \"玉环县\",\n\t    \"331022\": \"三门县\",\n\t    \"331023\": \"天台县\",\n\t    \"331024\": \"仙居县\",\n\t    \"331081\": \"温岭市\",\n\t    \"331082\": \"临海市\",\n\t    \"331083\": \"其它区\",\n\t    \"331100\": \"丽水市\",\n\t    \"331102\": \"莲都区\",\n\t    \"331121\": \"青田县\",\n\t    \"331122\": \"缙云县\",\n\t    \"331123\": \"遂昌县\",\n\t    \"331124\": \"松阳县\",\n\t    \"331125\": \"云和县\",\n\t    \"331126\": \"庆元县\",\n\t    \"331127\": \"景宁畲族自治县\",\n\t    \"331181\": \"龙泉市\",\n\t    \"331182\": \"其它区\",\n\t    \"340000\": \"安徽省\",\n\t    \"340100\": \"合肥市\",\n\t    \"340102\": \"瑶海区\",\n\t    \"340103\": \"庐阳区\",\n\t    \"340104\": \"蜀山区\",\n\t    \"340111\": \"包河区\",\n\t    \"340121\": \"长丰县\",\n\t    \"340122\": \"肥东县\",\n\t    \"340123\": \"肥西县\",\n\t    \"340192\": \"其它区\",\n\t    \"340200\": \"芜湖市\",\n\t    \"340202\": \"镜湖区\",\n\t    \"340203\": \"弋江区\",\n\t    \"340207\": \"鸠江区\",\n\t    \"340208\": \"三山区\",\n\t    \"340221\": \"芜湖县\",\n\t    \"340222\": \"繁昌县\",\n\t    \"340223\": \"南陵县\",\n\t    \"340224\": \"其它区\",\n\t    \"340300\": \"蚌埠市\",\n\t    \"340302\": \"龙子湖区\",\n\t    \"340303\": \"蚌山区\",\n\t    \"340304\": \"禹会区\",\n\t    \"340311\": \"淮上区\",\n\t    \"340321\": \"怀远县\",\n\t    \"340322\": \"五河县\",\n\t    \"340323\": \"固镇县\",\n\t    \"340324\": \"其它区\",\n\t    \"340400\": \"淮南市\",\n\t    \"340402\": \"大通区\",\n\t    \"340403\": \"田家庵区\",\n\t    \"340404\": \"谢家集区\",\n\t    \"340405\": \"八公山区\",\n\t    \"340406\": \"潘集区\",\n\t    \"340421\": \"凤台县\",\n\t    \"340422\": \"其它区\",\n\t    \"340500\": \"马鞍山市\",\n\t    \"340503\": \"花山区\",\n\t    \"340504\": \"雨山区\",\n\t    \"340506\": \"博望区\",\n\t    \"340521\": \"当涂县\",\n\t    \"340522\": \"其它区\",\n\t    \"340600\": \"淮北市\",\n\t    \"340602\": \"杜集区\",\n\t    \"340603\": \"相山区\",\n\t    \"340604\": \"烈山区\",\n\t    \"340621\": \"濉溪县\",\n\t    \"340622\": \"其它区\",\n\t    \"340700\": \"铜陵市\",\n\t    \"340702\": \"铜官山区\",\n\t    \"340703\": \"狮子山区\",\n\t    \"340711\": \"郊区\",\n\t    \"340721\": \"铜陵县\",\n\t    \"340722\": \"其它区\",\n\t    \"340800\": \"安庆市\",\n\t    \"340802\": \"迎江区\",\n\t    \"340803\": \"大观区\",\n\t    \"340811\": \"宜秀区\",\n\t    \"340822\": \"怀宁县\",\n\t    \"340823\": \"枞阳县\",\n\t    \"340824\": \"潜山县\",\n\t    \"340825\": \"太湖县\",\n\t    \"340826\": \"宿松县\",\n\t    \"340827\": \"望江县\",\n\t    \"340828\": \"岳西县\",\n\t    \"340881\": \"桐城市\",\n\t    \"340882\": \"其它区\",\n\t    \"341000\": \"黄山市\",\n\t    \"341002\": \"屯溪区\",\n\t    \"341003\": \"黄山区\",\n\t    \"341004\": \"徽州区\",\n\t    \"341021\": \"歙县\",\n\t    \"341022\": \"休宁县\",\n\t    \"341023\": \"黟县\",\n\t    \"341024\": \"祁门县\",\n\t    \"341025\": \"其它区\",\n\t    \"341100\": \"滁州市\",\n\t    \"341102\": \"琅琊区\",\n\t    \"341103\": \"南谯区\",\n\t    \"341122\": \"来安县\",\n\t    \"341124\": \"全椒县\",\n\t    \"341125\": \"定远县\",\n\t    \"341126\": \"凤阳县\",\n\t    \"341181\": \"天长市\",\n\t    \"341182\": \"明光市\",\n\t    \"341183\": \"其它区\",\n\t    \"341200\": \"阜阳市\",\n\t    \"341202\": \"颍州区\",\n\t    \"341203\": \"颍东区\",\n\t    \"341204\": \"颍泉区\",\n\t    \"341221\": \"临泉县\",\n\t    \"341222\": \"太和县\",\n\t    \"341225\": \"阜南县\",\n\t    \"341226\": \"颍上县\",\n\t    \"341282\": \"界首市\",\n\t    \"341283\": \"其它区\",\n\t    \"341300\": \"宿州市\",\n\t    \"341302\": \"埇桥区\",\n\t    \"341321\": \"砀山县\",\n\t    \"341322\": \"萧县\",\n\t    \"341323\": \"灵璧县\",\n\t    \"341324\": \"泗县\",\n\t    \"341325\": \"其它区\",\n\t    \"341400\": \"巢湖市\",\n\t    \"341421\": \"庐江县\",\n\t    \"341422\": \"无为县\",\n\t    \"341423\": \"含山县\",\n\t    \"341424\": \"和县\",\n\t    \"341500\": \"六安市\",\n\t    \"341502\": \"金安区\",\n\t    \"341503\": \"裕安区\",\n\t    \"341521\": \"寿县\",\n\t    \"341522\": \"霍邱县\",\n\t    \"341523\": \"舒城县\",\n\t    \"341524\": \"金寨县\",\n\t    \"341525\": \"霍山县\",\n\t    \"341526\": \"其它区\",\n\t    \"341600\": \"亳州市\",\n\t    \"341602\": \"谯城区\",\n\t    \"341621\": \"涡阳县\",\n\t    \"341622\": \"蒙城县\",\n\t    \"341623\": \"利辛县\",\n\t    \"341624\": \"其它区\",\n\t    \"341700\": \"池州市\",\n\t    \"341702\": \"贵池区\",\n\t    \"341721\": \"东至县\",\n\t    \"341722\": \"石台县\",\n\t    \"341723\": \"青阳县\",\n\t    \"341724\": \"其它区\",\n\t    \"341800\": \"宣城市\",\n\t    \"341802\": \"宣州区\",\n\t    \"341821\": \"郎溪县\",\n\t    \"341822\": \"广德县\",\n\t    \"341823\": \"泾县\",\n\t    \"341824\": \"绩溪县\",\n\t    \"341825\": \"旌德县\",\n\t    \"341881\": \"宁国市\",\n\t    \"341882\": \"其它区\",\n\t    \"350000\": \"福建省\",\n\t    \"350100\": \"福州市\",\n\t    \"350102\": \"鼓楼区\",\n\t    \"350103\": \"台江区\",\n\t    \"350104\": \"仓山区\",\n\t    \"350105\": \"马尾区\",\n\t    \"350111\": \"晋安区\",\n\t    \"350121\": \"闽侯县\",\n\t    \"350122\": \"连江县\",\n\t    \"350123\": \"罗源县\",\n\t    \"350124\": \"闽清县\",\n\t    \"350125\": \"永泰县\",\n\t    \"350128\": \"平潭县\",\n\t    \"350181\": \"福清市\",\n\t    \"350182\": \"长乐市\",\n\t    \"350183\": \"其它区\",\n\t    \"350200\": \"厦门市\",\n\t    \"350203\": \"思明区\",\n\t    \"350205\": \"海沧区\",\n\t    \"350206\": \"湖里区\",\n\t    \"350211\": \"集美区\",\n\t    \"350212\": \"同安区\",\n\t    \"350213\": \"翔安区\",\n\t    \"350214\": \"其它区\",\n\t    \"350300\": \"莆田市\",\n\t    \"350302\": \"城厢区\",\n\t    \"350303\": \"涵江区\",\n\t    \"350304\": \"荔城区\",\n\t    \"350305\": \"秀屿区\",\n\t    \"350322\": \"仙游县\",\n\t    \"350323\": \"其它区\",\n\t    \"350400\": \"三明市\",\n\t    \"350402\": \"梅列区\",\n\t    \"350403\": \"三元区\",\n\t    \"350421\": \"明溪县\",\n\t    \"350423\": \"清流县\",\n\t    \"350424\": \"宁化县\",\n\t    \"350425\": \"大田县\",\n\t    \"350426\": \"尤溪县\",\n\t    \"350427\": \"沙县\",\n\t    \"350428\": \"将乐县\",\n\t    \"350429\": \"泰宁县\",\n\t    \"350430\": \"建宁县\",\n\t    \"350481\": \"永安市\",\n\t    \"350482\": \"其它区\",\n\t    \"350500\": \"泉州市\",\n\t    \"350502\": \"鲤城区\",\n\t    \"350503\": \"丰泽区\",\n\t    \"350504\": \"洛江区\",\n\t    \"350505\": \"泉港区\",\n\t    \"350521\": \"惠安县\",\n\t    \"350524\": \"安溪县\",\n\t    \"350525\": \"永春县\",\n\t    \"350526\": \"德化县\",\n\t    \"350527\": \"金门县\",\n\t    \"350581\": \"石狮市\",\n\t    \"350582\": \"晋江市\",\n\t    \"350583\": \"南安市\",\n\t    \"350584\": \"其它区\",\n\t    \"350600\": \"漳州市\",\n\t    \"350602\": \"芗城区\",\n\t    \"350603\": \"龙文区\",\n\t    \"350622\": \"云霄县\",\n\t    \"350623\": \"漳浦县\",\n\t    \"350624\": \"诏安县\",\n\t    \"350625\": \"长泰县\",\n\t    \"350626\": \"东山县\",\n\t    \"350627\": \"南靖县\",\n\t    \"350628\": \"平和县\",\n\t    \"350629\": \"华安县\",\n\t    \"350681\": \"龙海市\",\n\t    \"350682\": \"其它区\",\n\t    \"350700\": \"南平市\",\n\t    \"350702\": \"延平区\",\n\t    \"350721\": \"顺昌县\",\n\t    \"350722\": \"浦城县\",\n\t    \"350723\": \"光泽县\",\n\t    \"350724\": \"松溪县\",\n\t    \"350725\": \"政和县\",\n\t    \"350781\": \"邵武市\",\n\t    \"350782\": \"武夷山市\",\n\t    \"350783\": \"建瓯市\",\n\t    \"350784\": \"建阳市\",\n\t    \"350785\": \"其它区\",\n\t    \"350800\": \"龙岩市\",\n\t    \"350802\": \"新罗区\",\n\t    \"350821\": \"长汀县\",\n\t    \"350822\": \"永定县\",\n\t    \"350823\": \"上杭县\",\n\t    \"350824\": \"武平县\",\n\t    \"350825\": \"连城县\",\n\t    \"350881\": \"漳平市\",\n\t    \"350882\": \"其它区\",\n\t    \"350900\": \"宁德市\",\n\t    \"350902\": \"蕉城区\",\n\t    \"350921\": \"霞浦县\",\n\t    \"350922\": \"古田县\",\n\t    \"350923\": \"屏南县\",\n\t    \"350924\": \"寿宁县\",\n\t    \"350925\": \"周宁县\",\n\t    \"350926\": \"柘荣县\",\n\t    \"350981\": \"福安市\",\n\t    \"350982\": \"福鼎市\",\n\t    \"350983\": \"其它区\",\n\t    \"360000\": \"江西省\",\n\t    \"360100\": \"南昌市\",\n\t    \"360102\": \"东湖区\",\n\t    \"360103\": \"西湖区\",\n\t    \"360104\": \"青云谱区\",\n\t    \"360105\": \"湾里区\",\n\t    \"360111\": \"青山湖区\",\n\t    \"360121\": \"南昌县\",\n\t    \"360122\": \"新建县\",\n\t    \"360123\": \"安义县\",\n\t    \"360124\": \"进贤县\",\n\t    \"360128\": \"其它区\",\n\t    \"360200\": \"景德镇市\",\n\t    \"360202\": \"昌江区\",\n\t    \"360203\": \"珠山区\",\n\t    \"360222\": \"浮梁县\",\n\t    \"360281\": \"乐平市\",\n\t    \"360282\": \"其它区\",\n\t    \"360300\": \"萍乡市\",\n\t    \"360302\": \"安源区\",\n\t    \"360313\": \"湘东区\",\n\t    \"360321\": \"莲花县\",\n\t    \"360322\": \"上栗县\",\n\t    \"360323\": \"芦溪县\",\n\t    \"360324\": \"其它区\",\n\t    \"360400\": \"九江市\",\n\t    \"360402\": \"庐山区\",\n\t    \"360403\": \"浔阳区\",\n\t    \"360421\": \"九江县\",\n\t    \"360423\": \"武宁县\",\n\t    \"360424\": \"修水县\",\n\t    \"360425\": \"永修县\",\n\t    \"360426\": \"德安县\",\n\t    \"360427\": \"星子县\",\n\t    \"360428\": \"都昌县\",\n\t    \"360429\": \"湖口县\",\n\t    \"360430\": \"彭泽县\",\n\t    \"360481\": \"瑞昌市\",\n\t    \"360482\": \"其它区\",\n\t    \"360483\": \"共青城市\",\n\t    \"360500\": \"新余市\",\n\t    \"360502\": \"渝水区\",\n\t    \"360521\": \"分宜县\",\n\t    \"360522\": \"其它区\",\n\t    \"360600\": \"鹰潭市\",\n\t    \"360602\": \"月湖区\",\n\t    \"360622\": \"余江县\",\n\t    \"360681\": \"贵溪市\",\n\t    \"360682\": \"其它区\",\n\t    \"360700\": \"赣州市\",\n\t    \"360702\": \"章贡区\",\n\t    \"360721\": \"赣县\",\n\t    \"360722\": \"信丰县\",\n\t    \"360723\": \"大余县\",\n\t    \"360724\": \"上犹县\",\n\t    \"360725\": \"崇义县\",\n\t    \"360726\": \"安远县\",\n\t    \"360727\": \"龙南县\",\n\t    \"360728\": \"定南县\",\n\t    \"360729\": \"全南县\",\n\t    \"360730\": \"宁都县\",\n\t    \"360731\": \"于都县\",\n\t    \"360732\": \"兴国县\",\n\t    \"360733\": \"会昌县\",\n\t    \"360734\": \"寻乌县\",\n\t    \"360735\": \"石城县\",\n\t    \"360781\": \"瑞金市\",\n\t    \"360782\": \"南康市\",\n\t    \"360783\": \"其它区\",\n\t    \"360800\": \"吉安市\",\n\t    \"360802\": \"吉州区\",\n\t    \"360803\": \"青原区\",\n\t    \"360821\": \"吉安县\",\n\t    \"360822\": \"吉水县\",\n\t    \"360823\": \"峡江县\",\n\t    \"360824\": \"新干县\",\n\t    \"360825\": \"永丰县\",\n\t    \"360826\": \"泰和县\",\n\t    \"360827\": \"遂川县\",\n\t    \"360828\": \"万安县\",\n\t    \"360829\": \"安福县\",\n\t    \"360830\": \"永新县\",\n\t    \"360881\": \"井冈山市\",\n\t    \"360882\": \"其它区\",\n\t    \"360900\": \"宜春市\",\n\t    \"360902\": \"袁州区\",\n\t    \"360921\": \"奉新县\",\n\t    \"360922\": \"万载县\",\n\t    \"360923\": \"上高县\",\n\t    \"360924\": \"宜丰县\",\n\t    \"360925\": \"靖安县\",\n\t    \"360926\": \"铜鼓县\",\n\t    \"360981\": \"丰城市\",\n\t    \"360982\": \"樟树市\",\n\t    \"360983\": \"高安市\",\n\t    \"360984\": \"其它区\",\n\t    \"361000\": \"抚州市\",\n\t    \"361002\": \"临川区\",\n\t    \"361021\": \"南城县\",\n\t    \"361022\": \"黎川县\",\n\t    \"361023\": \"南丰县\",\n\t    \"361024\": \"崇仁县\",\n\t    \"361025\": \"乐安县\",\n\t    \"361026\": \"宜黄县\",\n\t    \"361027\": \"金溪县\",\n\t    \"361028\": \"资溪县\",\n\t    \"361029\": \"东乡县\",\n\t    \"361030\": \"广昌县\",\n\t    \"361031\": \"其它区\",\n\t    \"361100\": \"上饶市\",\n\t    \"361102\": \"信州区\",\n\t    \"361121\": \"上饶县\",\n\t    \"361122\": \"广丰县\",\n\t    \"361123\": \"玉山县\",\n\t    \"361124\": \"铅山县\",\n\t    \"361125\": \"横峰县\",\n\t    \"361126\": \"弋阳县\",\n\t    \"361127\": \"余干县\",\n\t    \"361128\": \"鄱阳县\",\n\t    \"361129\": \"万年县\",\n\t    \"361130\": \"婺源县\",\n\t    \"361181\": \"德兴市\",\n\t    \"361182\": \"其它区\",\n\t    \"370000\": \"山东省\",\n\t    \"370100\": \"济南市\",\n\t    \"370102\": \"历下区\",\n\t    \"370103\": \"市中区\",\n\t    \"370104\": \"槐荫区\",\n\t    \"370105\": \"天桥区\",\n\t    \"370112\": \"历城区\",\n\t    \"370113\": \"长清区\",\n\t    \"370124\": \"平阴县\",\n\t    \"370125\": \"济阳县\",\n\t    \"370126\": \"商河县\",\n\t    \"370181\": \"章丘市\",\n\t    \"370182\": \"其它区\",\n\t    \"370200\": \"青岛市\",\n\t    \"370202\": \"市南区\",\n\t    \"370203\": \"市北区\",\n\t    \"370211\": \"黄岛区\",\n\t    \"370212\": \"崂山区\",\n\t    \"370213\": \"李沧区\",\n\t    \"370214\": \"城阳区\",\n\t    \"370281\": \"胶州市\",\n\t    \"370282\": \"即墨市\",\n\t    \"370283\": \"平度市\",\n\t    \"370285\": \"莱西市\",\n\t    \"370286\": \"其它区\",\n\t    \"370300\": \"淄博市\",\n\t    \"370302\": \"淄川区\",\n\t    \"370303\": \"张店区\",\n\t    \"370304\": \"博山区\",\n\t    \"370305\": \"临淄区\",\n\t    \"370306\": \"周村区\",\n\t    \"370321\": \"桓台县\",\n\t    \"370322\": \"高青县\",\n\t    \"370323\": \"沂源县\",\n\t    \"370324\": \"其它区\",\n\t    \"370400\": \"枣庄市\",\n\t    \"370402\": \"市中区\",\n\t    \"370403\": \"薛城区\",\n\t    \"370404\": \"峄城区\",\n\t    \"370405\": \"台儿庄区\",\n\t    \"370406\": \"山亭区\",\n\t    \"370481\": \"滕州市\",\n\t    \"370482\": \"其它区\",\n\t    \"370500\": \"东营市\",\n\t    \"370502\": \"东营区\",\n\t    \"370503\": \"河口区\",\n\t    \"370521\": \"垦利县\",\n\t    \"370522\": \"利津县\",\n\t    \"370523\": \"广饶县\",\n\t    \"370591\": \"其它区\",\n\t    \"370600\": \"烟台市\",\n\t    \"370602\": \"芝罘区\",\n\t    \"370611\": \"福山区\",\n\t    \"370612\": \"牟平区\",\n\t    \"370613\": \"莱山区\",\n\t    \"370634\": \"长岛县\",\n\t    \"370681\": \"龙口市\",\n\t    \"370682\": \"莱阳市\",\n\t    \"370683\": \"莱州市\",\n\t    \"370684\": \"蓬莱市\",\n\t    \"370685\": \"招远市\",\n\t    \"370686\": \"栖霞市\",\n\t    \"370687\": \"海阳市\",\n\t    \"370688\": \"其它区\",\n\t    \"370700\": \"潍坊市\",\n\t    \"370702\": \"潍城区\",\n\t    \"370703\": \"寒亭区\",\n\t    \"370704\": \"坊子区\",\n\t    \"370705\": \"奎文区\",\n\t    \"370724\": \"临朐县\",\n\t    \"370725\": \"昌乐县\",\n\t    \"370781\": \"青州市\",\n\t    \"370782\": \"诸城市\",\n\t    \"370783\": \"寿光市\",\n\t    \"370784\": \"安丘市\",\n\t    \"370785\": \"高密市\",\n\t    \"370786\": \"昌邑市\",\n\t    \"370787\": \"其它区\",\n\t    \"370800\": \"济宁市\",\n\t    \"370802\": \"市中区\",\n\t    \"370811\": \"任城区\",\n\t    \"370826\": \"微山县\",\n\t    \"370827\": \"鱼台县\",\n\t    \"370828\": \"金乡县\",\n\t    \"370829\": \"嘉祥县\",\n\t    \"370830\": \"汶上县\",\n\t    \"370831\": \"泗水县\",\n\t    \"370832\": \"梁山县\",\n\t    \"370881\": \"曲阜市\",\n\t    \"370882\": \"兖州市\",\n\t    \"370883\": \"邹城市\",\n\t    \"370884\": \"其它区\",\n\t    \"370900\": \"泰安市\",\n\t    \"370902\": \"泰山区\",\n\t    \"370903\": \"岱岳区\",\n\t    \"370921\": \"宁阳县\",\n\t    \"370923\": \"东平县\",\n\t    \"370982\": \"新泰市\",\n\t    \"370983\": \"肥城市\",\n\t    \"370984\": \"其它区\",\n\t    \"371000\": \"威海市\",\n\t    \"371002\": \"环翠区\",\n\t    \"371081\": \"文登市\",\n\t    \"371082\": \"荣成市\",\n\t    \"371083\": \"乳山市\",\n\t    \"371084\": \"其它区\",\n\t    \"371100\": \"日照市\",\n\t    \"371102\": \"东港区\",\n\t    \"371103\": \"岚山区\",\n\t    \"371121\": \"五莲县\",\n\t    \"371122\": \"莒县\",\n\t    \"371123\": \"其它区\",\n\t    \"371200\": \"莱芜市\",\n\t    \"371202\": \"莱城区\",\n\t    \"371203\": \"钢城区\",\n\t    \"371204\": \"其它区\",\n\t    \"371300\": \"临沂市\",\n\t    \"371302\": \"兰山区\",\n\t    \"371311\": \"罗庄区\",\n\t    \"371312\": \"河东区\",\n\t    \"371321\": \"沂南县\",\n\t    \"371322\": \"郯城县\",\n\t    \"371323\": \"沂水县\",\n\t    \"371324\": \"苍山县\",\n\t    \"371325\": \"费县\",\n\t    \"371326\": \"平邑县\",\n\t    \"371327\": \"莒南县\",\n\t    \"371328\": \"蒙阴县\",\n\t    \"371329\": \"临沭县\",\n\t    \"371330\": \"其它区\",\n\t    \"371400\": \"德州市\",\n\t    \"371402\": \"德城区\",\n\t    \"371421\": \"陵县\",\n\t    \"371422\": \"宁津县\",\n\t    \"371423\": \"庆云县\",\n\t    \"371424\": \"临邑县\",\n\t    \"371425\": \"齐河县\",\n\t    \"371426\": \"平原县\",\n\t    \"371427\": \"夏津县\",\n\t    \"371428\": \"武城县\",\n\t    \"371481\": \"乐陵市\",\n\t    \"371482\": \"禹城市\",\n\t    \"371483\": \"其它区\",\n\t    \"371500\": \"聊城市\",\n\t    \"371502\": \"东昌府区\",\n\t    \"371521\": \"阳谷县\",\n\t    \"371522\": \"莘县\",\n\t    \"371523\": \"茌平县\",\n\t    \"371524\": \"东阿县\",\n\t    \"371525\": \"冠县\",\n\t    \"371526\": \"高唐县\",\n\t    \"371581\": \"临清市\",\n\t    \"371582\": \"其它区\",\n\t    \"371600\": \"滨州市\",\n\t    \"371602\": \"滨城区\",\n\t    \"371621\": \"惠民县\",\n\t    \"371622\": \"阳信县\",\n\t    \"371623\": \"无棣县\",\n\t    \"371624\": \"沾化县\",\n\t    \"371625\": \"博兴县\",\n\t    \"371626\": \"邹平县\",\n\t    \"371627\": \"其它区\",\n\t    \"371700\": \"菏泽市\",\n\t    \"371702\": \"牡丹区\",\n\t    \"371721\": \"曹县\",\n\t    \"371722\": \"单县\",\n\t    \"371723\": \"成武县\",\n\t    \"371724\": \"巨野县\",\n\t    \"371725\": \"郓城县\",\n\t    \"371726\": \"鄄城县\",\n\t    \"371727\": \"定陶县\",\n\t    \"371728\": \"东明县\",\n\t    \"371729\": \"其它区\",\n\t    \"410000\": \"河南省\",\n\t    \"410100\": \"郑州市\",\n\t    \"410102\": \"中原区\",\n\t    \"410103\": \"二七区\",\n\t    \"410104\": \"管城回族区\",\n\t    \"410105\": \"金水区\",\n\t    \"410106\": \"上街区\",\n\t    \"410108\": \"惠济区\",\n\t    \"410122\": \"中牟县\",\n\t    \"410181\": \"巩义市\",\n\t    \"410182\": \"荥阳市\",\n\t    \"410183\": \"新密市\",\n\t    \"410184\": \"新郑市\",\n\t    \"410185\": \"登封市\",\n\t    \"410188\": \"其它区\",\n\t    \"410200\": \"开封市\",\n\t    \"410202\": \"龙亭区\",\n\t    \"410203\": \"顺河回族区\",\n\t    \"410204\": \"鼓楼区\",\n\t    \"410205\": \"禹王台区\",\n\t    \"410211\": \"金明区\",\n\t    \"410221\": \"杞县\",\n\t    \"410222\": \"通许县\",\n\t    \"410223\": \"尉氏县\",\n\t    \"410224\": \"开封县\",\n\t    \"410225\": \"兰考县\",\n\t    \"410226\": \"其它区\",\n\t    \"410300\": \"洛阳市\",\n\t    \"410302\": \"老城区\",\n\t    \"410303\": \"西工区\",\n\t    \"410304\": \"瀍河回族区\",\n\t    \"410305\": \"涧西区\",\n\t    \"410306\": \"吉利区\",\n\t    \"410307\": \"洛龙区\",\n\t    \"410322\": \"孟津县\",\n\t    \"410323\": \"新安县\",\n\t    \"410324\": \"栾川县\",\n\t    \"410325\": \"嵩县\",\n\t    \"410326\": \"汝阳县\",\n\t    \"410327\": \"宜阳县\",\n\t    \"410328\": \"洛宁县\",\n\t    \"410329\": \"伊川县\",\n\t    \"410381\": \"偃师市\",\n\t    \"410400\": \"平顶山市\",\n\t    \"410402\": \"新华区\",\n\t    \"410403\": \"卫东区\",\n\t    \"410404\": \"石龙区\",\n\t    \"410411\": \"湛河区\",\n\t    \"410421\": \"宝丰县\",\n\t    \"410422\": \"叶县\",\n\t    \"410423\": \"鲁山县\",\n\t    \"410425\": \"郏县\",\n\t    \"410481\": \"舞钢市\",\n\t    \"410482\": \"汝州市\",\n\t    \"410483\": \"其它区\",\n\t    \"410500\": \"安阳市\",\n\t    \"410502\": \"文峰区\",\n\t    \"410503\": \"北关区\",\n\t    \"410505\": \"殷都区\",\n\t    \"410506\": \"龙安区\",\n\t    \"410522\": \"安阳县\",\n\t    \"410523\": \"汤阴县\",\n\t    \"410526\": \"滑县\",\n\t    \"410527\": \"内黄县\",\n\t    \"410581\": \"林州市\",\n\t    \"410582\": \"其它区\",\n\t    \"410600\": \"鹤壁市\",\n\t    \"410602\": \"鹤山区\",\n\t    \"410603\": \"山城区\",\n\t    \"410611\": \"淇滨区\",\n\t    \"410621\": \"浚县\",\n\t    \"410622\": \"淇县\",\n\t    \"410623\": \"其它区\",\n\t    \"410700\": \"新乡市\",\n\t    \"410702\": \"红旗区\",\n\t    \"410703\": \"卫滨区\",\n\t    \"410704\": \"凤泉区\",\n\t    \"410711\": \"牧野区\",\n\t    \"410721\": \"新乡县\",\n\t    \"410724\": \"获嘉县\",\n\t    \"410725\": \"原阳县\",\n\t    \"410726\": \"延津县\",\n\t    \"410727\": \"封丘县\",\n\t    \"410728\": \"长垣县\",\n\t    \"410781\": \"卫辉市\",\n\t    \"410782\": \"辉县市\",\n\t    \"410783\": \"其它区\",\n\t    \"410800\": \"焦作市\",\n\t    \"410802\": \"解放区\",\n\t    \"410803\": \"中站区\",\n\t    \"410804\": \"马村区\",\n\t    \"410811\": \"山阳区\",\n\t    \"410821\": \"修武县\",\n\t    \"410822\": \"博爱县\",\n\t    \"410823\": \"武陟县\",\n\t    \"410825\": \"温县\",\n\t    \"410881\": \"济源市\",\n\t    \"410882\": \"沁阳市\",\n\t    \"410883\": \"孟州市\",\n\t    \"410884\": \"其它区\",\n\t    \"410900\": \"濮阳市\",\n\t    \"410902\": \"华龙区\",\n\t    \"410922\": \"清丰县\",\n\t    \"410923\": \"南乐县\",\n\t    \"410926\": \"范县\",\n\t    \"410927\": \"台前县\",\n\t    \"410928\": \"濮阳县\",\n\t    \"410929\": \"其它区\",\n\t    \"411000\": \"许昌市\",\n\t    \"411002\": \"魏都区\",\n\t    \"411023\": \"许昌县\",\n\t    \"411024\": \"鄢陵县\",\n\t    \"411025\": \"襄城县\",\n\t    \"411081\": \"禹州市\",\n\t    \"411082\": \"长葛市\",\n\t    \"411083\": \"其它区\",\n\t    \"411100\": \"漯河市\",\n\t    \"411102\": \"源汇区\",\n\t    \"411103\": \"郾城区\",\n\t    \"411104\": \"召陵区\",\n\t    \"411121\": \"舞阳县\",\n\t    \"411122\": \"临颍县\",\n\t    \"411123\": \"其它区\",\n\t    \"411200\": \"三门峡市\",\n\t    \"411202\": \"湖滨区\",\n\t    \"411221\": \"渑池县\",\n\t    \"411222\": \"陕县\",\n\t    \"411224\": \"卢氏县\",\n\t    \"411281\": \"义马市\",\n\t    \"411282\": \"灵宝市\",\n\t    \"411283\": \"其它区\",\n\t    \"411300\": \"南阳市\",\n\t    \"411302\": \"宛城区\",\n\t    \"411303\": \"卧龙区\",\n\t    \"411321\": \"南召县\",\n\t    \"411322\": \"方城县\",\n\t    \"411323\": \"西峡县\",\n\t    \"411324\": \"镇平县\",\n\t    \"411325\": \"内乡县\",\n\t    \"411326\": \"淅川县\",\n\t    \"411327\": \"社旗县\",\n\t    \"411328\": \"唐河县\",\n\t    \"411329\": \"新野县\",\n\t    \"411330\": \"桐柏县\",\n\t    \"411381\": \"邓州市\",\n\t    \"411382\": \"其它区\",\n\t    \"411400\": \"商丘市\",\n\t    \"411402\": \"梁园区\",\n\t    \"411403\": \"睢阳区\",\n\t    \"411421\": \"民权县\",\n\t    \"411422\": \"睢县\",\n\t    \"411423\": \"宁陵县\",\n\t    \"411424\": \"柘城县\",\n\t    \"411425\": \"虞城县\",\n\t    \"411426\": \"夏邑县\",\n\t    \"411481\": \"永城市\",\n\t    \"411482\": \"其它区\",\n\t    \"411500\": \"信阳市\",\n\t    \"411502\": \"浉河区\",\n\t    \"411503\": \"平桥区\",\n\t    \"411521\": \"罗山县\",\n\t    \"411522\": \"光山县\",\n\t    \"411523\": \"新县\",\n\t    \"411524\": \"商城县\",\n\t    \"411525\": \"固始县\",\n\t    \"411526\": \"潢川县\",\n\t    \"411527\": \"淮滨县\",\n\t    \"411528\": \"息县\",\n\t    \"411529\": \"其它区\",\n\t    \"411600\": \"周口市\",\n\t    \"411602\": \"川汇区\",\n\t    \"411621\": \"扶沟县\",\n\t    \"411622\": \"西华县\",\n\t    \"411623\": \"商水县\",\n\t    \"411624\": \"沈丘县\",\n\t    \"411625\": \"郸城县\",\n\t    \"411626\": \"淮阳县\",\n\t    \"411627\": \"太康县\",\n\t    \"411628\": \"鹿邑县\",\n\t    \"411681\": \"项城市\",\n\t    \"411682\": \"其它区\",\n\t    \"411700\": \"驻马店市\",\n\t    \"411702\": \"驿城区\",\n\t    \"411721\": \"西平县\",\n\t    \"411722\": \"上蔡县\",\n\t    \"411723\": \"平舆县\",\n\t    \"411724\": \"正阳县\",\n\t    \"411725\": \"确山县\",\n\t    \"411726\": \"泌阳县\",\n\t    \"411727\": \"汝南县\",\n\t    \"411728\": \"遂平县\",\n\t    \"411729\": \"新蔡县\",\n\t    \"411730\": \"其它区\",\n\t    \"420000\": \"湖北省\",\n\t    \"420100\": \"武汉市\",\n\t    \"420102\": \"江岸区\",\n\t    \"420103\": \"江汉区\",\n\t    \"420104\": \"硚口区\",\n\t    \"420105\": \"汉阳区\",\n\t    \"420106\": \"武昌区\",\n\t    \"420107\": \"青山区\",\n\t    \"420111\": \"洪山区\",\n\t    \"420112\": \"东西湖区\",\n\t    \"420113\": \"汉南区\",\n\t    \"420114\": \"蔡甸区\",\n\t    \"420115\": \"江夏区\",\n\t    \"420116\": \"黄陂区\",\n\t    \"420117\": \"新洲区\",\n\t    \"420118\": \"其它区\",\n\t    \"420200\": \"黄石市\",\n\t    \"420202\": \"黄石港区\",\n\t    \"420203\": \"西塞山区\",\n\t    \"420204\": \"下陆区\",\n\t    \"420205\": \"铁山区\",\n\t    \"420222\": \"阳新县\",\n\t    \"420281\": \"大冶市\",\n\t    \"420282\": \"其它区\",\n\t    \"420300\": \"十堰市\",\n\t    \"420302\": \"茅箭区\",\n\t    \"420303\": \"张湾区\",\n\t    \"420321\": \"郧县\",\n\t    \"420322\": \"郧西县\",\n\t    \"420323\": \"竹山县\",\n\t    \"420324\": \"竹溪县\",\n\t    \"420325\": \"房县\",\n\t    \"420381\": \"丹江口市\",\n\t    \"420383\": \"其它区\",\n\t    \"420500\": \"宜昌市\",\n\t    \"420502\": \"西陵区\",\n\t    \"420503\": \"伍家岗区\",\n\t    \"420504\": \"点军区\",\n\t    \"420505\": \"猇亭区\",\n\t    \"420506\": \"夷陵区\",\n\t    \"420525\": \"远安县\",\n\t    \"420526\": \"兴山县\",\n\t    \"420527\": \"秭归县\",\n\t    \"420528\": \"长阳土家族自治县\",\n\t    \"420529\": \"五峰土家族自治县\",\n\t    \"420581\": \"宜都市\",\n\t    \"420582\": \"当阳市\",\n\t    \"420583\": \"枝江市\",\n\t    \"420584\": \"其它区\",\n\t    \"420600\": \"襄阳市\",\n\t    \"420602\": \"襄城区\",\n\t    \"420606\": \"樊城区\",\n\t    \"420607\": \"襄州区\",\n\t    \"420624\": \"南漳县\",\n\t    \"420625\": \"谷城县\",\n\t    \"420626\": \"保康县\",\n\t    \"420682\": \"老河口市\",\n\t    \"420683\": \"枣阳市\",\n\t    \"420684\": \"宜城市\",\n\t    \"420685\": \"其它区\",\n\t    \"420700\": \"鄂州市\",\n\t    \"420702\": \"梁子湖区\",\n\t    \"420703\": \"华容区\",\n\t    \"420704\": \"鄂城区\",\n\t    \"420705\": \"其它区\",\n\t    \"420800\": \"荆门市\",\n\t    \"420802\": \"东宝区\",\n\t    \"420804\": \"掇刀区\",\n\t    \"420821\": \"京山县\",\n\t    \"420822\": \"沙洋县\",\n\t    \"420881\": \"钟祥市\",\n\t    \"420882\": \"其它区\",\n\t    \"420900\": \"孝感市\",\n\t    \"420902\": \"孝南区\",\n\t    \"420921\": \"孝昌县\",\n\t    \"420922\": \"大悟县\",\n\t    \"420923\": \"云梦县\",\n\t    \"420981\": \"应城市\",\n\t    \"420982\": \"安陆市\",\n\t    \"420984\": \"汉川市\",\n\t    \"420985\": \"其它区\",\n\t    \"421000\": \"荆州市\",\n\t    \"421002\": \"沙市区\",\n\t    \"421003\": \"荆州区\",\n\t    \"421022\": \"公安县\",\n\t    \"421023\": \"监利县\",\n\t    \"421024\": \"江陵县\",\n\t    \"421081\": \"石首市\",\n\t    \"421083\": \"洪湖市\",\n\t    \"421087\": \"松滋市\",\n\t    \"421088\": \"其它区\",\n\t    \"421100\": \"黄冈市\",\n\t    \"421102\": \"黄州区\",\n\t    \"421121\": \"团风县\",\n\t    \"421122\": \"红安县\",\n\t    \"421123\": \"罗田县\",\n\t    \"421124\": \"英山县\",\n\t    \"421125\": \"浠水县\",\n\t    \"421126\": \"蕲春县\",\n\t    \"421127\": \"黄梅县\",\n\t    \"421181\": \"麻城市\",\n\t    \"421182\": \"武穴市\",\n\t    \"421183\": \"其它区\",\n\t    \"421200\": \"咸宁市\",\n\t    \"421202\": \"咸安区\",\n\t    \"421221\": \"嘉鱼县\",\n\t    \"421222\": \"通城县\",\n\t    \"421223\": \"崇阳县\",\n\t    \"421224\": \"通山县\",\n\t    \"421281\": \"赤壁市\",\n\t    \"421283\": \"其它区\",\n\t    \"421300\": \"随州市\",\n\t    \"421302\": \"曾都区\",\n\t    \"421321\": \"随县\",\n\t    \"421381\": \"广水市\",\n\t    \"421382\": \"其它区\",\n\t    \"422800\": \"恩施土家族苗族自治州\",\n\t    \"422801\": \"恩施市\",\n\t    \"422802\": \"利川市\",\n\t    \"422822\": \"建始县\",\n\t    \"422823\": \"巴东县\",\n\t    \"422825\": \"宣恩县\",\n\t    \"422826\": \"咸丰县\",\n\t    \"422827\": \"来凤县\",\n\t    \"422828\": \"鹤峰县\",\n\t    \"422829\": \"其它区\",\n\t    \"429004\": \"仙桃市\",\n\t    \"429005\": \"潜江市\",\n\t    \"429006\": \"天门市\",\n\t    \"429021\": \"神农架林区\",\n\t    \"430000\": \"湖南省\",\n\t    \"430100\": \"长沙市\",\n\t    \"430102\": \"芙蓉区\",\n\t    \"430103\": \"天心区\",\n\t    \"430104\": \"岳麓区\",\n\t    \"430105\": \"开福区\",\n\t    \"430111\": \"雨花区\",\n\t    \"430121\": \"长沙县\",\n\t    \"430122\": \"望城区\",\n\t    \"430124\": \"宁乡县\",\n\t    \"430181\": \"浏阳市\",\n\t    \"430182\": \"其它区\",\n\t    \"430200\": \"株洲市\",\n\t    \"430202\": \"荷塘区\",\n\t    \"430203\": \"芦淞区\",\n\t    \"430204\": \"石峰区\",\n\t    \"430211\": \"天元区\",\n\t    \"430221\": \"株洲县\",\n\t    \"430223\": \"攸县\",\n\t    \"430224\": \"茶陵县\",\n\t    \"430225\": \"炎陵县\",\n\t    \"430281\": \"醴陵市\",\n\t    \"430282\": \"其它区\",\n\t    \"430300\": \"湘潭市\",\n\t    \"430302\": \"雨湖区\",\n\t    \"430304\": \"岳塘区\",\n\t    \"430321\": \"湘潭县\",\n\t    \"430381\": \"湘乡市\",\n\t    \"430382\": \"韶山市\",\n\t    \"430383\": \"其它区\",\n\t    \"430400\": \"衡阳市\",\n\t    \"430405\": \"珠晖区\",\n\t    \"430406\": \"雁峰区\",\n\t    \"430407\": \"石鼓区\",\n\t    \"430408\": \"蒸湘区\",\n\t    \"430412\": \"南岳区\",\n\t    \"430421\": \"衡阳县\",\n\t    \"430422\": \"衡南县\",\n\t    \"430423\": \"衡山县\",\n\t    \"430424\": \"衡东县\",\n\t    \"430426\": \"祁东县\",\n\t    \"430481\": \"耒阳市\",\n\t    \"430482\": \"常宁市\",\n\t    \"430483\": \"其它区\",\n\t    \"430500\": \"邵阳市\",\n\t    \"430502\": \"双清区\",\n\t    \"430503\": \"大祥区\",\n\t    \"430511\": \"北塔区\",\n\t    \"430521\": \"邵东县\",\n\t    \"430522\": \"新邵县\",\n\t    \"430523\": \"邵阳县\",\n\t    \"430524\": \"隆回县\",\n\t    \"430525\": \"洞口县\",\n\t    \"430527\": \"绥宁县\",\n\t    \"430528\": \"新宁县\",\n\t    \"430529\": \"城步苗族自治县\",\n\t    \"430581\": \"武冈市\",\n\t    \"430582\": \"其它区\",\n\t    \"430600\": \"岳阳市\",\n\t    \"430602\": \"岳阳楼区\",\n\t    \"430603\": \"云溪区\",\n\t    \"430611\": \"君山区\",\n\t    \"430621\": \"岳阳县\",\n\t    \"430623\": \"华容县\",\n\t    \"430624\": \"湘阴县\",\n\t    \"430626\": \"平江县\",\n\t    \"430681\": \"汨罗市\",\n\t    \"430682\": \"临湘市\",\n\t    \"430683\": \"其它区\",\n\t    \"430700\": \"常德市\",\n\t    \"430702\": \"武陵区\",\n\t    \"430703\": \"鼎城区\",\n\t    \"430721\": \"安乡县\",\n\t    \"430722\": \"汉寿县\",\n\t    \"430723\": \"澧县\",\n\t    \"430724\": \"临澧县\",\n\t    \"430725\": \"桃源县\",\n\t    \"430726\": \"石门县\",\n\t    \"430781\": \"津市市\",\n\t    \"430782\": \"其它区\",\n\t    \"430800\": \"张家界市\",\n\t    \"430802\": \"永定区\",\n\t    \"430811\": \"武陵源区\",\n\t    \"430821\": \"慈利县\",\n\t    \"430822\": \"桑植县\",\n\t    \"430823\": \"其它区\",\n\t    \"430900\": \"益阳市\",\n\t    \"430902\": \"资阳区\",\n\t    \"430903\": \"赫山区\",\n\t    \"430921\": \"南县\",\n\t    \"430922\": \"桃江县\",\n\t    \"430923\": \"安化县\",\n\t    \"430981\": \"沅江市\",\n\t    \"430982\": \"其它区\",\n\t    \"431000\": \"郴州市\",\n\t    \"431002\": \"北湖区\",\n\t    \"431003\": \"苏仙区\",\n\t    \"431021\": \"桂阳县\",\n\t    \"431022\": \"宜章县\",\n\t    \"431023\": \"永兴县\",\n\t    \"431024\": \"嘉禾县\",\n\t    \"431025\": \"临武县\",\n\t    \"431026\": \"汝城县\",\n\t    \"431027\": \"桂东县\",\n\t    \"431028\": \"安仁县\",\n\t    \"431081\": \"资兴市\",\n\t    \"431082\": \"其它区\",\n\t    \"431100\": \"永州市\",\n\t    \"431102\": \"零陵区\",\n\t    \"431103\": \"冷水滩区\",\n\t    \"431121\": \"祁阳县\",\n\t    \"431122\": \"东安县\",\n\t    \"431123\": \"双牌县\",\n\t    \"431124\": \"道县\",\n\t    \"431125\": \"江永县\",\n\t    \"431126\": \"宁远县\",\n\t    \"431127\": \"蓝山县\",\n\t    \"431128\": \"新田县\",\n\t    \"431129\": \"江华瑶族自治县\",\n\t    \"431130\": \"其它区\",\n\t    \"431200\": \"怀化市\",\n\t    \"431202\": \"鹤城区\",\n\t    \"431221\": \"中方县\",\n\t    \"431222\": \"沅陵县\",\n\t    \"431223\": \"辰溪县\",\n\t    \"431224\": \"溆浦县\",\n\t    \"431225\": \"会同县\",\n\t    \"431226\": \"麻阳苗族自治县\",\n\t    \"431227\": \"新晃侗族自治县\",\n\t    \"431228\": \"芷江侗族自治县\",\n\t    \"431229\": \"靖州苗族侗族自治县\",\n\t    \"431230\": \"通道侗族自治县\",\n\t    \"431281\": \"洪江市\",\n\t    \"431282\": \"其它区\",\n\t    \"431300\": \"娄底市\",\n\t    \"431302\": \"娄星区\",\n\t    \"431321\": \"双峰县\",\n\t    \"431322\": \"新化县\",\n\t    \"431381\": \"冷水江市\",\n\t    \"431382\": \"涟源市\",\n\t    \"431383\": \"其它区\",\n\t    \"433100\": \"湘西土家族苗族自治州\",\n\t    \"433101\": \"吉首市\",\n\t    \"433122\": \"泸溪县\",\n\t    \"433123\": \"凤凰县\",\n\t    \"433124\": \"花垣县\",\n\t    \"433125\": \"保靖县\",\n\t    \"433126\": \"古丈县\",\n\t    \"433127\": \"永顺县\",\n\t    \"433130\": \"龙山县\",\n\t    \"433131\": \"其它区\",\n\t    \"440000\": \"广东省\",\n\t    \"440100\": \"广州市\",\n\t    \"440103\": \"荔湾区\",\n\t    \"440104\": \"越秀区\",\n\t    \"440105\": \"海珠区\",\n\t    \"440106\": \"天河区\",\n\t    \"440111\": \"白云区\",\n\t    \"440112\": \"黄埔区\",\n\t    \"440113\": \"番禺区\",\n\t    \"440114\": \"花都区\",\n\t    \"440115\": \"南沙区\",\n\t    \"440116\": \"萝岗区\",\n\t    \"440183\": \"增城市\",\n\t    \"440184\": \"从化市\",\n\t    \"440189\": \"其它区\",\n\t    \"440200\": \"韶关市\",\n\t    \"440203\": \"武江区\",\n\t    \"440204\": \"浈江区\",\n\t    \"440205\": \"曲江区\",\n\t    \"440222\": \"始兴县\",\n\t    \"440224\": \"仁化县\",\n\t    \"440229\": \"翁源县\",\n\t    \"440232\": \"乳源瑶族自治县\",\n\t    \"440233\": \"新丰县\",\n\t    \"440281\": \"乐昌市\",\n\t    \"440282\": \"南雄市\",\n\t    \"440283\": \"其它区\",\n\t    \"440300\": \"深圳市\",\n\t    \"440303\": \"罗湖区\",\n\t    \"440304\": \"福田区\",\n\t    \"440305\": \"南山区\",\n\t    \"440306\": \"宝安区\",\n\t    \"440307\": \"龙岗区\",\n\t    \"440308\": \"盐田区\",\n\t    \"440309\": \"其它区\",\n\t    \"440320\": \"光明新区\",\n\t    \"440321\": \"坪山新区\",\n\t    \"440322\": \"大鹏新区\",\n\t    \"440323\": \"龙华新区\",\n\t    \"440400\": \"珠海市\",\n\t    \"440402\": \"香洲区\",\n\t    \"440403\": \"斗门区\",\n\t    \"440404\": \"金湾区\",\n\t    \"440488\": \"其它区\",\n\t    \"440500\": \"汕头市\",\n\t    \"440507\": \"龙湖区\",\n\t    \"440511\": \"金平区\",\n\t    \"440512\": \"濠江区\",\n\t    \"440513\": \"潮阳区\",\n\t    \"440514\": \"潮南区\",\n\t    \"440515\": \"澄海区\",\n\t    \"440523\": \"南澳县\",\n\t    \"440524\": \"其它区\",\n\t    \"440600\": \"佛山市\",\n\t    \"440604\": \"禅城区\",\n\t    \"440605\": \"南海区\",\n\t    \"440606\": \"顺德区\",\n\t    \"440607\": \"三水区\",\n\t    \"440608\": \"高明区\",\n\t    \"440609\": \"其它区\",\n\t    \"440700\": \"江门市\",\n\t    \"440703\": \"蓬江区\",\n\t    \"440704\": \"江海区\",\n\t    \"440705\": \"新会区\",\n\t    \"440781\": \"台山市\",\n\t    \"440783\": \"开平市\",\n\t    \"440784\": \"鹤山市\",\n\t    \"440785\": \"恩平市\",\n\t    \"440786\": \"其它区\",\n\t    \"440800\": \"湛江市\",\n\t    \"440802\": \"赤坎区\",\n\t    \"440803\": \"霞山区\",\n\t    \"440804\": \"坡头区\",\n\t    \"440811\": \"麻章区\",\n\t    \"440823\": \"遂溪县\",\n\t    \"440825\": \"徐闻县\",\n\t    \"440881\": \"廉江市\",\n\t    \"440882\": \"雷州市\",\n\t    \"440883\": \"吴川市\",\n\t    \"440884\": \"其它区\",\n\t    \"440900\": \"茂名市\",\n\t    \"440902\": \"茂南区\",\n\t    \"440903\": \"茂港区\",\n\t    \"440923\": \"电白县\",\n\t    \"440981\": \"高州市\",\n\t    \"440982\": \"化州市\",\n\t    \"440983\": \"信宜市\",\n\t    \"440984\": \"其它区\",\n\t    \"441200\": \"肇庆市\",\n\t    \"441202\": \"端州区\",\n\t    \"441203\": \"鼎湖区\",\n\t    \"441223\": \"广宁县\",\n\t    \"441224\": \"怀集县\",\n\t    \"441225\": \"封开县\",\n\t    \"441226\": \"德庆县\",\n\t    \"441283\": \"高要市\",\n\t    \"441284\": \"四会市\",\n\t    \"441285\": \"其它区\",\n\t    \"441300\": \"惠州市\",\n\t    \"441302\": \"惠城区\",\n\t    \"441303\": \"惠阳区\",\n\t    \"441322\": \"博罗县\",\n\t    \"441323\": \"惠东县\",\n\t    \"441324\": \"龙门县\",\n\t    \"441325\": \"其它区\",\n\t    \"441400\": \"梅州市\",\n\t    \"441402\": \"梅江区\",\n\t    \"441421\": \"梅县\",\n\t    \"441422\": \"大埔县\",\n\t    \"441423\": \"丰顺县\",\n\t    \"441424\": \"五华县\",\n\t    \"441426\": \"平远县\",\n\t    \"441427\": \"蕉岭县\",\n\t    \"441481\": \"兴宁市\",\n\t    \"441482\": \"其它区\",\n\t    \"441500\": \"汕尾市\",\n\t    \"441502\": \"城区\",\n\t    \"441521\": \"海丰县\",\n\t    \"441523\": \"陆河县\",\n\t    \"441581\": \"陆丰市\",\n\t    \"441582\": \"其它区\",\n\t    \"441600\": \"河源市\",\n\t    \"441602\": \"源城区\",\n\t    \"441621\": \"紫金县\",\n\t    \"441622\": \"龙川县\",\n\t    \"441623\": \"连平县\",\n\t    \"441624\": \"和平县\",\n\t    \"441625\": \"东源县\",\n\t    \"441626\": \"其它区\",\n\t    \"441700\": \"阳江市\",\n\t    \"441702\": \"江城区\",\n\t    \"441721\": \"阳西县\",\n\t    \"441723\": \"阳东县\",\n\t    \"441781\": \"阳春市\",\n\t    \"441782\": \"其它区\",\n\t    \"441800\": \"清远市\",\n\t    \"441802\": \"清城区\",\n\t    \"441821\": \"佛冈县\",\n\t    \"441823\": \"阳山县\",\n\t    \"441825\": \"连山壮族瑶族自治县\",\n\t    \"441826\": \"连南瑶族自治县\",\n\t    \"441827\": \"清新区\",\n\t    \"441881\": \"英德市\",\n\t    \"441882\": \"连州市\",\n\t    \"441883\": \"其它区\",\n\t    \"441900\": \"东莞市\",\n\t    \"442000\": \"中山市\",\n\t    \"442101\": \"东沙群岛\",\n\t    \"445100\": \"潮州市\",\n\t    \"445102\": \"湘桥区\",\n\t    \"445121\": \"潮安区\",\n\t    \"445122\": \"饶平县\",\n\t    \"445186\": \"其它区\",\n\t    \"445200\": \"揭阳市\",\n\t    \"445202\": \"榕城区\",\n\t    \"445221\": \"揭东区\",\n\t    \"445222\": \"揭西县\",\n\t    \"445224\": \"惠来县\",\n\t    \"445281\": \"普宁市\",\n\t    \"445285\": \"其它区\",\n\t    \"445300\": \"云浮市\",\n\t    \"445302\": \"云城区\",\n\t    \"445321\": \"新兴县\",\n\t    \"445322\": \"郁南县\",\n\t    \"445323\": \"云安县\",\n\t    \"445381\": \"罗定市\",\n\t    \"445382\": \"其它区\",\n\t    \"450000\": \"广西壮族自治区\",\n\t    \"450100\": \"南宁市\",\n\t    \"450102\": \"兴宁区\",\n\t    \"450103\": \"青秀区\",\n\t    \"450105\": \"江南区\",\n\t    \"450107\": \"西乡塘区\",\n\t    \"450108\": \"良庆区\",\n\t    \"450109\": \"邕宁区\",\n\t    \"450122\": \"武鸣县\",\n\t    \"450123\": \"隆安县\",\n\t    \"450124\": \"马山县\",\n\t    \"450125\": \"上林县\",\n\t    \"450126\": \"宾阳县\",\n\t    \"450127\": \"横县\",\n\t    \"450128\": \"其它区\",\n\t    \"450200\": \"柳州市\",\n\t    \"450202\": \"城中区\",\n\t    \"450203\": \"鱼峰区\",\n\t    \"450204\": \"柳南区\",\n\t    \"450205\": \"柳北区\",\n\t    \"450221\": \"柳江县\",\n\t    \"450222\": \"柳城县\",\n\t    \"450223\": \"鹿寨县\",\n\t    \"450224\": \"融安县\",\n\t    \"450225\": \"融水苗族自治县\",\n\t    \"450226\": \"三江侗族自治县\",\n\t    \"450227\": \"其它区\",\n\t    \"450300\": \"桂林市\",\n\t    \"450302\": \"秀峰区\",\n\t    \"450303\": \"叠彩区\",\n\t    \"450304\": \"象山区\",\n\t    \"450305\": \"七星区\",\n\t    \"450311\": \"雁山区\",\n\t    \"450321\": \"阳朔县\",\n\t    \"450322\": \"临桂区\",\n\t    \"450323\": \"灵川县\",\n\t    \"450324\": \"全州县\",\n\t    \"450325\": \"兴安县\",\n\t    \"450326\": \"永福县\",\n\t    \"450327\": \"灌阳县\",\n\t    \"450328\": \"龙胜各族自治县\",\n\t    \"450329\": \"资源县\",\n\t    \"450330\": \"平乐县\",\n\t    \"450331\": \"荔浦县\",\n\t    \"450332\": \"恭城瑶族自治县\",\n\t    \"450333\": \"其它区\",\n\t    \"450400\": \"梧州市\",\n\t    \"450403\": \"万秀区\",\n\t    \"450405\": \"长洲区\",\n\t    \"450406\": \"龙圩区\",\n\t    \"450421\": \"苍梧县\",\n\t    \"450422\": \"藤县\",\n\t    \"450423\": \"蒙山县\",\n\t    \"450481\": \"岑溪市\",\n\t    \"450482\": \"其它区\",\n\t    \"450500\": \"北海市\",\n\t    \"450502\": \"海城区\",\n\t    \"450503\": \"银海区\",\n\t    \"450512\": \"铁山港区\",\n\t    \"450521\": \"合浦县\",\n\t    \"450522\": \"其它区\",\n\t    \"450600\": \"防城港市\",\n\t    \"450602\": \"港口区\",\n\t    \"450603\": \"防城区\",\n\t    \"450621\": \"上思县\",\n\t    \"450681\": \"东兴市\",\n\t    \"450682\": \"其它区\",\n\t    \"450700\": \"钦州市\",\n\t    \"450702\": \"钦南区\",\n\t    \"450703\": \"钦北区\",\n\t    \"450721\": \"灵山县\",\n\t    \"450722\": \"浦北县\",\n\t    \"450723\": \"其它区\",\n\t    \"450800\": \"贵港市\",\n\t    \"450802\": \"港北区\",\n\t    \"450803\": \"港南区\",\n\t    \"450804\": \"覃塘区\",\n\t    \"450821\": \"平南县\",\n\t    \"450881\": \"桂平市\",\n\t    \"450882\": \"其它区\",\n\t    \"450900\": \"玉林市\",\n\t    \"450902\": \"玉州区\",\n\t    \"450903\": \"福绵区\",\n\t    \"450921\": \"容县\",\n\t    \"450922\": \"陆川县\",\n\t    \"450923\": \"博白县\",\n\t    \"450924\": \"兴业县\",\n\t    \"450981\": \"北流市\",\n\t    \"450982\": \"其它区\",\n\t    \"451000\": \"百色市\",\n\t    \"451002\": \"右江区\",\n\t    \"451021\": \"田阳县\",\n\t    \"451022\": \"田东县\",\n\t    \"451023\": \"平果县\",\n\t    \"451024\": \"德保县\",\n\t    \"451025\": \"靖西县\",\n\t    \"451026\": \"那坡县\",\n\t    \"451027\": \"凌云县\",\n\t    \"451028\": \"乐业县\",\n\t    \"451029\": \"田林县\",\n\t    \"451030\": \"西林县\",\n\t    \"451031\": \"隆林各族自治县\",\n\t    \"451032\": \"其它区\",\n\t    \"451100\": \"贺州市\",\n\t    \"451102\": \"八步区\",\n\t    \"451119\": \"平桂管理区\",\n\t    \"451121\": \"昭平县\",\n\t    \"451122\": \"钟山县\",\n\t    \"451123\": \"富川瑶族自治县\",\n\t    \"451124\": \"其它区\",\n\t    \"451200\": \"河池市\",\n\t    \"451202\": \"金城江区\",\n\t    \"451221\": \"南丹县\",\n\t    \"451222\": \"天峨县\",\n\t    \"451223\": \"凤山县\",\n\t    \"451224\": \"东兰县\",\n\t    \"451225\": \"罗城仫佬族自治县\",\n\t    \"451226\": \"环江毛南族自治县\",\n\t    \"451227\": \"巴马瑶族自治县\",\n\t    \"451228\": \"都安瑶族自治县\",\n\t    \"451229\": \"大化瑶族自治县\",\n\t    \"451281\": \"宜州市\",\n\t    \"451282\": \"其它区\",\n\t    \"451300\": \"来宾市\",\n\t    \"451302\": \"兴宾区\",\n\t    \"451321\": \"忻城县\",\n\t    \"451322\": \"象州县\",\n\t    \"451323\": \"武宣县\",\n\t    \"451324\": \"金秀瑶族自治县\",\n\t    \"451381\": \"合山市\",\n\t    \"451382\": \"其它区\",\n\t    \"451400\": \"崇左市\",\n\t    \"451402\": \"江州区\",\n\t    \"451421\": \"扶绥县\",\n\t    \"451422\": \"宁明县\",\n\t    \"451423\": \"龙州县\",\n\t    \"451424\": \"大新县\",\n\t    \"451425\": \"天等县\",\n\t    \"451481\": \"凭祥市\",\n\t    \"451482\": \"其它区\",\n\t    \"460000\": \"海南省\",\n\t    \"460100\": \"海口市\",\n\t    \"460105\": \"秀英区\",\n\t    \"460106\": \"龙华区\",\n\t    \"460107\": \"琼山区\",\n\t    \"460108\": \"美兰区\",\n\t    \"460109\": \"其它区\",\n\t    \"460200\": \"三亚市\",\n\t    \"460300\": \"三沙市\",\n\t    \"460321\": \"西沙群岛\",\n\t    \"460322\": \"南沙群岛\",\n\t    \"460323\": \"中沙群岛的岛礁及其海域\",\n\t    \"469001\": \"五指山市\",\n\t    \"469002\": \"琼海市\",\n\t    \"469003\": \"儋州市\",\n\t    \"469005\": \"文昌市\",\n\t    \"469006\": \"万宁市\",\n\t    \"469007\": \"东方市\",\n\t    \"469025\": \"定安县\",\n\t    \"469026\": \"屯昌县\",\n\t    \"469027\": \"澄迈县\",\n\t    \"469028\": \"临高县\",\n\t    \"469030\": \"白沙黎族自治县\",\n\t    \"469031\": \"昌江黎族自治县\",\n\t    \"469033\": \"乐东黎族自治县\",\n\t    \"469034\": \"陵水黎族自治县\",\n\t    \"469035\": \"保亭黎族苗族自治县\",\n\t    \"469036\": \"琼中黎族苗族自治县\",\n\t    \"471005\": \"其它区\",\n\t    \"500000\": \"重庆\",\n\t    \"500100\": \"重庆市\",\n\t    \"500101\": \"万州区\",\n\t    \"500102\": \"涪陵区\",\n\t    \"500103\": \"渝中区\",\n\t    \"500104\": \"大渡口区\",\n\t    \"500105\": \"江北区\",\n\t    \"500106\": \"沙坪坝区\",\n\t    \"500107\": \"九龙坡区\",\n\t    \"500108\": \"南岸区\",\n\t    \"500109\": \"北碚区\",\n\t    \"500110\": \"万盛区\",\n\t    \"500111\": \"双桥区\",\n\t    \"500112\": \"渝北区\",\n\t    \"500113\": \"巴南区\",\n\t    \"500114\": \"黔江区\",\n\t    \"500115\": \"长寿区\",\n\t    \"500222\": \"綦江区\",\n\t    \"500223\": \"潼南县\",\n\t    \"500224\": \"铜梁县\",\n\t    \"500225\": \"大足区\",\n\t    \"500226\": \"荣昌县\",\n\t    \"500227\": \"璧山县\",\n\t    \"500228\": \"梁平县\",\n\t    \"500229\": \"城口县\",\n\t    \"500230\": \"丰都县\",\n\t    \"500231\": \"垫江县\",\n\t    \"500232\": \"武隆县\",\n\t    \"500233\": \"忠县\",\n\t    \"500234\": \"开县\",\n\t    \"500235\": \"云阳县\",\n\t    \"500236\": \"奉节县\",\n\t    \"500237\": \"巫山县\",\n\t    \"500238\": \"巫溪县\",\n\t    \"500240\": \"石柱土家族自治县\",\n\t    \"500241\": \"秀山土家族苗族自治县\",\n\t    \"500242\": \"酉阳土家族苗族自治县\",\n\t    \"500243\": \"彭水苗族土家族自治县\",\n\t    \"500381\": \"江津区\",\n\t    \"500382\": \"合川区\",\n\t    \"500383\": \"永川区\",\n\t    \"500384\": \"南川区\",\n\t    \"500385\": \"其它区\",\n\t    \"510000\": \"四川省\",\n\t    \"510100\": \"成都市\",\n\t    \"510104\": \"锦江区\",\n\t    \"510105\": \"青羊区\",\n\t    \"510106\": \"金牛区\",\n\t    \"510107\": \"武侯区\",\n\t    \"510108\": \"成华区\",\n\t    \"510112\": \"龙泉驿区\",\n\t    \"510113\": \"青白江区\",\n\t    \"510114\": \"新都区\",\n\t    \"510115\": \"温江区\",\n\t    \"510121\": \"金堂县\",\n\t    \"510122\": \"双流县\",\n\t    \"510124\": \"郫县\",\n\t    \"510129\": \"大邑县\",\n\t    \"510131\": \"蒲江县\",\n\t    \"510132\": \"新津县\",\n\t    \"510181\": \"都江堰市\",\n\t    \"510182\": \"彭州市\",\n\t    \"510183\": \"邛崃市\",\n\t    \"510184\": \"崇州市\",\n\t    \"510185\": \"其它区\",\n\t    \"510300\": \"自贡市\",\n\t    \"510302\": \"自流井区\",\n\t    \"510303\": \"贡井区\",\n\t    \"510304\": \"大安区\",\n\t    \"510311\": \"沿滩区\",\n\t    \"510321\": \"荣县\",\n\t    \"510322\": \"富顺县\",\n\t    \"510323\": \"其它区\",\n\t    \"510400\": \"攀枝花市\",\n\t    \"510402\": \"东区\",\n\t    \"510403\": \"西区\",\n\t    \"510411\": \"仁和区\",\n\t    \"510421\": \"米易县\",\n\t    \"510422\": \"盐边县\",\n\t    \"510423\": \"其它区\",\n\t    \"510500\": \"泸州市\",\n\t    \"510502\": \"江阳区\",\n\t    \"510503\": \"纳溪区\",\n\t    \"510504\": \"龙马潭区\",\n\t    \"510521\": \"泸县\",\n\t    \"510522\": \"合江县\",\n\t    \"510524\": \"叙永县\",\n\t    \"510525\": \"古蔺县\",\n\t    \"510526\": \"其它区\",\n\t    \"510600\": \"德阳市\",\n\t    \"510603\": \"旌阳区\",\n\t    \"510623\": \"中江县\",\n\t    \"510626\": \"罗江县\",\n\t    \"510681\": \"广汉市\",\n\t    \"510682\": \"什邡市\",\n\t    \"510683\": \"绵竹市\",\n\t    \"510684\": \"其它区\",\n\t    \"510700\": \"绵阳市\",\n\t    \"510703\": \"涪城区\",\n\t    \"510704\": \"游仙区\",\n\t    \"510722\": \"三台县\",\n\t    \"510723\": \"盐亭县\",\n\t    \"510724\": \"安县\",\n\t    \"510725\": \"梓潼县\",\n\t    \"510726\": \"北川羌族自治县\",\n\t    \"510727\": \"平武县\",\n\t    \"510781\": \"江油市\",\n\t    \"510782\": \"其它区\",\n\t    \"510800\": \"广元市\",\n\t    \"510802\": \"利州区\",\n\t    \"510811\": \"昭化区\",\n\t    \"510812\": \"朝天区\",\n\t    \"510821\": \"旺苍县\",\n\t    \"510822\": \"青川县\",\n\t    \"510823\": \"剑阁县\",\n\t    \"510824\": \"苍溪县\",\n\t    \"510825\": \"其它区\",\n\t    \"510900\": \"遂宁市\",\n\t    \"510903\": \"船山区\",\n\t    \"510904\": \"安居区\",\n\t    \"510921\": \"蓬溪县\",\n\t    \"510922\": \"射洪县\",\n\t    \"510923\": \"大英县\",\n\t    \"510924\": \"其它区\",\n\t    \"511000\": \"内江市\",\n\t    \"511002\": \"市中区\",\n\t    \"511011\": \"东兴区\",\n\t    \"511024\": \"威远县\",\n\t    \"511025\": \"资中县\",\n\t    \"511028\": \"隆昌县\",\n\t    \"511029\": \"其它区\",\n\t    \"511100\": \"乐山市\",\n\t    \"511102\": \"市中区\",\n\t    \"511111\": \"沙湾区\",\n\t    \"511112\": \"五通桥区\",\n\t    \"511113\": \"金口河区\",\n\t    \"511123\": \"犍为县\",\n\t    \"511124\": \"井研县\",\n\t    \"511126\": \"夹江县\",\n\t    \"511129\": \"沐川县\",\n\t    \"511132\": \"峨边彝族自治县\",\n\t    \"511133\": \"马边彝族自治县\",\n\t    \"511181\": \"峨眉山市\",\n\t    \"511182\": \"其它区\",\n\t    \"511300\": \"南充市\",\n\t    \"511302\": \"顺庆区\",\n\t    \"511303\": \"高坪区\",\n\t    \"511304\": \"嘉陵区\",\n\t    \"511321\": \"南部县\",\n\t    \"511322\": \"营山县\",\n\t    \"511323\": \"蓬安县\",\n\t    \"511324\": \"仪陇县\",\n\t    \"511325\": \"西充县\",\n\t    \"511381\": \"阆中市\",\n\t    \"511382\": \"其它区\",\n\t    \"511400\": \"眉山市\",\n\t    \"511402\": \"东坡区\",\n\t    \"511421\": \"仁寿县\",\n\t    \"511422\": \"彭山县\",\n\t    \"511423\": \"洪雅县\",\n\t    \"511424\": \"丹棱县\",\n\t    \"511425\": \"青神县\",\n\t    \"511426\": \"其它区\",\n\t    \"511500\": \"宜宾市\",\n\t    \"511502\": \"翠屏区\",\n\t    \"511521\": \"宜宾县\",\n\t    \"511522\": \"南溪区\",\n\t    \"511523\": \"江安县\",\n\t    \"511524\": \"长宁县\",\n\t    \"511525\": \"高县\",\n\t    \"511526\": \"珙县\",\n\t    \"511527\": \"筠连县\",\n\t    \"511528\": \"兴文县\",\n\t    \"511529\": \"屏山县\",\n\t    \"511530\": \"其它区\",\n\t    \"511600\": \"广安市\",\n\t    \"511602\": \"广安区\",\n\t    \"511603\": \"前锋区\",\n\t    \"511621\": \"岳池县\",\n\t    \"511622\": \"武胜县\",\n\t    \"511623\": \"邻水县\",\n\t    \"511681\": \"华蓥市\",\n\t    \"511683\": \"其它区\",\n\t    \"511700\": \"达州市\",\n\t    \"511702\": \"通川区\",\n\t    \"511721\": \"达川区\",\n\t    \"511722\": \"宣汉县\",\n\t    \"511723\": \"开江县\",\n\t    \"511724\": \"大竹县\",\n\t    \"511725\": \"渠县\",\n\t    \"511781\": \"万源市\",\n\t    \"511782\": \"其它区\",\n\t    \"511800\": \"雅安市\",\n\t    \"511802\": \"雨城区\",\n\t    \"511821\": \"名山区\",\n\t    \"511822\": \"荥经县\",\n\t    \"511823\": \"汉源县\",\n\t    \"511824\": \"石棉县\",\n\t    \"511825\": \"天全县\",\n\t    \"511826\": \"芦山县\",\n\t    \"511827\": \"宝兴县\",\n\t    \"511828\": \"其它区\",\n\t    \"511900\": \"巴中市\",\n\t    \"511902\": \"巴州区\",\n\t    \"511903\": \"恩阳区\",\n\t    \"511921\": \"通江县\",\n\t    \"511922\": \"南江县\",\n\t    \"511923\": \"平昌县\",\n\t    \"511924\": \"其它区\",\n\t    \"512000\": \"资阳市\",\n\t    \"512002\": \"雁江区\",\n\t    \"512021\": \"安岳县\",\n\t    \"512022\": \"乐至县\",\n\t    \"512081\": \"简阳市\",\n\t    \"512082\": \"其它区\",\n\t    \"513200\": \"阿坝藏族羌族自治州\",\n\t    \"513221\": \"汶川县\",\n\t    \"513222\": \"理县\",\n\t    \"513223\": \"茂县\",\n\t    \"513224\": \"松潘县\",\n\t    \"513225\": \"九寨沟县\",\n\t    \"513226\": \"金川县\",\n\t    \"513227\": \"小金县\",\n\t    \"513228\": \"黑水县\",\n\t    \"513229\": \"马尔康县\",\n\t    \"513230\": \"壤塘县\",\n\t    \"513231\": \"阿坝县\",\n\t    \"513232\": \"若尔盖县\",\n\t    \"513233\": \"红原县\",\n\t    \"513234\": \"其它区\",\n\t    \"513300\": \"甘孜藏族自治州\",\n\t    \"513321\": \"康定县\",\n\t    \"513322\": \"泸定县\",\n\t    \"513323\": \"丹巴县\",\n\t    \"513324\": \"九龙县\",\n\t    \"513325\": \"雅江县\",\n\t    \"513326\": \"道孚县\",\n\t    \"513327\": \"炉霍县\",\n\t    \"513328\": \"甘孜县\",\n\t    \"513329\": \"新龙县\",\n\t    \"513330\": \"德格县\",\n\t    \"513331\": \"白玉县\",\n\t    \"513332\": \"石渠县\",\n\t    \"513333\": \"色达县\",\n\t    \"513334\": \"理塘县\",\n\t    \"513335\": \"巴塘县\",\n\t    \"513336\": \"乡城县\",\n\t    \"513337\": \"稻城县\",\n\t    \"513338\": \"得荣县\",\n\t    \"513339\": \"其它区\",\n\t    \"513400\": \"凉山彝族自治州\",\n\t    \"513401\": \"西昌市\",\n\t    \"513422\": \"木里藏族自治县\",\n\t    \"513423\": \"盐源县\",\n\t    \"513424\": \"德昌县\",\n\t    \"513425\": \"会理县\",\n\t    \"513426\": \"会东县\",\n\t    \"513427\": \"宁南县\",\n\t    \"513428\": \"普格县\",\n\t    \"513429\": \"布拖县\",\n\t    \"513430\": \"金阳县\",\n\t    \"513431\": \"昭觉县\",\n\t    \"513432\": \"喜德县\",\n\t    \"513433\": \"冕宁县\",\n\t    \"513434\": \"越西县\",\n\t    \"513435\": \"甘洛县\",\n\t    \"513436\": \"美姑县\",\n\t    \"513437\": \"雷波县\",\n\t    \"513438\": \"其它区\",\n\t    \"520000\": \"贵州省\",\n\t    \"520100\": \"贵阳市\",\n\t    \"520102\": \"南明区\",\n\t    \"520103\": \"云岩区\",\n\t    \"520111\": \"花溪区\",\n\t    \"520112\": \"乌当区\",\n\t    \"520113\": \"白云区\",\n\t    \"520121\": \"开阳县\",\n\t    \"520122\": \"息烽县\",\n\t    \"520123\": \"修文县\",\n\t    \"520151\": \"观山湖区\",\n\t    \"520181\": \"清镇市\",\n\t    \"520182\": \"其它区\",\n\t    \"520200\": \"六盘水市\",\n\t    \"520201\": \"钟山区\",\n\t    \"520203\": \"六枝特区\",\n\t    \"520221\": \"水城县\",\n\t    \"520222\": \"盘县\",\n\t    \"520223\": \"其它区\",\n\t    \"520300\": \"遵义市\",\n\t    \"520302\": \"红花岗区\",\n\t    \"520303\": \"汇川区\",\n\t    \"520321\": \"遵义县\",\n\t    \"520322\": \"桐梓县\",\n\t    \"520323\": \"绥阳县\",\n\t    \"520324\": \"正安县\",\n\t    \"520325\": \"道真仡佬族苗族自治县\",\n\t    \"520326\": \"务川仡佬族苗族自治县\",\n\t    \"520327\": \"凤冈县\",\n\t    \"520328\": \"湄潭县\",\n\t    \"520329\": \"余庆县\",\n\t    \"520330\": \"习水县\",\n\t    \"520381\": \"赤水市\",\n\t    \"520382\": \"仁怀市\",\n\t    \"520383\": \"其它区\",\n\t    \"520400\": \"安顺市\",\n\t    \"520402\": \"西秀区\",\n\t    \"520421\": \"平坝县\",\n\t    \"520422\": \"普定县\",\n\t    \"520423\": \"镇宁布依族苗族自治县\",\n\t    \"520424\": \"关岭布依族苗族自治县\",\n\t    \"520425\": \"紫云苗族布依族自治县\",\n\t    \"520426\": \"其它区\",\n\t    \"522200\": \"铜仁市\",\n\t    \"522201\": \"碧江区\",\n\t    \"522222\": \"江口县\",\n\t    \"522223\": \"玉屏侗族自治县\",\n\t    \"522224\": \"石阡县\",\n\t    \"522225\": \"思南县\",\n\t    \"522226\": \"印江土家族苗族自治县\",\n\t    \"522227\": \"德江县\",\n\t    \"522228\": \"沿河土家族自治县\",\n\t    \"522229\": \"松桃苗族自治县\",\n\t    \"522230\": \"万山区\",\n\t    \"522231\": \"其它区\",\n\t    \"522300\": \"黔西南布依族苗族自治州\",\n\t    \"522301\": \"兴义市\",\n\t    \"522322\": \"兴仁县\",\n\t    \"522323\": \"普安县\",\n\t    \"522324\": \"晴隆县\",\n\t    \"522325\": \"贞丰县\",\n\t    \"522326\": \"望谟县\",\n\t    \"522327\": \"册亨县\",\n\t    \"522328\": \"安龙县\",\n\t    \"522329\": \"其它区\",\n\t    \"522400\": \"毕节市\",\n\t    \"522401\": \"七星关区\",\n\t    \"522422\": \"大方县\",\n\t    \"522423\": \"黔西县\",\n\t    \"522424\": \"金沙县\",\n\t    \"522425\": \"织金县\",\n\t    \"522426\": \"纳雍县\",\n\t    \"522427\": \"威宁彝族回族苗族自治县\",\n\t    \"522428\": \"赫章县\",\n\t    \"522429\": \"其它区\",\n\t    \"522600\": \"黔东南苗族侗族自治州\",\n\t    \"522601\": \"凯里市\",\n\t    \"522622\": \"黄平县\",\n\t    \"522623\": \"施秉县\",\n\t    \"522624\": \"三穗县\",\n\t    \"522625\": \"镇远县\",\n\t    \"522626\": \"岑巩县\",\n\t    \"522627\": \"天柱县\",\n\t    \"522628\": \"锦屏县\",\n\t    \"522629\": \"剑河县\",\n\t    \"522630\": \"台江县\",\n\t    \"522631\": \"黎平县\",\n\t    \"522632\": \"榕江县\",\n\t    \"522633\": \"从江县\",\n\t    \"522634\": \"雷山县\",\n\t    \"522635\": \"麻江县\",\n\t    \"522636\": \"丹寨县\",\n\t    \"522637\": \"其它区\",\n\t    \"522700\": \"黔南布依族苗族自治州\",\n\t    \"522701\": \"都匀市\",\n\t    \"522702\": \"福泉市\",\n\t    \"522722\": \"荔波县\",\n\t    \"522723\": \"贵定县\",\n\t    \"522725\": \"瓮安县\",\n\t    \"522726\": \"独山县\",\n\t    \"522727\": \"平塘县\",\n\t    \"522728\": \"罗甸县\",\n\t    \"522729\": \"长顺县\",\n\t    \"522730\": \"龙里县\",\n\t    \"522731\": \"惠水县\",\n\t    \"522732\": \"三都水族自治县\",\n\t    \"522733\": \"其它区\",\n\t    \"530000\": \"云南省\",\n\t    \"530100\": \"昆明市\",\n\t    \"530102\": \"五华区\",\n\t    \"530103\": \"盘龙区\",\n\t    \"530111\": \"官渡区\",\n\t    \"530112\": \"西山区\",\n\t    \"530113\": \"东川区\",\n\t    \"530121\": \"呈贡区\",\n\t    \"530122\": \"晋宁县\",\n\t    \"530124\": \"富民县\",\n\t    \"530125\": \"宜良县\",\n\t    \"530126\": \"石林彝族自治县\",\n\t    \"530127\": \"嵩明县\",\n\t    \"530128\": \"禄劝彝族苗族自治县\",\n\t    \"530129\": \"寻甸回族彝族自治县\",\n\t    \"530181\": \"安宁市\",\n\t    \"530182\": \"其它区\",\n\t    \"530300\": \"曲靖市\",\n\t    \"530302\": \"麒麟区\",\n\t    \"530321\": \"马龙县\",\n\t    \"530322\": \"陆良县\",\n\t    \"530323\": \"师宗县\",\n\t    \"530324\": \"罗平县\",\n\t    \"530325\": \"富源县\",\n\t    \"530326\": \"会泽县\",\n\t    \"530328\": \"沾益县\",\n\t    \"530381\": \"宣威市\",\n\t    \"530382\": \"其它区\",\n\t    \"530400\": \"玉溪市\",\n\t    \"530402\": \"红塔区\",\n\t    \"530421\": \"江川县\",\n\t    \"530422\": \"澄江县\",\n\t    \"530423\": \"通海县\",\n\t    \"530424\": \"华宁县\",\n\t    \"530425\": \"易门县\",\n\t    \"530426\": \"峨山彝族自治县\",\n\t    \"530427\": \"新平彝族傣族自治县\",\n\t    \"530428\": \"元江哈尼族彝族傣族自治县\",\n\t    \"530429\": \"其它区\",\n\t    \"530500\": \"保山市\",\n\t    \"530502\": \"隆阳区\",\n\t    \"530521\": \"施甸县\",\n\t    \"530522\": \"腾冲县\",\n\t    \"530523\": \"龙陵县\",\n\t    \"530524\": \"昌宁县\",\n\t    \"530525\": \"其它区\",\n\t    \"530600\": \"昭通市\",\n\t    \"530602\": \"昭阳区\",\n\t    \"530621\": \"鲁甸县\",\n\t    \"530622\": \"巧家县\",\n\t    \"530623\": \"盐津县\",\n\t    \"530624\": \"大关县\",\n\t    \"530625\": \"永善县\",\n\t    \"530626\": \"绥江县\",\n\t    \"530627\": \"镇雄县\",\n\t    \"530628\": \"彝良县\",\n\t    \"530629\": \"威信县\",\n\t    \"530630\": \"水富县\",\n\t    \"530631\": \"其它区\",\n\t    \"530700\": \"丽江市\",\n\t    \"530702\": \"古城区\",\n\t    \"530721\": \"玉龙纳西族自治县\",\n\t    \"530722\": \"永胜县\",\n\t    \"530723\": \"华坪县\",\n\t    \"530724\": \"宁蒗彝族自治县\",\n\t    \"530725\": \"其它区\",\n\t    \"530800\": \"普洱市\",\n\t    \"530802\": \"思茅区\",\n\t    \"530821\": \"宁洱哈尼族彝族自治县\",\n\t    \"530822\": \"墨江哈尼族自治县\",\n\t    \"530823\": \"景东彝族自治县\",\n\t    \"530824\": \"景谷傣族彝族自治县\",\n\t    \"530825\": \"镇沅彝族哈尼族拉祜族自治县\",\n\t    \"530826\": \"江城哈尼族彝族自治县\",\n\t    \"530827\": \"孟连傣族拉祜族佤族自治县\",\n\t    \"530828\": \"澜沧拉祜族自治县\",\n\t    \"530829\": \"西盟佤族自治县\",\n\t    \"530830\": \"其它区\",\n\t    \"530900\": \"临沧市\",\n\t    \"530902\": \"临翔区\",\n\t    \"530921\": \"凤庆县\",\n\t    \"530922\": \"云县\",\n\t    \"530923\": \"永德县\",\n\t    \"530924\": \"镇康县\",\n\t    \"530925\": \"双江拉祜族佤族布朗族傣族自治县\",\n\t    \"530926\": \"耿马傣族佤族自治县\",\n\t    \"530927\": \"沧源佤族自治县\",\n\t    \"530928\": \"其它区\",\n\t    \"532300\": \"楚雄彝族自治州\",\n\t    \"532301\": \"楚雄市\",\n\t    \"532322\": \"双柏县\",\n\t    \"532323\": \"牟定县\",\n\t    \"532324\": \"南华县\",\n\t    \"532325\": \"姚安县\",\n\t    \"532326\": \"大姚县\",\n\t    \"532327\": \"永仁县\",\n\t    \"532328\": \"元谋县\",\n\t    \"532329\": \"武定县\",\n\t    \"532331\": \"禄丰县\",\n\t    \"532332\": \"其它区\",\n\t    \"532500\": \"红河哈尼族彝族自治州\",\n\t    \"532501\": \"个旧市\",\n\t    \"532502\": \"开远市\",\n\t    \"532522\": \"蒙自市\",\n\t    \"532523\": \"屏边苗族自治县\",\n\t    \"532524\": \"建水县\",\n\t    \"532525\": \"石屏县\",\n\t    \"532526\": \"弥勒市\",\n\t    \"532527\": \"泸西县\",\n\t    \"532528\": \"元阳县\",\n\t    \"532529\": \"红河县\",\n\t    \"532530\": \"金平苗族瑶族傣族自治县\",\n\t    \"532531\": \"绿春县\",\n\t    \"532532\": \"河口瑶族自治县\",\n\t    \"532533\": \"其它区\",\n\t    \"532600\": \"文山壮族苗族自治州\",\n\t    \"532621\": \"文山市\",\n\t    \"532622\": \"砚山县\",\n\t    \"532623\": \"西畴县\",\n\t    \"532624\": \"麻栗坡县\",\n\t    \"532625\": \"马关县\",\n\t    \"532626\": \"丘北县\",\n\t    \"532627\": \"广南县\",\n\t    \"532628\": \"富宁县\",\n\t    \"532629\": \"其它区\",\n\t    \"532800\": \"西双版纳傣族自治州\",\n\t    \"532801\": \"景洪市\",\n\t    \"532822\": \"勐海县\",\n\t    \"532823\": \"勐腊县\",\n\t    \"532824\": \"其它区\",\n\t    \"532900\": \"大理白族自治州\",\n\t    \"532901\": \"大理市\",\n\t    \"532922\": \"漾濞彝族自治县\",\n\t    \"532923\": \"祥云县\",\n\t    \"532924\": \"宾川县\",\n\t    \"532925\": \"弥渡县\",\n\t    \"532926\": \"南涧彝族自治县\",\n\t    \"532927\": \"巍山彝族回族自治县\",\n\t    \"532928\": \"永平县\",\n\t    \"532929\": \"云龙县\",\n\t    \"532930\": \"洱源县\",\n\t    \"532931\": \"剑川县\",\n\t    \"532932\": \"鹤庆县\",\n\t    \"532933\": \"其它区\",\n\t    \"533100\": \"德宏傣族景颇族自治州\",\n\t    \"533102\": \"瑞丽市\",\n\t    \"533103\": \"芒市\",\n\t    \"533122\": \"梁河县\",\n\t    \"533123\": \"盈江县\",\n\t    \"533124\": \"陇川县\",\n\t    \"533125\": \"其它区\",\n\t    \"533300\": \"怒江傈僳族自治州\",\n\t    \"533321\": \"泸水县\",\n\t    \"533323\": \"福贡县\",\n\t    \"533324\": \"贡山独龙族怒族自治县\",\n\t    \"533325\": \"兰坪白族普米族自治县\",\n\t    \"533326\": \"其它区\",\n\t    \"533400\": \"迪庆藏族自治州\",\n\t    \"533421\": \"香格里拉县\",\n\t    \"533422\": \"德钦县\",\n\t    \"533423\": \"维西傈僳族自治县\",\n\t    \"533424\": \"其它区\",\n\t    \"540000\": \"西藏自治区\",\n\t    \"540100\": \"拉萨市\",\n\t    \"540102\": \"城关区\",\n\t    \"540121\": \"林周县\",\n\t    \"540122\": \"当雄县\",\n\t    \"540123\": \"尼木县\",\n\t    \"540124\": \"曲水县\",\n\t    \"540125\": \"堆龙德庆县\",\n\t    \"540126\": \"达孜县\",\n\t    \"540127\": \"墨竹工卡县\",\n\t    \"540128\": \"其它区\",\n\t    \"542100\": \"昌都地区\",\n\t    \"542121\": \"昌都县\",\n\t    \"542122\": \"江达县\",\n\t    \"542123\": \"贡觉县\",\n\t    \"542124\": \"类乌齐县\",\n\t    \"542125\": \"丁青县\",\n\t    \"542126\": \"察雅县\",\n\t    \"542127\": \"八宿县\",\n\t    \"542128\": \"左贡县\",\n\t    \"542129\": \"芒康县\",\n\t    \"542132\": \"洛隆县\",\n\t    \"542133\": \"边坝县\",\n\t    \"542134\": \"其它区\",\n\t    \"542200\": \"山南地区\",\n\t    \"542221\": \"乃东县\",\n\t    \"542222\": \"扎囊县\",\n\t    \"542223\": \"贡嘎县\",\n\t    \"542224\": \"桑日县\",\n\t    \"542225\": \"琼结县\",\n\t    \"542226\": \"曲松县\",\n\t    \"542227\": \"措美县\",\n\t    \"542228\": \"洛扎县\",\n\t    \"542229\": \"加查县\",\n\t    \"542231\": \"隆子县\",\n\t    \"542232\": \"错那县\",\n\t    \"542233\": \"浪卡子县\",\n\t    \"542234\": \"其它区\",\n\t    \"542300\": \"日喀则地区\",\n\t    \"542301\": \"日喀则市\",\n\t    \"542322\": \"南木林县\",\n\t    \"542323\": \"江孜县\",\n\t    \"542324\": \"定日县\",\n\t    \"542325\": \"萨迦县\",\n\t    \"542326\": \"拉孜县\",\n\t    \"542327\": \"昂仁县\",\n\t    \"542328\": \"谢通门县\",\n\t    \"542329\": \"白朗县\",\n\t    \"542330\": \"仁布县\",\n\t    \"542331\": \"康马县\",\n\t    \"542332\": \"定结县\",\n\t    \"542333\": \"仲巴县\",\n\t    \"542334\": \"亚东县\",\n\t    \"542335\": \"吉隆县\",\n\t    \"542336\": \"聂拉木县\",\n\t    \"542337\": \"萨嘎县\",\n\t    \"542338\": \"岗巴县\",\n\t    \"542339\": \"其它区\",\n\t    \"542400\": \"那曲地区\",\n\t    \"542421\": \"那曲县\",\n\t    \"542422\": \"嘉黎县\",\n\t    \"542423\": \"比如县\",\n\t    \"542424\": \"聂荣县\",\n\t    \"542425\": \"安多县\",\n\t    \"542426\": \"申扎县\",\n\t    \"542427\": \"索县\",\n\t    \"542428\": \"班戈县\",\n\t    \"542429\": \"巴青县\",\n\t    \"542430\": \"尼玛县\",\n\t    \"542431\": \"其它区\",\n\t    \"542432\": \"双湖县\",\n\t    \"542500\": \"阿里地区\",\n\t    \"542521\": \"普兰县\",\n\t    \"542522\": \"札达县\",\n\t    \"542523\": \"噶尔县\",\n\t    \"542524\": \"日土县\",\n\t    \"542525\": \"革吉县\",\n\t    \"542526\": \"改则县\",\n\t    \"542527\": \"措勤县\",\n\t    \"542528\": \"其它区\",\n\t    \"542600\": \"林芝地区\",\n\t    \"542621\": \"林芝县\",\n\t    \"542622\": \"工布江达县\",\n\t    \"542623\": \"米林县\",\n\t    \"542624\": \"墨脱县\",\n\t    \"542625\": \"波密县\",\n\t    \"542626\": \"察隅县\",\n\t    \"542627\": \"朗县\",\n\t    \"542628\": \"其它区\",\n\t    \"610000\": \"陕西省\",\n\t    \"610100\": \"西安市\",\n\t    \"610102\": \"新城区\",\n\t    \"610103\": \"碑林区\",\n\t    \"610104\": \"莲湖区\",\n\t    \"610111\": \"灞桥区\",\n\t    \"610112\": \"未央区\",\n\t    \"610113\": \"雁塔区\",\n\t    \"610114\": \"阎良区\",\n\t    \"610115\": \"临潼区\",\n\t    \"610116\": \"长安区\",\n\t    \"610122\": \"蓝田县\",\n\t    \"610124\": \"周至县\",\n\t    \"610125\": \"户县\",\n\t    \"610126\": \"高陵县\",\n\t    \"610127\": \"其它区\",\n\t    \"610200\": \"铜川市\",\n\t    \"610202\": \"王益区\",\n\t    \"610203\": \"印台区\",\n\t    \"610204\": \"耀州区\",\n\t    \"610222\": \"宜君县\",\n\t    \"610223\": \"其它区\",\n\t    \"610300\": \"宝鸡市\",\n\t    \"610302\": \"渭滨区\",\n\t    \"610303\": \"金台区\",\n\t    \"610304\": \"陈仓区\",\n\t    \"610322\": \"凤翔县\",\n\t    \"610323\": \"岐山县\",\n\t    \"610324\": \"扶风县\",\n\t    \"610326\": \"眉县\",\n\t    \"610327\": \"陇县\",\n\t    \"610328\": \"千阳县\",\n\t    \"610329\": \"麟游县\",\n\t    \"610330\": \"凤县\",\n\t    \"610331\": \"太白县\",\n\t    \"610332\": \"其它区\",\n\t    \"610400\": \"咸阳市\",\n\t    \"610402\": \"秦都区\",\n\t    \"610403\": \"杨陵区\",\n\t    \"610404\": \"渭城区\",\n\t    \"610422\": \"三原县\",\n\t    \"610423\": \"泾阳县\",\n\t    \"610424\": \"乾县\",\n\t    \"610425\": \"礼泉县\",\n\t    \"610426\": \"永寿县\",\n\t    \"610427\": \"彬县\",\n\t    \"610428\": \"长武县\",\n\t    \"610429\": \"旬邑县\",\n\t    \"610430\": \"淳化县\",\n\t    \"610431\": \"武功县\",\n\t    \"610481\": \"兴平市\",\n\t    \"610482\": \"其它区\",\n\t    \"610500\": \"渭南市\",\n\t    \"610502\": \"临渭区\",\n\t    \"610521\": \"华县\",\n\t    \"610522\": \"潼关县\",\n\t    \"610523\": \"大荔县\",\n\t    \"610524\": \"合阳县\",\n\t    \"610525\": \"澄城县\",\n\t    \"610526\": \"蒲城县\",\n\t    \"610527\": \"白水县\",\n\t    \"610528\": \"富平县\",\n\t    \"610581\": \"韩城市\",\n\t    \"610582\": \"华阴市\",\n\t    \"610583\": \"其它区\",\n\t    \"610600\": \"延安市\",\n\t    \"610602\": \"宝塔区\",\n\t    \"610621\": \"延长县\",\n\t    \"610622\": \"延川县\",\n\t    \"610623\": \"子长县\",\n\t    \"610624\": \"安塞县\",\n\t    \"610625\": \"志丹县\",\n\t    \"610626\": \"吴起县\",\n\t    \"610627\": \"甘泉县\",\n\t    \"610628\": \"富县\",\n\t    \"610629\": \"洛川县\",\n\t    \"610630\": \"宜川县\",\n\t    \"610631\": \"黄龙县\",\n\t    \"610632\": \"黄陵县\",\n\t    \"610633\": \"其它区\",\n\t    \"610700\": \"汉中市\",\n\t    \"610702\": \"汉台区\",\n\t    \"610721\": \"南郑县\",\n\t    \"610722\": \"城固县\",\n\t    \"610723\": \"洋县\",\n\t    \"610724\": \"西乡县\",\n\t    \"610725\": \"勉县\",\n\t    \"610726\": \"宁强县\",\n\t    \"610727\": \"略阳县\",\n\t    \"610728\": \"镇巴县\",\n\t    \"610729\": \"留坝县\",\n\t    \"610730\": \"佛坪县\",\n\t    \"610731\": \"其它区\",\n\t    \"610800\": \"榆林市\",\n\t    \"610802\": \"榆阳区\",\n\t    \"610821\": \"神木县\",\n\t    \"610822\": \"府谷县\",\n\t    \"610823\": \"横山县\",\n\t    \"610824\": \"靖边县\",\n\t    \"610825\": \"定边县\",\n\t    \"610826\": \"绥德县\",\n\t    \"610827\": \"米脂县\",\n\t    \"610828\": \"佳县\",\n\t    \"610829\": \"吴堡县\",\n\t    \"610830\": \"清涧县\",\n\t    \"610831\": \"子洲县\",\n\t    \"610832\": \"其它区\",\n\t    \"610900\": \"安康市\",\n\t    \"610902\": \"汉滨区\",\n\t    \"610921\": \"汉阴县\",\n\t    \"610922\": \"石泉县\",\n\t    \"610923\": \"宁陕县\",\n\t    \"610924\": \"紫阳县\",\n\t    \"610925\": \"岚皋县\",\n\t    \"610926\": \"平利县\",\n\t    \"610927\": \"镇坪县\",\n\t    \"610928\": \"旬阳县\",\n\t    \"610929\": \"白河县\",\n\t    \"610930\": \"其它区\",\n\t    \"611000\": \"商洛市\",\n\t    \"611002\": \"商州区\",\n\t    \"611021\": \"洛南县\",\n\t    \"611022\": \"丹凤县\",\n\t    \"611023\": \"商南县\",\n\t    \"611024\": \"山阳县\",\n\t    \"611025\": \"镇安县\",\n\t    \"611026\": \"柞水县\",\n\t    \"611027\": \"其它区\",\n\t    \"620000\": \"甘肃省\",\n\t    \"620100\": \"兰州市\",\n\t    \"620102\": \"城关区\",\n\t    \"620103\": \"七里河区\",\n\t    \"620104\": \"西固区\",\n\t    \"620105\": \"安宁区\",\n\t    \"620111\": \"红古区\",\n\t    \"620121\": \"永登县\",\n\t    \"620122\": \"皋兰县\",\n\t    \"620123\": \"榆中县\",\n\t    \"620124\": \"其它区\",\n\t    \"620200\": \"嘉峪关市\",\n\t    \"620300\": \"金昌市\",\n\t    \"620302\": \"金川区\",\n\t    \"620321\": \"永昌县\",\n\t    \"620322\": \"其它区\",\n\t    \"620400\": \"白银市\",\n\t    \"620402\": \"白银区\",\n\t    \"620403\": \"平川区\",\n\t    \"620421\": \"靖远县\",\n\t    \"620422\": \"会宁县\",\n\t    \"620423\": \"景泰县\",\n\t    \"620424\": \"其它区\",\n\t    \"620500\": \"天水市\",\n\t    \"620502\": \"秦州区\",\n\t    \"620503\": \"麦积区\",\n\t    \"620521\": \"清水县\",\n\t    \"620522\": \"秦安县\",\n\t    \"620523\": \"甘谷县\",\n\t    \"620524\": \"武山县\",\n\t    \"620525\": \"张家川回族自治县\",\n\t    \"620526\": \"其它区\",\n\t    \"620600\": \"武威市\",\n\t    \"620602\": \"凉州区\",\n\t    \"620621\": \"民勤县\",\n\t    \"620622\": \"古浪县\",\n\t    \"620623\": \"天祝藏族自治县\",\n\t    \"620624\": \"其它区\",\n\t    \"620700\": \"张掖市\",\n\t    \"620702\": \"甘州区\",\n\t    \"620721\": \"肃南裕固族自治县\",\n\t    \"620722\": \"民乐县\",\n\t    \"620723\": \"临泽县\",\n\t    \"620724\": \"高台县\",\n\t    \"620725\": \"山丹县\",\n\t    \"620726\": \"其它区\",\n\t    \"620800\": \"平凉市\",\n\t    \"620802\": \"崆峒区\",\n\t    \"620821\": \"泾川县\",\n\t    \"620822\": \"灵台县\",\n\t    \"620823\": \"崇信县\",\n\t    \"620824\": \"华亭县\",\n\t    \"620825\": \"庄浪县\",\n\t    \"620826\": \"静宁县\",\n\t    \"620827\": \"其它区\",\n\t    \"620900\": \"酒泉市\",\n\t    \"620902\": \"肃州区\",\n\t    \"620921\": \"金塔县\",\n\t    \"620922\": \"瓜州县\",\n\t    \"620923\": \"肃北蒙古族自治县\",\n\t    \"620924\": \"阿克塞哈萨克族自治县\",\n\t    \"620981\": \"玉门市\",\n\t    \"620982\": \"敦煌市\",\n\t    \"620983\": \"其它区\",\n\t    \"621000\": \"庆阳市\",\n\t    \"621002\": \"西峰区\",\n\t    \"621021\": \"庆城县\",\n\t    \"621022\": \"环县\",\n\t    \"621023\": \"华池县\",\n\t    \"621024\": \"合水县\",\n\t    \"621025\": \"正宁县\",\n\t    \"621026\": \"宁县\",\n\t    \"621027\": \"镇原县\",\n\t    \"621028\": \"其它区\",\n\t    \"621100\": \"定西市\",\n\t    \"621102\": \"安定区\",\n\t    \"621121\": \"通渭县\",\n\t    \"621122\": \"陇西县\",\n\t    \"621123\": \"渭源县\",\n\t    \"621124\": \"临洮县\",\n\t    \"621125\": \"漳县\",\n\t    \"621126\": \"岷县\",\n\t    \"621127\": \"其它区\",\n\t    \"621200\": \"陇南市\",\n\t    \"621202\": \"武都区\",\n\t    \"621221\": \"成县\",\n\t    \"621222\": \"文县\",\n\t    \"621223\": \"宕昌县\",\n\t    \"621224\": \"康县\",\n\t    \"621225\": \"西和县\",\n\t    \"621226\": \"礼县\",\n\t    \"621227\": \"徽县\",\n\t    \"621228\": \"两当县\",\n\t    \"621229\": \"其它区\",\n\t    \"622900\": \"临夏回族自治州\",\n\t    \"622901\": \"临夏市\",\n\t    \"622921\": \"临夏县\",\n\t    \"622922\": \"康乐县\",\n\t    \"622923\": \"永靖县\",\n\t    \"622924\": \"广河县\",\n\t    \"622925\": \"和政县\",\n\t    \"622926\": \"东乡族自治县\",\n\t    \"622927\": \"积石山保安族东乡族撒拉族自治县\",\n\t    \"622928\": \"其它区\",\n\t    \"623000\": \"甘南藏族自治州\",\n\t    \"623001\": \"合作市\",\n\t    \"623021\": \"临潭县\",\n\t    \"623022\": \"卓尼县\",\n\t    \"623023\": \"舟曲县\",\n\t    \"623024\": \"迭部县\",\n\t    \"623025\": \"玛曲县\",\n\t    \"623026\": \"碌曲县\",\n\t    \"623027\": \"夏河县\",\n\t    \"623028\": \"其它区\",\n\t    \"630000\": \"青海省\",\n\t    \"630100\": \"西宁市\",\n\t    \"630102\": \"城东区\",\n\t    \"630103\": \"城中区\",\n\t    \"630104\": \"城西区\",\n\t    \"630105\": \"城北区\",\n\t    \"630121\": \"大通回族土族自治县\",\n\t    \"630122\": \"湟中县\",\n\t    \"630123\": \"湟源县\",\n\t    \"630124\": \"其它区\",\n\t    \"632100\": \"海东市\",\n\t    \"632121\": \"平安县\",\n\t    \"632122\": \"民和回族土族自治县\",\n\t    \"632123\": \"乐都区\",\n\t    \"632126\": \"互助土族自治县\",\n\t    \"632127\": \"化隆回族自治县\",\n\t    \"632128\": \"循化撒拉族自治县\",\n\t    \"632129\": \"其它区\",\n\t    \"632200\": \"海北藏族自治州\",\n\t    \"632221\": \"门源回族自治县\",\n\t    \"632222\": \"祁连县\",\n\t    \"632223\": \"海晏县\",\n\t    \"632224\": \"刚察县\",\n\t    \"632225\": \"其它区\",\n\t    \"632300\": \"黄南藏族自治州\",\n\t    \"632321\": \"同仁县\",\n\t    \"632322\": \"尖扎县\",\n\t    \"632323\": \"泽库县\",\n\t    \"632324\": \"河南蒙古族自治县\",\n\t    \"632325\": \"其它区\",\n\t    \"632500\": \"海南藏族自治州\",\n\t    \"632521\": \"共和县\",\n\t    \"632522\": \"同德县\",\n\t    \"632523\": \"贵德县\",\n\t    \"632524\": \"兴海县\",\n\t    \"632525\": \"贵南县\",\n\t    \"632526\": \"其它区\",\n\t    \"632600\": \"果洛藏族自治州\",\n\t    \"632621\": \"玛沁县\",\n\t    \"632622\": \"班玛县\",\n\t    \"632623\": \"甘德县\",\n\t    \"632624\": \"达日县\",\n\t    \"632625\": \"久治县\",\n\t    \"632626\": \"玛多县\",\n\t    \"632627\": \"其它区\",\n\t    \"632700\": \"玉树藏族自治州\",\n\t    \"632721\": \"玉树市\",\n\t    \"632722\": \"杂多县\",\n\t    \"632723\": \"称多县\",\n\t    \"632724\": \"治多县\",\n\t    \"632725\": \"囊谦县\",\n\t    \"632726\": \"曲麻莱县\",\n\t    \"632727\": \"其它区\",\n\t    \"632800\": \"海西蒙古族藏族自治州\",\n\t    \"632801\": \"格尔木市\",\n\t    \"632802\": \"德令哈市\",\n\t    \"632821\": \"乌兰县\",\n\t    \"632822\": \"都兰县\",\n\t    \"632823\": \"天峻县\",\n\t    \"632824\": \"其它区\",\n\t    \"640000\": \"宁夏回族自治区\",\n\t    \"640100\": \"银川市\",\n\t    \"640104\": \"兴庆区\",\n\t    \"640105\": \"西夏区\",\n\t    \"640106\": \"金凤区\",\n\t    \"640121\": \"永宁县\",\n\t    \"640122\": \"贺兰县\",\n\t    \"640181\": \"灵武市\",\n\t    \"640182\": \"其它区\",\n\t    \"640200\": \"石嘴山市\",\n\t    \"640202\": \"大武口区\",\n\t    \"640205\": \"惠农区\",\n\t    \"640221\": \"平罗县\",\n\t    \"640222\": \"其它区\",\n\t    \"640300\": \"吴忠市\",\n\t    \"640302\": \"利通区\",\n\t    \"640303\": \"红寺堡区\",\n\t    \"640323\": \"盐池县\",\n\t    \"640324\": \"同心县\",\n\t    \"640381\": \"青铜峡市\",\n\t    \"640382\": \"其它区\",\n\t    \"640400\": \"固原市\",\n\t    \"640402\": \"原州区\",\n\t    \"640422\": \"西吉县\",\n\t    \"640423\": \"隆德县\",\n\t    \"640424\": \"泾源县\",\n\t    \"640425\": \"彭阳县\",\n\t    \"640426\": \"其它区\",\n\t    \"640500\": \"中卫市\",\n\t    \"640502\": \"沙坡头区\",\n\t    \"640521\": \"中宁县\",\n\t    \"640522\": \"海原县\",\n\t    \"640523\": \"其它区\",\n\t    \"650000\": \"新疆维吾尔自治区\",\n\t    \"650100\": \"乌鲁木齐市\",\n\t    \"650102\": \"天山区\",\n\t    \"650103\": \"沙依巴克区\",\n\t    \"650104\": \"新市区\",\n\t    \"650105\": \"水磨沟区\",\n\t    \"650106\": \"头屯河区\",\n\t    \"650107\": \"达坂城区\",\n\t    \"650109\": \"米东区\",\n\t    \"650121\": \"乌鲁木齐县\",\n\t    \"650122\": \"其它区\",\n\t    \"650200\": \"克拉玛依市\",\n\t    \"650202\": \"独山子区\",\n\t    \"650203\": \"克拉玛依区\",\n\t    \"650204\": \"白碱滩区\",\n\t    \"650205\": \"乌尔禾区\",\n\t    \"650206\": \"其它区\",\n\t    \"652100\": \"吐鲁番地区\",\n\t    \"652101\": \"吐鲁番市\",\n\t    \"652122\": \"鄯善县\",\n\t    \"652123\": \"托克逊县\",\n\t    \"652124\": \"其它区\",\n\t    \"652200\": \"哈密地区\",\n\t    \"652201\": \"哈密市\",\n\t    \"652222\": \"巴里坤哈萨克自治县\",\n\t    \"652223\": \"伊吾县\",\n\t    \"652224\": \"其它区\",\n\t    \"652300\": \"昌吉回族自治州\",\n\t    \"652301\": \"昌吉市\",\n\t    \"652302\": \"阜康市\",\n\t    \"652323\": \"呼图壁县\",\n\t    \"652324\": \"玛纳斯县\",\n\t    \"652325\": \"奇台县\",\n\t    \"652327\": \"吉木萨尔县\",\n\t    \"652328\": \"木垒哈萨克自治县\",\n\t    \"652329\": \"其它区\",\n\t    \"652700\": \"博尔塔拉蒙古自治州\",\n\t    \"652701\": \"博乐市\",\n\t    \"652702\": \"阿拉山口市\",\n\t    \"652722\": \"精河县\",\n\t    \"652723\": \"温泉县\",\n\t    \"652724\": \"其它区\",\n\t    \"652800\": \"巴音郭楞蒙古自治州\",\n\t    \"652801\": \"库尔勒市\",\n\t    \"652822\": \"轮台县\",\n\t    \"652823\": \"尉犁县\",\n\t    \"652824\": \"若羌县\",\n\t    \"652825\": \"且末县\",\n\t    \"652826\": \"焉耆回族自治县\",\n\t    \"652827\": \"和静县\",\n\t    \"652828\": \"和硕县\",\n\t    \"652829\": \"博湖县\",\n\t    \"652830\": \"其它区\",\n\t    \"652900\": \"阿克苏地区\",\n\t    \"652901\": \"阿克苏市\",\n\t    \"652922\": \"温宿县\",\n\t    \"652923\": \"库车县\",\n\t    \"652924\": \"沙雅县\",\n\t    \"652925\": \"新和县\",\n\t    \"652926\": \"拜城县\",\n\t    \"652927\": \"乌什县\",\n\t    \"652928\": \"阿瓦提县\",\n\t    \"652929\": \"柯坪县\",\n\t    \"652930\": \"其它区\",\n\t    \"653000\": \"克孜勒苏柯尔克孜自治州\",\n\t    \"653001\": \"阿图什市\",\n\t    \"653022\": \"阿克陶县\",\n\t    \"653023\": \"阿合奇县\",\n\t    \"653024\": \"乌恰县\",\n\t    \"653025\": \"其它区\",\n\t    \"653100\": \"喀什地区\",\n\t    \"653101\": \"喀什市\",\n\t    \"653121\": \"疏附县\",\n\t    \"653122\": \"疏勒县\",\n\t    \"653123\": \"英吉沙县\",\n\t    \"653124\": \"泽普县\",\n\t    \"653125\": \"莎车县\",\n\t    \"653126\": \"叶城县\",\n\t    \"653127\": \"麦盖提县\",\n\t    \"653128\": \"岳普湖县\",\n\t    \"653129\": \"伽师县\",\n\t    \"653130\": \"巴楚县\",\n\t    \"653131\": \"塔什库尔干塔吉克自治县\",\n\t    \"653132\": \"其它区\",\n\t    \"653200\": \"和田地区\",\n\t    \"653201\": \"和田市\",\n\t    \"653221\": \"和田县\",\n\t    \"653222\": \"墨玉县\",\n\t    \"653223\": \"皮山县\",\n\t    \"653224\": \"洛浦县\",\n\t    \"653225\": \"策勒县\",\n\t    \"653226\": \"于田县\",\n\t    \"653227\": \"民丰县\",\n\t    \"653228\": \"其它区\",\n\t    \"654000\": \"伊犁哈萨克自治州\",\n\t    \"654002\": \"伊宁市\",\n\t    \"654003\": \"奎屯市\",\n\t    \"654021\": \"伊宁县\",\n\t    \"654022\": \"察布查尔锡伯自治县\",\n\t    \"654023\": \"霍城县\",\n\t    \"654024\": \"巩留县\",\n\t    \"654025\": \"新源县\",\n\t    \"654026\": \"昭苏县\",\n\t    \"654027\": \"特克斯县\",\n\t    \"654028\": \"尼勒克县\",\n\t    \"654029\": \"其它区\",\n\t    \"654200\": \"塔城地区\",\n\t    \"654201\": \"塔城市\",\n\t    \"654202\": \"乌苏市\",\n\t    \"654221\": \"额敏县\",\n\t    \"654223\": \"沙湾县\",\n\t    \"654224\": \"托里县\",\n\t    \"654225\": \"裕民县\",\n\t    \"654226\": \"和布克赛尔蒙古自治县\",\n\t    \"654227\": \"其它区\",\n\t    \"654300\": \"阿勒泰地区\",\n\t    \"654301\": \"阿勒泰市\",\n\t    \"654321\": \"布尔津县\",\n\t    \"654322\": \"富蕴县\",\n\t    \"654323\": \"福海县\",\n\t    \"654324\": \"哈巴河县\",\n\t    \"654325\": \"青河县\",\n\t    \"654326\": \"吉木乃县\",\n\t    \"654327\": \"其它区\",\n\t    \"659001\": \"石河子市\",\n\t    \"659002\": \"阿拉尔市\",\n\t    \"659003\": \"图木舒克市\",\n\t    \"659004\": \"五家渠市\",\n\t    \"710000\": \"台湾\",\n\t    \"710100\": \"台北市\",\n\t    \"710101\": \"中正区\",\n\t    \"710102\": \"大同区\",\n\t    \"710103\": \"中山区\",\n\t    \"710104\": \"松山区\",\n\t    \"710105\": \"大安区\",\n\t    \"710106\": \"万华区\",\n\t    \"710107\": \"信义区\",\n\t    \"710108\": \"士林区\",\n\t    \"710109\": \"北投区\",\n\t    \"710110\": \"内湖区\",\n\t    \"710111\": \"南港区\",\n\t    \"710112\": \"文山区\",\n\t    \"710113\": \"其它区\",\n\t    \"710200\": \"高雄市\",\n\t    \"710201\": \"新兴区\",\n\t    \"710202\": \"前金区\",\n\t    \"710203\": \"芩雅区\",\n\t    \"710204\": \"盐埕区\",\n\t    \"710205\": \"鼓山区\",\n\t    \"710206\": \"旗津区\",\n\t    \"710207\": \"前镇区\",\n\t    \"710208\": \"三民区\",\n\t    \"710209\": \"左营区\",\n\t    \"710210\": \"楠梓区\",\n\t    \"710211\": \"小港区\",\n\t    \"710212\": \"其它区\",\n\t    \"710241\": \"苓雅区\",\n\t    \"710242\": \"仁武区\",\n\t    \"710243\": \"大社区\",\n\t    \"710244\": \"冈山区\",\n\t    \"710245\": \"路竹区\",\n\t    \"710246\": \"阿莲区\",\n\t    \"710247\": \"田寮区\",\n\t    \"710248\": \"燕巢区\",\n\t    \"710249\": \"桥头区\",\n\t    \"710250\": \"梓官区\",\n\t    \"710251\": \"弥陀区\",\n\t    \"710252\": \"永安区\",\n\t    \"710253\": \"湖内区\",\n\t    \"710254\": \"凤山区\",\n\t    \"710255\": \"大寮区\",\n\t    \"710256\": \"林园区\",\n\t    \"710257\": \"鸟松区\",\n\t    \"710258\": \"大树区\",\n\t    \"710259\": \"旗山区\",\n\t    \"710260\": \"美浓区\",\n\t    \"710261\": \"六龟区\",\n\t    \"710262\": \"内门区\",\n\t    \"710263\": \"杉林区\",\n\t    \"710264\": \"甲仙区\",\n\t    \"710265\": \"桃源区\",\n\t    \"710266\": \"那玛夏区\",\n\t    \"710267\": \"茂林区\",\n\t    \"710268\": \"茄萣区\",\n\t    \"710300\": \"台南市\",\n\t    \"710301\": \"中西区\",\n\t    \"710302\": \"东区\",\n\t    \"710303\": \"南区\",\n\t    \"710304\": \"北区\",\n\t    \"710305\": \"安平区\",\n\t    \"710306\": \"安南区\",\n\t    \"710307\": \"其它区\",\n\t    \"710339\": \"永康区\",\n\t    \"710340\": \"归仁区\",\n\t    \"710341\": \"新化区\",\n\t    \"710342\": \"左镇区\",\n\t    \"710343\": \"玉井区\",\n\t    \"710344\": \"楠西区\",\n\t    \"710345\": \"南化区\",\n\t    \"710346\": \"仁德区\",\n\t    \"710347\": \"关庙区\",\n\t    \"710348\": \"龙崎区\",\n\t    \"710349\": \"官田区\",\n\t    \"710350\": \"麻豆区\",\n\t    \"710351\": \"佳里区\",\n\t    \"710352\": \"西港区\",\n\t    \"710353\": \"七股区\",\n\t    \"710354\": \"将军区\",\n\t    \"710355\": \"学甲区\",\n\t    \"710356\": \"北门区\",\n\t    \"710357\": \"新营区\",\n\t    \"710358\": \"后壁区\",\n\t    \"710359\": \"白河区\",\n\t    \"710360\": \"东山区\",\n\t    \"710361\": \"六甲区\",\n\t    \"710362\": \"下营区\",\n\t    \"710363\": \"柳营区\",\n\t    \"710364\": \"盐水区\",\n\t    \"710365\": \"善化区\",\n\t    \"710366\": \"大内区\",\n\t    \"710367\": \"山上区\",\n\t    \"710368\": \"新市区\",\n\t    \"710369\": \"安定区\",\n\t    \"710400\": \"台中市\",\n\t    \"710401\": \"中区\",\n\t    \"710402\": \"东区\",\n\t    \"710403\": \"南区\",\n\t    \"710404\": \"西区\",\n\t    \"710405\": \"北区\",\n\t    \"710406\": \"北屯区\",\n\t    \"710407\": \"西屯区\",\n\t    \"710408\": \"南屯区\",\n\t    \"710409\": \"其它区\",\n\t    \"710431\": \"太平区\",\n\t    \"710432\": \"大里区\",\n\t    \"710433\": \"雾峰区\",\n\t    \"710434\": \"乌日区\",\n\t    \"710435\": \"丰原区\",\n\t    \"710436\": \"后里区\",\n\t    \"710437\": \"石冈区\",\n\t    \"710438\": \"东势区\",\n\t    \"710439\": \"和平区\",\n\t    \"710440\": \"新社区\",\n\t    \"710441\": \"潭子区\",\n\t    \"710442\": \"大雅区\",\n\t    \"710443\": \"神冈区\",\n\t    \"710444\": \"大肚区\",\n\t    \"710445\": \"沙鹿区\",\n\t    \"710446\": \"龙井区\",\n\t    \"710447\": \"梧栖区\",\n\t    \"710448\": \"清水区\",\n\t    \"710449\": \"大甲区\",\n\t    \"710450\": \"外埔区\",\n\t    \"710451\": \"大安区\",\n\t    \"710500\": \"金门县\",\n\t    \"710507\": \"金沙镇\",\n\t    \"710508\": \"金湖镇\",\n\t    \"710509\": \"金宁乡\",\n\t    \"710510\": \"金城镇\",\n\t    \"710511\": \"烈屿乡\",\n\t    \"710512\": \"乌坵乡\",\n\t    \"710600\": \"南投县\",\n\t    \"710614\": \"南投市\",\n\t    \"710615\": \"中寮乡\",\n\t    \"710616\": \"草屯镇\",\n\t    \"710617\": \"国姓乡\",\n\t    \"710618\": \"埔里镇\",\n\t    \"710619\": \"仁爱乡\",\n\t    \"710620\": \"名间乡\",\n\t    \"710621\": \"集集镇\",\n\t    \"710622\": \"水里乡\",\n\t    \"710623\": \"鱼池乡\",\n\t    \"710624\": \"信义乡\",\n\t    \"710625\": \"竹山镇\",\n\t    \"710626\": \"鹿谷乡\",\n\t    \"710700\": \"基隆市\",\n\t    \"710701\": \"仁爱区\",\n\t    \"710702\": \"信义区\",\n\t    \"710703\": \"中正区\",\n\t    \"710704\": \"中山区\",\n\t    \"710705\": \"安乐区\",\n\t    \"710706\": \"暖暖区\",\n\t    \"710707\": \"七堵区\",\n\t    \"710708\": \"其它区\",\n\t    \"710800\": \"新竹市\",\n\t    \"710801\": \"东区\",\n\t    \"710802\": \"北区\",\n\t    \"710803\": \"香山区\",\n\t    \"710804\": \"其它区\",\n\t    \"710900\": \"嘉义市\",\n\t    \"710901\": \"东区\",\n\t    \"710902\": \"西区\",\n\t    \"710903\": \"其它区\",\n\t    \"711100\": \"新北市\",\n\t    \"711130\": \"万里区\",\n\t    \"711131\": \"金山区\",\n\t    \"711132\": \"板桥区\",\n\t    \"711133\": \"汐止区\",\n\t    \"711134\": \"深坑区\",\n\t    \"711135\": \"石碇区\",\n\t    \"711136\": \"瑞芳区\",\n\t    \"711137\": \"平溪区\",\n\t    \"711138\": \"双溪区\",\n\t    \"711139\": \"贡寮区\",\n\t    \"711140\": \"新店区\",\n\t    \"711141\": \"坪林区\",\n\t    \"711142\": \"乌来区\",\n\t    \"711143\": \"永和区\",\n\t    \"711144\": \"中和区\",\n\t    \"711145\": \"土城区\",\n\t    \"711146\": \"三峡区\",\n\t    \"711147\": \"树林区\",\n\t    \"711148\": \"莺歌区\",\n\t    \"711149\": \"三重区\",\n\t    \"711150\": \"新庄区\",\n\t    \"711151\": \"泰山区\",\n\t    \"711152\": \"林口区\",\n\t    \"711153\": \"芦洲区\",\n\t    \"711154\": \"五股区\",\n\t    \"711155\": \"八里区\",\n\t    \"711156\": \"淡水区\",\n\t    \"711157\": \"三芝区\",\n\t    \"711158\": \"石门区\",\n\t    \"711200\": \"宜兰县\",\n\t    \"711214\": \"宜兰市\",\n\t    \"711215\": \"头城镇\",\n\t    \"711216\": \"礁溪乡\",\n\t    \"711217\": \"壮围乡\",\n\t    \"711218\": \"员山乡\",\n\t    \"711219\": \"罗东镇\",\n\t    \"711220\": \"三星乡\",\n\t    \"711221\": \"大同乡\",\n\t    \"711222\": \"五结乡\",\n\t    \"711223\": \"冬山乡\",\n\t    \"711224\": \"苏澳镇\",\n\t    \"711225\": \"南澳乡\",\n\t    \"711226\": \"钓鱼台\",\n\t    \"711300\": \"新竹县\",\n\t    \"711314\": \"竹北市\",\n\t    \"711315\": \"湖口乡\",\n\t    \"711316\": \"新丰乡\",\n\t    \"711317\": \"新埔镇\",\n\t    \"711318\": \"关西镇\",\n\t    \"711319\": \"芎林乡\",\n\t    \"711320\": \"宝山乡\",\n\t    \"711321\": \"竹东镇\",\n\t    \"711322\": \"五峰乡\",\n\t    \"711323\": \"横山乡\",\n\t    \"711324\": \"尖石乡\",\n\t    \"711325\": \"北埔乡\",\n\t    \"711326\": \"峨眉乡\",\n\t    \"711400\": \"桃园县\",\n\t    \"711414\": \"中坜市\",\n\t    \"711415\": \"平镇市\",\n\t    \"711416\": \"龙潭乡\",\n\t    \"711417\": \"杨梅市\",\n\t    \"711418\": \"新屋乡\",\n\t    \"711419\": \"观音乡\",\n\t    \"711420\": \"桃园市\",\n\t    \"711421\": \"龟山乡\",\n\t    \"711422\": \"八德市\",\n\t    \"711423\": \"大溪镇\",\n\t    \"711424\": \"复兴乡\",\n\t    \"711425\": \"大园乡\",\n\t    \"711426\": \"芦竹乡\",\n\t    \"711500\": \"苗栗县\",\n\t    \"711519\": \"竹南镇\",\n\t    \"711520\": \"头份镇\",\n\t    \"711521\": \"三湾乡\",\n\t    \"711522\": \"南庄乡\",\n\t    \"711523\": \"狮潭乡\",\n\t    \"711524\": \"后龙镇\",\n\t    \"711525\": \"通霄镇\",\n\t    \"711526\": \"苑里镇\",\n\t    \"711527\": \"苗栗市\",\n\t    \"711528\": \"造桥乡\",\n\t    \"711529\": \"头屋乡\",\n\t    \"711530\": \"公馆乡\",\n\t    \"711531\": \"大湖乡\",\n\t    \"711532\": \"泰安乡\",\n\t    \"711533\": \"铜锣乡\",\n\t    \"711534\": \"三义乡\",\n\t    \"711535\": \"西湖乡\",\n\t    \"711536\": \"卓兰镇\",\n\t    \"711700\": \"彰化县\",\n\t    \"711727\": \"彰化市\",\n\t    \"711728\": \"芬园乡\",\n\t    \"711729\": \"花坛乡\",\n\t    \"711730\": \"秀水乡\",\n\t    \"711731\": \"鹿港镇\",\n\t    \"711732\": \"福兴乡\",\n\t    \"711733\": \"线西乡\",\n\t    \"711734\": \"和美镇\",\n\t    \"711735\": \"伸港乡\",\n\t    \"711736\": \"员林镇\",\n\t    \"711737\": \"社头乡\",\n\t    \"711738\": \"永靖乡\",\n\t    \"711739\": \"埔心乡\",\n\t    \"711740\": \"溪湖镇\",\n\t    \"711741\": \"大村乡\",\n\t    \"711742\": \"埔盐乡\",\n\t    \"711743\": \"田中镇\",\n\t    \"711744\": \"北斗镇\",\n\t    \"711745\": \"田尾乡\",\n\t    \"711746\": \"埤头乡\",\n\t    \"711747\": \"溪州乡\",\n\t    \"711748\": \"竹塘乡\",\n\t    \"711749\": \"二林镇\",\n\t    \"711750\": \"大城乡\",\n\t    \"711751\": \"芳苑乡\",\n\t    \"711752\": \"二水乡\",\n\t    \"711900\": \"嘉义县\",\n\t    \"711919\": \"番路乡\",\n\t    \"711920\": \"梅山乡\",\n\t    \"711921\": \"竹崎乡\",\n\t    \"711922\": \"阿里山乡\",\n\t    \"711923\": \"中埔乡\",\n\t    \"711924\": \"大埔乡\",\n\t    \"711925\": \"水上乡\",\n\t    \"711926\": \"鹿草乡\",\n\t    \"711927\": \"太保市\",\n\t    \"711928\": \"朴子市\",\n\t    \"711929\": \"东石乡\",\n\t    \"711930\": \"六脚乡\",\n\t    \"711931\": \"新港乡\",\n\t    \"711932\": \"民雄乡\",\n\t    \"711933\": \"大林镇\",\n\t    \"711934\": \"溪口乡\",\n\t    \"711935\": \"义竹乡\",\n\t    \"711936\": \"布袋镇\",\n\t    \"712100\": \"云林县\",\n\t    \"712121\": \"斗南镇\",\n\t    \"712122\": \"大埤乡\",\n\t    \"712123\": \"虎尾镇\",\n\t    \"712124\": \"土库镇\",\n\t    \"712125\": \"褒忠乡\",\n\t    \"712126\": \"东势乡\",\n\t    \"712127\": \"台西乡\",\n\t    \"712128\": \"仑背乡\",\n\t    \"712129\": \"麦寮乡\",\n\t    \"712130\": \"斗六市\",\n\t    \"712131\": \"林内乡\",\n\t    \"712132\": \"古坑乡\",\n\t    \"712133\": \"莿桐乡\",\n\t    \"712134\": \"西螺镇\",\n\t    \"712135\": \"二仑乡\",\n\t    \"712136\": \"北港镇\",\n\t    \"712137\": \"水林乡\",\n\t    \"712138\": \"口湖乡\",\n\t    \"712139\": \"四湖乡\",\n\t    \"712140\": \"元长乡\",\n\t    \"712400\": \"屏东县\",\n\t    \"712434\": \"屏东市\",\n\t    \"712435\": \"三地门乡\",\n\t    \"712436\": \"雾台乡\",\n\t    \"712437\": \"玛家乡\",\n\t    \"712438\": \"九如乡\",\n\t    \"712439\": \"里港乡\",\n\t    \"712440\": \"高树乡\",\n\t    \"712441\": \"盐埔乡\",\n\t    \"712442\": \"长治乡\",\n\t    \"712443\": \"麟洛乡\",\n\t    \"712444\": \"竹田乡\",\n\t    \"712445\": \"内埔乡\",\n\t    \"712446\": \"万丹乡\",\n\t    \"712447\": \"潮州镇\",\n\t    \"712448\": \"泰武乡\",\n\t    \"712449\": \"来义乡\",\n\t    \"712450\": \"万峦乡\",\n\t    \"712451\": \"崁顶乡\",\n\t    \"712452\": \"新埤乡\",\n\t    \"712453\": \"南州乡\",\n\t    \"712454\": \"林边乡\",\n\t    \"712455\": \"东港镇\",\n\t    \"712456\": \"琉球乡\",\n\t    \"712457\": \"佳冬乡\",\n\t    \"712458\": \"新园乡\",\n\t    \"712459\": \"枋寮乡\",\n\t    \"712460\": \"枋山乡\",\n\t    \"712461\": \"春日乡\",\n\t    \"712462\": \"狮子乡\",\n\t    \"712463\": \"车城乡\",\n\t    \"712464\": \"牡丹乡\",\n\t    \"712465\": \"恒春镇\",\n\t    \"712466\": \"满州乡\",\n\t    \"712500\": \"台东县\",\n\t    \"712517\": \"台东市\",\n\t    \"712518\": \"绿岛乡\",\n\t    \"712519\": \"兰屿乡\",\n\t    \"712520\": \"延平乡\",\n\t    \"712521\": \"卑南乡\",\n\t    \"712522\": \"鹿野乡\",\n\t    \"712523\": \"关山镇\",\n\t    \"712524\": \"海端乡\",\n\t    \"712525\": \"池上乡\",\n\t    \"712526\": \"东河乡\",\n\t    \"712527\": \"成功镇\",\n\t    \"712528\": \"长滨乡\",\n\t    \"712529\": \"金峰乡\",\n\t    \"712530\": \"大武乡\",\n\t    \"712531\": \"达仁乡\",\n\t    \"712532\": \"太麻里乡\",\n\t    \"712600\": \"花莲县\",\n\t    \"712615\": \"花莲市\",\n\t    \"712616\": \"新城乡\",\n\t    \"712617\": \"太鲁阁\",\n\t    \"712618\": \"秀林乡\",\n\t    \"712619\": \"吉安乡\",\n\t    \"712620\": \"寿丰乡\",\n\t    \"712621\": \"凤林镇\",\n\t    \"712622\": \"光复乡\",\n\t    \"712623\": \"丰滨乡\",\n\t    \"712624\": \"瑞穗乡\",\n\t    \"712625\": \"万荣乡\",\n\t    \"712626\": \"玉里镇\",\n\t    \"712627\": \"卓溪乡\",\n\t    \"712628\": \"富里乡\",\n\t    \"712700\": \"澎湖县\",\n\t    \"712707\": \"马公市\",\n\t    \"712708\": \"西屿乡\",\n\t    \"712709\": \"望安乡\",\n\t    \"712710\": \"七美乡\",\n\t    \"712711\": \"白沙乡\",\n\t    \"712712\": \"湖西乡\",\n\t    \"712800\": \"连江县\",\n\t    \"712805\": \"南竿乡\",\n\t    \"712806\": \"北竿乡\",\n\t    \"712807\": \"莒光乡\",\n\t    \"712808\": \"东引乡\",\n\t    \"810000\": \"香港特别行政区\",\n\t    \"810100\": \"香港岛\",\n\t    \"810101\": \"中西区\",\n\t    \"810102\": \"湾仔\",\n\t    \"810103\": \"东区\",\n\t    \"810104\": \"南区\",\n\t    \"810200\": \"九龙\",\n\t    \"810201\": \"九龙城区\",\n\t    \"810202\": \"油尖旺区\",\n\t    \"810203\": \"深水埗区\",\n\t    \"810204\": \"黄大仙区\",\n\t    \"810205\": \"观塘区\",\n\t    \"810300\": \"新界\",\n\t    \"810301\": \"北区\",\n\t    \"810302\": \"大埔区\",\n\t    \"810303\": \"沙田区\",\n\t    \"810304\": \"西贡区\",\n\t    \"810305\": \"元朗区\",\n\t    \"810306\": \"屯门区\",\n\t    \"810307\": \"荃湾区\",\n\t    \"810308\": \"葵青区\",\n\t    \"810309\": \"离岛区\",\n\t    \"820000\": \"澳门特别行政区\",\n\t    \"820100\": \"澳门半岛\",\n\t    \"820200\": \"离岛\",\n\t    \"990000\": \"海外\",\n\t    \"990100\": \"海外\"\n\t}\n\n\t// id pid/parentId name children\n\tfunction tree(list) {\n\t    var mapped = {}\n\t    for (var i = 0, item; i < list.length; i++) {\n\t        item = list[i]\n\t        if (!item || !item.id) continue\n\t        mapped[item.id] = item\n\t    }\n\n\t    var result = []\n\t    for (var ii = 0; ii < list.length; ii++) {\n\t        item = list[ii]\n\n\t        if (!item) continue\n\t            /* jshint -W041 */\n\t        if (item.pid == undefined && item.parentId == undefined) {\n\t            result.push(item)\n\t            continue\n\t        }\n\t        var parent = mapped[item.pid] || mapped[item.parentId]\n\t        if (!parent) continue\n\t        if (!parent.children) parent.children = []\n\t        parent.children.push(item)\n\t    }\n\t    return result\n\t}\n\n\tvar DICT_FIXED = function() {\n\t    var fixed = []\n\t    for (var id in DICT) {\n\t        var pid = id.slice(2, 6) === '0000' ? undefined :\n\t            id.slice(4, 6) == '00' ? (id.slice(0, 2) + '0000') :\n\t            id.slice(0, 4) + '00'\n\t        fixed.push({\n\t            id: id,\n\t            pid: pid,\n\t            name: DICT[id]\n\t        })\n\t    }\n\t    return tree(fixed)\n\t}()\n\n\tmodule.exports = DICT_FIXED\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## Miscellaneous\n\t*/\n\tvar DICT = __webpack_require__(18)\n\tmodule.exports = {\n\t\t// Dice\n\t\td4: function() {\n\t\t\treturn this.natural(1, 4)\n\t\t},\n\t\td6: function() {\n\t\t\treturn this.natural(1, 6)\n\t\t},\n\t\td8: function() {\n\t\t\treturn this.natural(1, 8)\n\t\t},\n\t\td12: function() {\n\t\t\treturn this.natural(1, 12)\n\t\t},\n\t\td20: function() {\n\t\t\treturn this.natural(1, 20)\n\t\t},\n\t\td100: function() {\n\t\t\treturn this.natural(1, 100)\n\t\t},\n\t\t/*\n\t\t    随机生成一个 GUID。\n\n\t\t    http://www.broofa.com/2008/09/javascript-uuid-function/\n\t\t    [UUID 规范](http://www.ietf.org/rfc/rfc4122.txt)\n\t\t        UUIDs (Universally Unique IDentifier)\n\t\t        GUIDs (Globally Unique IDentifier)\n\t\t        The formal definition of the UUID string representation is provided by the following ABNF [7]:\n\t\t            UUID                   = time-low \"-\" time-mid \"-\"\n\t\t                                   time-high-and-version \"-\"\n\t\t                                   clock-seq-and-reserved\n\t\t                                   clock-seq-low \"-\" node\n\t\t            time-low               = 4hexOctet\n\t\t            time-mid               = 2hexOctet\n\t\t            time-high-and-version  = 2hexOctet\n\t\t            clock-seq-and-reserved = hexOctet\n\t\t            clock-seq-low          = hexOctet\n\t\t            node                   = 6hexOctet\n\t\t            hexOctet               = hexDigit hexDigit\n\t\t            hexDigit =\n\t\t                \"0\" / \"1\" / \"2\" / \"3\" / \"4\" / \"5\" / \"6\" / \"7\" / \"8\" / \"9\" /\n\t\t                \"a\" / \"b\" / \"c\" / \"d\" / \"e\" / \"f\" /\n\t\t                \"A\" / \"B\" / \"C\" / \"D\" / \"E\" / \"F\"\n\t\t    \n\t\t    https://github.com/victorquinn/chancejs/blob/develop/chance.js#L1349\n\t\t*/\n\t\tguid: function() {\n\t\t\tvar pool = \"abcdefABCDEF1234567890\",\n\t\t\t\tguid = this.string(pool, 8) + '-' +\n\t\t\t\tthis.string(pool, 4) + '-' +\n\t\t\t\tthis.string(pool, 4) + '-' +\n\t\t\t\tthis.string(pool, 4) + '-' +\n\t\t\t\tthis.string(pool, 12);\n\t\t\treturn guid\n\t\t},\n\t\tuuid: function() {\n\t\t\treturn this.guid()\n\t\t},\n\t\t/*\n\t\t    随机生成一个 18 位身份证。\n\n\t\t    [身份证](http://baike.baidu.com/view/1697.htm#4)\n\t\t        地址码 6 + 出生日期码 8 + 顺序码 3 + 校验码 1\n\t\t    [《中华人民共和国行政区划代码》国家标准(GB/T2260)](http://zhidao.baidu.com/question/1954561.html)\n\t\t*/\n\t\tid: function() {\n\t\t\tvar id,\n\t\t\t\tsum = 0,\n\t\t\t\trank = [\n\t\t\t\t\t\"7\", \"9\", \"10\", \"5\", \"8\", \"4\", \"2\", \"1\", \"6\", \"3\", \"7\", \"9\", \"10\", \"5\", \"8\", \"4\", \"2\"\n\t\t\t\t],\n\t\t\t\tlast = [\n\t\t\t\t\t\"1\", \"0\", \"X\", \"9\", \"8\", \"7\", \"6\", \"5\", \"4\", \"3\", \"2\"\n\t\t\t\t]\n\n\t\t\tid = this.pick(DICT).id +\n\t\t\t\tthis.date('yyyyMMdd') +\n\t\t\t\tthis.string('number', 3)\n\n\t\t\tfor (var i = 0; i < id.length; i++) {\n\t\t\t\tsum += id[i] * rank[i];\n\t\t\t}\n\t\t\tid += last[sum % 11];\n\n\t\t\treturn id\n\t\t},\n\n\t\t/*\n\t\t    生成一个全局的自增整数。\n\t\t    类似自增主键（auto increment primary key）。\n\t\t*/\n\t\tincrement: function() {\n\t\t\tvar key = 0\n\t\t\treturn function(step) {\n\t\t\t\treturn key += (+step || 1) // step?\n\t\t\t}\n\t\t}(),\n\t\tinc: function(step) {\n\t\t\treturn this.increment(step)\n\t\t}\n\t}\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tvar Parser = __webpack_require__(21)\n\tvar Handler = __webpack_require__(22)\n\tmodule.exports = {\n\t\tParser: Parser,\n\t\tHandler: Handler\n\t}\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports) {\n\n\t// https://github.com/nuysoft/regexp\n\t// forked from https://github.com/ForbesLindesay/regexp\n\n\tfunction parse(n) {\n\t    if (\"string\" != typeof n) {\n\t        var l = new TypeError(\"The regexp to parse must be represented as a string.\");\n\t        throw l;\n\t    }\n\t    return index = 1, cgs = {}, parser.parse(n);\n\t}\n\n\tfunction Token(n) {\n\t    this.type = n, this.offset = Token.offset(), this.text = Token.text();\n\t}\n\n\tfunction Alternate(n, l) {\n\t    Token.call(this, \"alternate\"), this.left = n, this.right = l;\n\t}\n\n\tfunction Match(n) {\n\t    Token.call(this, \"match\"), this.body = n.filter(Boolean);\n\t}\n\n\tfunction Group(n, l) {\n\t    Token.call(this, n), this.body = l;\n\t}\n\n\tfunction CaptureGroup(n) {\n\t    Group.call(this, \"capture-group\"), this.index = cgs[this.offset] || (cgs[this.offset] = index++), \n\t    this.body = n;\n\t}\n\n\tfunction Quantified(n, l) {\n\t    Token.call(this, \"quantified\"), this.body = n, this.quantifier = l;\n\t}\n\n\tfunction Quantifier(n, l) {\n\t    Token.call(this, \"quantifier\"), this.min = n, this.max = l, this.greedy = !0;\n\t}\n\n\tfunction CharSet(n, l) {\n\t    Token.call(this, \"charset\"), this.invert = n, this.body = l;\n\t}\n\n\tfunction CharacterRange(n, l) {\n\t    Token.call(this, \"range\"), this.start = n, this.end = l;\n\t}\n\n\tfunction Literal(n) {\n\t    Token.call(this, \"literal\"), this.body = n, this.escaped = this.body != this.text;\n\t}\n\n\tfunction Unicode(n) {\n\t    Token.call(this, \"unicode\"), this.code = n.toUpperCase();\n\t}\n\n\tfunction Hex(n) {\n\t    Token.call(this, \"hex\"), this.code = n.toUpperCase();\n\t}\n\n\tfunction Octal(n) {\n\t    Token.call(this, \"octal\"), this.code = n.toUpperCase();\n\t}\n\n\tfunction BackReference(n) {\n\t    Token.call(this, \"back-reference\"), this.code = n.toUpperCase();\n\t}\n\n\tfunction ControlCharacter(n) {\n\t    Token.call(this, \"control-character\"), this.code = n.toUpperCase();\n\t}\n\n\tvar parser = function() {\n\t    function n(n, l) {\n\t        function u() {\n\t            this.constructor = n;\n\t        }\n\t        u.prototype = l.prototype, n.prototype = new u();\n\t    }\n\t    function l(n, l, u, t, r) {\n\t        function e(n, l) {\n\t            function u(n) {\n\t                function l(n) {\n\t                    return n.charCodeAt(0).toString(16).toUpperCase();\n\t                }\n\t                return n.replace(/\\\\/g, \"\\\\\\\\\").replace(/\"/g, '\\\\\"').replace(/\\x08/g, \"\\\\b\").replace(/\\t/g, \"\\\\t\").replace(/\\n/g, \"\\\\n\").replace(/\\f/g, \"\\\\f\").replace(/\\r/g, \"\\\\r\").replace(/[\\x00-\\x07\\x0B\\x0E\\x0F]/g, function(n) {\n\t                    return \"\\\\x0\" + l(n);\n\t                }).replace(/[\\x10-\\x1F\\x80-\\xFF]/g, function(n) {\n\t                    return \"\\\\x\" + l(n);\n\t                }).replace(/[\\u0180-\\u0FFF]/g, function(n) {\n\t                    return \"\\\\u0\" + l(n);\n\t                }).replace(/[\\u1080-\\uFFFF]/g, function(n) {\n\t                    return \"\\\\u\" + l(n);\n\t                });\n\t            }\n\t            var t, r;\n\t            switch (n.length) {\n\t              case 0:\n\t                t = \"end of input\";\n\t                break;\n\n\t              case 1:\n\t                t = n[0];\n\t                break;\n\n\t              default:\n\t                t = n.slice(0, -1).join(\", \") + \" or \" + n[n.length - 1];\n\t            }\n\t            return r = l ? '\"' + u(l) + '\"' : \"end of input\", \"Expected \" + t + \" but \" + r + \" found.\";\n\t        }\n\t        this.expected = n, this.found = l, this.offset = u, this.line = t, this.column = r, \n\t        this.name = \"SyntaxError\", this.message = e(n, l);\n\t    }\n\t    function u(n) {\n\t        function u() {\n\t            return n.substring(Lt, qt);\n\t        }\n\t        function t() {\n\t            return Lt;\n\t        }\n\t        function r(l) {\n\t            function u(l, u, t) {\n\t                var r, e;\n\t                for (r = u; t > r; r++) e = n.charAt(r), \"\\n\" === e ? (l.seenCR || l.line++, l.column = 1, \n\t                l.seenCR = !1) : \"\\r\" === e || \"\\u2028\" === e || \"\\u2029\" === e ? (l.line++, l.column = 1, \n\t                l.seenCR = !0) : (l.column++, l.seenCR = !1);\n\t            }\n\t            return Mt !== l && (Mt > l && (Mt = 0, Dt = {\n\t                line: 1,\n\t                column: 1,\n\t                seenCR: !1\n\t            }), u(Dt, Mt, l), Mt = l), Dt;\n\t        }\n\t        function e(n) {\n\t            Ht > qt || (qt > Ht && (Ht = qt, Ot = []), Ot.push(n));\n\t        }\n\t        function o(n) {\n\t            var l = 0;\n\t            for (n.sort(); l < n.length; ) n[l - 1] === n[l] ? n.splice(l, 1) : l++;\n\t        }\n\t        function c() {\n\t            var l, u, t, r, o;\n\t            return l = qt, u = i(), null !== u ? (t = qt, 124 === n.charCodeAt(qt) ? (r = fl, \n\t            qt++) : (r = null, 0 === Wt && e(sl)), null !== r ? (o = c(), null !== o ? (r = [ r, o ], \n\t            t = r) : (qt = t, t = il)) : (qt = t, t = il), null === t && (t = al), null !== t ? (Lt = l, \n\t            u = hl(u, t), null === u ? (qt = l, l = u) : l = u) : (qt = l, l = il)) : (qt = l, \n\t            l = il), l;\n\t        }\n\t        function i() {\n\t            var n, l, u, t, r;\n\t            if (n = qt, l = f(), null === l && (l = al), null !== l) if (u = qt, Wt++, t = d(), \n\t            Wt--, null === t ? u = al : (qt = u, u = il), null !== u) {\n\t                for (t = [], r = h(), null === r && (r = a()); null !== r; ) t.push(r), r = h(), \n\t                null === r && (r = a());\n\t                null !== t ? (r = s(), null === r && (r = al), null !== r ? (Lt = n, l = dl(l, t, r), \n\t                null === l ? (qt = n, n = l) : n = l) : (qt = n, n = il)) : (qt = n, n = il);\n\t            } else qt = n, n = il; else qt = n, n = il;\n\t            return n;\n\t        }\n\t        function a() {\n\t            var n;\n\t            return n = x(), null === n && (n = Q(), null === n && (n = B())), n;\n\t        }\n\t        function f() {\n\t            var l, u;\n\t            return l = qt, 94 === n.charCodeAt(qt) ? (u = pl, qt++) : (u = null, 0 === Wt && e(vl)), \n\t            null !== u && (Lt = l, u = wl()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function s() {\n\t            var l, u;\n\t            return l = qt, 36 === n.charCodeAt(qt) ? (u = Al, qt++) : (u = null, 0 === Wt && e(Cl)), \n\t            null !== u && (Lt = l, u = gl()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function h() {\n\t            var n, l, u;\n\t            return n = qt, l = a(), null !== l ? (u = d(), null !== u ? (Lt = n, l = bl(l, u), \n\t            null === l ? (qt = n, n = l) : n = l) : (qt = n, n = il)) : (qt = n, n = il), n;\n\t        }\n\t        function d() {\n\t            var n, l, u;\n\t            return Wt++, n = qt, l = p(), null !== l ? (u = k(), null === u && (u = al), null !== u ? (Lt = n, \n\t            l = Tl(l, u), null === l ? (qt = n, n = l) : n = l) : (qt = n, n = il)) : (qt = n, \n\t            n = il), Wt--, null === n && (l = null, 0 === Wt && e(kl)), n;\n\t        }\n\t        function p() {\n\t            var n;\n\t            return n = v(), null === n && (n = w(), null === n && (n = A(), null === n && (n = C(), \n\t            null === n && (n = g(), null === n && (n = b()))))), n;\n\t        }\n\t        function v() {\n\t            var l, u, t, r, o, c;\n\t            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)), \n\t            null !== u ? (t = T(), null !== t ? (44 === n.charCodeAt(qt) ? (r = ml, qt++) : (r = null, \n\t            0 === Wt && e(Rl)), null !== r ? (o = T(), null !== o ? (125 === n.charCodeAt(qt) ? (c = Fl, \n\t            qt++) : (c = null, 0 === Wt && e(Ql)), null !== c ? (Lt = l, u = Sl(t, o), null === u ? (qt = l, \n\t            l = u) : l = u) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, \n\t            l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function w() {\n\t            var l, u, t, r;\n\t            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)), \n\t            null !== u ? (t = T(), null !== t ? (n.substr(qt, 2) === Ul ? (r = Ul, qt += 2) : (r = null, \n\t            0 === Wt && e(El)), null !== r ? (Lt = l, u = Gl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function A() {\n\t            var l, u, t, r;\n\t            return l = qt, 123 === n.charCodeAt(qt) ? (u = xl, qt++) : (u = null, 0 === Wt && e(yl)), \n\t            null !== u ? (t = T(), null !== t ? (125 === n.charCodeAt(qt) ? (r = Fl, qt++) : (r = null, \n\t            0 === Wt && e(Ql)), null !== r ? (Lt = l, u = Bl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function C() {\n\t            var l, u;\n\t            return l = qt, 43 === n.charCodeAt(qt) ? (u = jl, qt++) : (u = null, 0 === Wt && e($l)), \n\t            null !== u && (Lt = l, u = ql()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function g() {\n\t            var l, u;\n\t            return l = qt, 42 === n.charCodeAt(qt) ? (u = Ll, qt++) : (u = null, 0 === Wt && e(Ml)), \n\t            null !== u && (Lt = l, u = Dl()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function b() {\n\t            var l, u;\n\t            return l = qt, 63 === n.charCodeAt(qt) ? (u = Hl, qt++) : (u = null, 0 === Wt && e(Ol)), \n\t            null !== u && (Lt = l, u = Wl()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function k() {\n\t            var l;\n\t            return 63 === n.charCodeAt(qt) ? (l = Hl, qt++) : (l = null, 0 === Wt && e(Ol)), \n\t            l;\n\t        }\n\t        function T() {\n\t            var l, u, t;\n\t            if (l = qt, u = [], zl.test(n.charAt(qt)) ? (t = n.charAt(qt), qt++) : (t = null, \n\t            0 === Wt && e(Il)), null !== t) for (;null !== t; ) u.push(t), zl.test(n.charAt(qt)) ? (t = n.charAt(qt), \n\t            qt++) : (t = null, 0 === Wt && e(Il)); else u = il;\n\t            return null !== u && (Lt = l, u = Jl(u)), null === u ? (qt = l, l = u) : l = u, \n\t            l;\n\t        }\n\t        function x() {\n\t            var l, u, t, r;\n\t            return l = qt, 40 === n.charCodeAt(qt) ? (u = Kl, qt++) : (u = null, 0 === Wt && e(Nl)), \n\t            null !== u ? (t = R(), null === t && (t = F(), null === t && (t = m(), null === t && (t = y()))), \n\t            null !== t ? (41 === n.charCodeAt(qt) ? (r = Pl, qt++) : (r = null, 0 === Wt && e(Vl)), \n\t            null !== r ? (Lt = l, u = Xl(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function y() {\n\t            var n, l;\n\t            return n = qt, l = c(), null !== l && (Lt = n, l = Yl(l)), null === l ? (qt = n, \n\t            n = l) : n = l, n;\n\t        }\n\t        function m() {\n\t            var l, u, t;\n\t            return l = qt, n.substr(qt, 2) === Zl ? (u = Zl, qt += 2) : (u = null, 0 === Wt && e(_l)), \n\t            null !== u ? (t = c(), null !== t ? (Lt = l, u = nu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function R() {\n\t            var l, u, t;\n\t            return l = qt, n.substr(qt, 2) === lu ? (u = lu, qt += 2) : (u = null, 0 === Wt && e(uu)), \n\t            null !== u ? (t = c(), null !== t ? (Lt = l, u = tu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function F() {\n\t            var l, u, t;\n\t            return l = qt, n.substr(qt, 2) === ru ? (u = ru, qt += 2) : (u = null, 0 === Wt && e(eu)), \n\t            null !== u ? (t = c(), null !== t ? (Lt = l, u = ou(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function Q() {\n\t            var l, u, t, r, o;\n\t            if (Wt++, l = qt, 91 === n.charCodeAt(qt) ? (u = iu, qt++) : (u = null, 0 === Wt && e(au)), \n\t            null !== u) if (94 === n.charCodeAt(qt) ? (t = pl, qt++) : (t = null, 0 === Wt && e(vl)), \n\t            null === t && (t = al), null !== t) {\n\t                for (r = [], o = S(), null === o && (o = U()); null !== o; ) r.push(o), o = S(), \n\t                null === o && (o = U());\n\t                null !== r ? (93 === n.charCodeAt(qt) ? (o = fu, qt++) : (o = null, 0 === Wt && e(su)), \n\t                null !== o ? (Lt = l, u = hu(t, r), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t                l = il)) : (qt = l, l = il);\n\t            } else qt = l, l = il; else qt = l, l = il;\n\t            return Wt--, null === l && (u = null, 0 === Wt && e(cu)), l;\n\t        }\n\t        function S() {\n\t            var l, u, t, r;\n\t            return Wt++, l = qt, u = U(), null !== u ? (45 === n.charCodeAt(qt) ? (t = pu, qt++) : (t = null, \n\t            0 === Wt && e(vu)), null !== t ? (r = U(), null !== r ? (Lt = l, u = wu(u, r), null === u ? (qt = l, \n\t            l = u) : l = u) : (qt = l, l = il)) : (qt = l, l = il)) : (qt = l, l = il), Wt--, \n\t            null === l && (u = null, 0 === Wt && e(du)), l;\n\t        }\n\t        function U() {\n\t            var n, l;\n\t            return Wt++, n = G(), null === n && (n = E()), Wt--, null === n && (l = null, 0 === Wt && e(Au)), \n\t            n;\n\t        }\n\t        function E() {\n\t            var l, u;\n\t            return l = qt, Cu.test(n.charAt(qt)) ? (u = n.charAt(qt), qt++) : (u = null, 0 === Wt && e(gu)), \n\t            null !== u && (Lt = l, u = bu(u)), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function G() {\n\t            var n;\n\t            return n = L(), null === n && (n = Y(), null === n && (n = H(), null === n && (n = O(), \n\t            null === n && (n = W(), null === n && (n = z(), null === n && (n = I(), null === n && (n = J(), \n\t            null === n && (n = K(), null === n && (n = N(), null === n && (n = P(), null === n && (n = V(), \n\t            null === n && (n = X(), null === n && (n = _(), null === n && (n = nl(), null === n && (n = ll(), \n\t            null === n && (n = ul(), null === n && (n = tl()))))))))))))))))), n;\n\t        }\n\t        function B() {\n\t            var n;\n\t            return n = j(), null === n && (n = q(), null === n && (n = $())), n;\n\t        }\n\t        function j() {\n\t            var l, u;\n\t            return l = qt, 46 === n.charCodeAt(qt) ? (u = ku, qt++) : (u = null, 0 === Wt && e(Tu)), \n\t            null !== u && (Lt = l, u = xu()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function $() {\n\t            var l, u;\n\t            return Wt++, l = qt, mu.test(n.charAt(qt)) ? (u = n.charAt(qt), qt++) : (u = null, \n\t            0 === Wt && e(Ru)), null !== u && (Lt = l, u = bu(u)), null === u ? (qt = l, l = u) : l = u, \n\t            Wt--, null === l && (u = null, 0 === Wt && e(yu)), l;\n\t        }\n\t        function q() {\n\t            var n;\n\t            return n = M(), null === n && (n = D(), null === n && (n = Y(), null === n && (n = H(), \n\t            null === n && (n = O(), null === n && (n = W(), null === n && (n = z(), null === n && (n = I(), \n\t            null === n && (n = J(), null === n && (n = K(), null === n && (n = N(), null === n && (n = P(), \n\t            null === n && (n = V(), null === n && (n = X(), null === n && (n = Z(), null === n && (n = _(), \n\t            null === n && (n = nl(), null === n && (n = ll(), null === n && (n = ul(), null === n && (n = tl()))))))))))))))))))), \n\t            n;\n\t        }\n\t        function L() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Fu ? (u = Fu, qt += 2) : (u = null, 0 === Wt && e(Qu)), \n\t            null !== u && (Lt = l, u = Su()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function M() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Fu ? (u = Fu, qt += 2) : (u = null, 0 === Wt && e(Qu)), \n\t            null !== u && (Lt = l, u = Uu()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function D() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Eu ? (u = Eu, qt += 2) : (u = null, 0 === Wt && e(Gu)), \n\t            null !== u && (Lt = l, u = Bu()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function H() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === ju ? (u = ju, qt += 2) : (u = null, 0 === Wt && e($u)), \n\t            null !== u && (Lt = l, u = qu()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function O() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Lu ? (u = Lu, qt += 2) : (u = null, 0 === Wt && e(Mu)), \n\t            null !== u && (Lt = l, u = Du()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function W() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Hu ? (u = Hu, qt += 2) : (u = null, 0 === Wt && e(Ou)), \n\t            null !== u && (Lt = l, u = Wu()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function z() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === zu ? (u = zu, qt += 2) : (u = null, 0 === Wt && e(Iu)), \n\t            null !== u && (Lt = l, u = Ju()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function I() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Ku ? (u = Ku, qt += 2) : (u = null, 0 === Wt && e(Nu)), \n\t            null !== u && (Lt = l, u = Pu()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function J() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Vu ? (u = Vu, qt += 2) : (u = null, 0 === Wt && e(Xu)), \n\t            null !== u && (Lt = l, u = Yu()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function K() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Zu ? (u = Zu, qt += 2) : (u = null, 0 === Wt && e(_u)), \n\t            null !== u && (Lt = l, u = nt()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function N() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === lt ? (u = lt, qt += 2) : (u = null, 0 === Wt && e(ut)), \n\t            null !== u && (Lt = l, u = tt()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function P() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === rt ? (u = rt, qt += 2) : (u = null, 0 === Wt && e(et)), \n\t            null !== u && (Lt = l, u = ot()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function V() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === ct ? (u = ct, qt += 2) : (u = null, 0 === Wt && e(it)), \n\t            null !== u && (Lt = l, u = at()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function X() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === ft ? (u = ft, qt += 2) : (u = null, 0 === Wt && e(st)), \n\t            null !== u && (Lt = l, u = ht()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function Y() {\n\t            var l, u, t;\n\t            return l = qt, n.substr(qt, 2) === dt ? (u = dt, qt += 2) : (u = null, 0 === Wt && e(pt)), \n\t            null !== u ? (n.length > qt ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(vt)), \n\t            null !== t ? (Lt = l, u = wt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function Z() {\n\t            var l, u, t;\n\t            return l = qt, 92 === n.charCodeAt(qt) ? (u = At, qt++) : (u = null, 0 === Wt && e(Ct)), \n\t            null !== u ? (gt.test(n.charAt(qt)) ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(bt)), \n\t            null !== t ? (Lt = l, u = kt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il), l;\n\t        }\n\t        function _() {\n\t            var l, u, t, r;\n\t            if (l = qt, n.substr(qt, 2) === Tt ? (u = Tt, qt += 2) : (u = null, 0 === Wt && e(xt)), \n\t            null !== u) {\n\t                if (t = [], yt.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(mt)), \n\t                null !== r) for (;null !== r; ) t.push(r), yt.test(n.charAt(qt)) ? (r = n.charAt(qt), \n\t                qt++) : (r = null, 0 === Wt && e(mt)); else t = il;\n\t                null !== t ? (Lt = l, u = Rt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t                l = il);\n\t            } else qt = l, l = il;\n\t            return l;\n\t        }\n\t        function nl() {\n\t            var l, u, t, r;\n\t            if (l = qt, n.substr(qt, 2) === Ft ? (u = Ft, qt += 2) : (u = null, 0 === Wt && e(Qt)), \n\t            null !== u) {\n\t                if (t = [], St.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(Ut)), \n\t                null !== r) for (;null !== r; ) t.push(r), St.test(n.charAt(qt)) ? (r = n.charAt(qt), \n\t                qt++) : (r = null, 0 === Wt && e(Ut)); else t = il;\n\t                null !== t ? (Lt = l, u = Et(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t                l = il);\n\t            } else qt = l, l = il;\n\t            return l;\n\t        }\n\t        function ll() {\n\t            var l, u, t, r;\n\t            if (l = qt, n.substr(qt, 2) === Gt ? (u = Gt, qt += 2) : (u = null, 0 === Wt && e(Bt)), \n\t            null !== u) {\n\t                if (t = [], St.test(n.charAt(qt)) ? (r = n.charAt(qt), qt++) : (r = null, 0 === Wt && e(Ut)), \n\t                null !== r) for (;null !== r; ) t.push(r), St.test(n.charAt(qt)) ? (r = n.charAt(qt), \n\t                qt++) : (r = null, 0 === Wt && e(Ut)); else t = il;\n\t                null !== t ? (Lt = l, u = jt(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t                l = il);\n\t            } else qt = l, l = il;\n\t            return l;\n\t        }\n\t        function ul() {\n\t            var l, u;\n\t            return l = qt, n.substr(qt, 2) === Tt ? (u = Tt, qt += 2) : (u = null, 0 === Wt && e(xt)), \n\t            null !== u && (Lt = l, u = $t()), null === u ? (qt = l, l = u) : l = u, l;\n\t        }\n\t        function tl() {\n\t            var l, u, t;\n\t            return l = qt, 92 === n.charCodeAt(qt) ? (u = At, qt++) : (u = null, 0 === Wt && e(Ct)), \n\t            null !== u ? (n.length > qt ? (t = n.charAt(qt), qt++) : (t = null, 0 === Wt && e(vt)), \n\t            null !== t ? (Lt = l, u = bu(t), null === u ? (qt = l, l = u) : l = u) : (qt = l, \n\t            l = il)) : (qt = l, l = il), l;\n\t        }\n\t        var rl, el = arguments.length > 1 ? arguments[1] : {}, ol = {\n\t            regexp: c\n\t        }, cl = c, il = null, al = \"\", fl = \"|\", sl = '\"|\"', hl = function(n, l) {\n\t            return l ? new Alternate(n, l[1]) : n;\n\t        }, dl = function(n, l, u) {\n\t            return new Match([ n ].concat(l).concat([ u ]));\n\t        }, pl = \"^\", vl = '\"^\"', wl = function() {\n\t            return new Token(\"start\");\n\t        }, Al = \"$\", Cl = '\"$\"', gl = function() {\n\t            return new Token(\"end\");\n\t        }, bl = function(n, l) {\n\t            return new Quantified(n, l);\n\t        }, kl = \"Quantifier\", Tl = function(n, l) {\n\t            return l && (n.greedy = !1), n;\n\t        }, xl = \"{\", yl = '\"{\"', ml = \",\", Rl = '\",\"', Fl = \"}\", Ql = '\"}\"', Sl = function(n, l) {\n\t            return new Quantifier(n, l);\n\t        }, Ul = \",}\", El = '\",}\"', Gl = function(n) {\n\t            return new Quantifier(n, 1/0);\n\t        }, Bl = function(n) {\n\t            return new Quantifier(n, n);\n\t        }, jl = \"+\", $l = '\"+\"', ql = function() {\n\t            return new Quantifier(1, 1/0);\n\t        }, Ll = \"*\", Ml = '\"*\"', Dl = function() {\n\t            return new Quantifier(0, 1/0);\n\t        }, Hl = \"?\", Ol = '\"?\"', Wl = function() {\n\t            return new Quantifier(0, 1);\n\t        }, zl = /^[0-9]/, Il = \"[0-9]\", Jl = function(n) {\n\t            return +n.join(\"\");\n\t        }, Kl = \"(\", Nl = '\"(\"', Pl = \")\", Vl = '\")\"', Xl = function(n) {\n\t            return n;\n\t        }, Yl = function(n) {\n\t            return new CaptureGroup(n);\n\t        }, Zl = \"?:\", _l = '\"?:\"', nu = function(n) {\n\t            return new Group(\"non-capture-group\", n);\n\t        }, lu = \"?=\", uu = '\"?=\"', tu = function(n) {\n\t            return new Group(\"positive-lookahead\", n);\n\t        }, ru = \"?!\", eu = '\"?!\"', ou = function(n) {\n\t            return new Group(\"negative-lookahead\", n);\n\t        }, cu = \"CharacterSet\", iu = \"[\", au = '\"[\"', fu = \"]\", su = '\"]\"', hu = function(n, l) {\n\t            return new CharSet(!!n, l);\n\t        }, du = \"CharacterRange\", pu = \"-\", vu = '\"-\"', wu = function(n, l) {\n\t            return new CharacterRange(n, l);\n\t        }, Au = \"Character\", Cu = /^[^\\\\\\]]/, gu = \"[^\\\\\\\\\\\\]]\", bu = function(n) {\n\t            return new Literal(n);\n\t        }, ku = \".\", Tu = '\".\"', xu = function() {\n\t            return new Token(\"any-character\");\n\t        }, yu = \"Literal\", mu = /^[^|\\\\\\/.[()?+*$\\^]/, Ru = \"[^|\\\\\\\\\\\\/.[()?+*$\\\\^]\", Fu = \"\\\\b\", Qu = '\"\\\\\\\\b\"', Su = function() {\n\t            return new Token(\"backspace\");\n\t        }, Uu = function() {\n\t            return new Token(\"word-boundary\");\n\t        }, Eu = \"\\\\B\", Gu = '\"\\\\\\\\B\"', Bu = function() {\n\t            return new Token(\"non-word-boundary\");\n\t        }, ju = \"\\\\d\", $u = '\"\\\\\\\\d\"', qu = function() {\n\t            return new Token(\"digit\");\n\t        }, Lu = \"\\\\D\", Mu = '\"\\\\\\\\D\"', Du = function() {\n\t            return new Token(\"non-digit\");\n\t        }, Hu = \"\\\\f\", Ou = '\"\\\\\\\\f\"', Wu = function() {\n\t            return new Token(\"form-feed\");\n\t        }, zu = \"\\\\n\", Iu = '\"\\\\\\\\n\"', Ju = function() {\n\t            return new Token(\"line-feed\");\n\t        }, Ku = \"\\\\r\", Nu = '\"\\\\\\\\r\"', Pu = function() {\n\t            return new Token(\"carriage-return\");\n\t        }, Vu = \"\\\\s\", Xu = '\"\\\\\\\\s\"', Yu = function() {\n\t            return new Token(\"white-space\");\n\t        }, Zu = \"\\\\S\", _u = '\"\\\\\\\\S\"', nt = function() {\n\t            return new Token(\"non-white-space\");\n\t        }, lt = \"\\\\t\", ut = '\"\\\\\\\\t\"', tt = function() {\n\t            return new Token(\"tab\");\n\t        }, rt = \"\\\\v\", et = '\"\\\\\\\\v\"', ot = function() {\n\t            return new Token(\"vertical-tab\");\n\t        }, ct = \"\\\\w\", it = '\"\\\\\\\\w\"', at = function() {\n\t            return new Token(\"word\");\n\t        }, ft = \"\\\\W\", st = '\"\\\\\\\\W\"', ht = function() {\n\t            return new Token(\"non-word\");\n\t        }, dt = \"\\\\c\", pt = '\"\\\\\\\\c\"', vt = \"any character\", wt = function(n) {\n\t            return new ControlCharacter(n);\n\t        }, At = \"\\\\\", Ct = '\"\\\\\\\\\"', gt = /^[1-9]/, bt = \"[1-9]\", kt = function(n) {\n\t            return new BackReference(n);\n\t        }, Tt = \"\\\\0\", xt = '\"\\\\\\\\0\"', yt = /^[0-7]/, mt = \"[0-7]\", Rt = function(n) {\n\t            return new Octal(n.join(\"\"));\n\t        }, Ft = \"\\\\x\", Qt = '\"\\\\\\\\x\"', St = /^[0-9a-fA-F]/, Ut = \"[0-9a-fA-F]\", Et = function(n) {\n\t            return new Hex(n.join(\"\"));\n\t        }, Gt = \"\\\\u\", Bt = '\"\\\\\\\\u\"', jt = function(n) {\n\t            return new Unicode(n.join(\"\"));\n\t        }, $t = function() {\n\t            return new Token(\"null-character\");\n\t        }, qt = 0, Lt = 0, Mt = 0, Dt = {\n\t            line: 1,\n\t            column: 1,\n\t            seenCR: !1\n\t        }, Ht = 0, Ot = [], Wt = 0;\n\t        if (\"startRule\" in el) {\n\t            if (!(el.startRule in ol)) throw new Error(\"Can't start parsing from rule \\\"\" + el.startRule + '\".');\n\t            cl = ol[el.startRule];\n\t        }\n\t        if (Token.offset = t, Token.text = u, rl = cl(), null !== rl && qt === n.length) return rl;\n\t        throw o(Ot), Lt = Math.max(qt, Ht), new l(Ot, Lt < n.length ? n.charAt(Lt) : null, Lt, r(Lt).line, r(Lt).column);\n\t    }\n\t    return n(l, Error), {\n\t        SyntaxError: l,\n\t        parse: u\n\t    };\n\t}(), index = 1, cgs = {};\n\n\tmodule.exports = parser\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## RegExp Handler\n\n\t    https://github.com/ForbesLindesay/regexp\n\t    https://github.com/dmajda/pegjs\n\t    http://www.regexper.com/\n\n\t    每个节点的结构\n\t        {\n\t            type: '',\n\t            offset: number,\n\t            text: '',\n\t            body: {},\n\t            escaped: true/false\n\t        }\n\n\t    type 可选值\n\t        alternate             |         选择\n\t        match                 匹配\n\t        capture-group         ()        捕获组\n\t        non-capture-group     (?:...)   非捕获组\n\t        positive-lookahead    (?=p)     零宽正向先行断言\n\t        negative-lookahead    (?!p)     零宽负向先行断言\n\t        quantified            a*        重复节点\n\t        quantifier            *         量词\n\t        charset               []        字符集\n\t        range                 {m, n}    范围\n\t        literal               a         直接量字符\n\t        unicode               \\uxxxx    Unicode\n\t        hex                   \\x        十六进制\n\t        octal                 八进制\n\t        back-reference        \\n        反向引用\n\t        control-character     \\cX       控制字符\n\n\t        // Token\n\t        start               ^       开头\n\t        end                 $       结尾\n\t        any-character       .       任意字符\n\t        backspace           [\\b]    退格直接量\n\t        word-boundary       \\b      单词边界\n\t        non-word-boundary   \\B      非单词边界\n\t        digit               \\d      ASCII 数字，[0-9]\n\t        non-digit           \\D      非 ASCII 数字，[^0-9]\n\t        form-feed           \\f      换页符\n\t        line-feed           \\n      换行符\n\t        carriage-return     \\r      回车符\n\t        white-space         \\s      空白符\n\t        non-white-space     \\S      非空白符\n\t        tab                 \\t      制表符\n\t        vertical-tab        \\v      垂直制表符\n\t        word                \\w      ASCII 字符，[a-zA-Z0-9]\n\t        non-word            \\W      非 ASCII 字符，[^a-zA-Z0-9]\n\t        null-character      \\o      NUL 字符\n\t */\n\n\tvar Util = __webpack_require__(3)\n\tvar Random = __webpack_require__(5)\n\t    /*\n\t        \n\t    */\n\tvar Handler = {\n\t    extend: Util.extend\n\t}\n\n\t// http://en.wikipedia.org/wiki/ASCII#ASCII_printable_code_chart\n\t/*var ASCII_CONTROL_CODE_CHART = {\n\t    '@': ['\\u0000'],\n\t    A: ['\\u0001'],\n\t    B: ['\\u0002'],\n\t    C: ['\\u0003'],\n\t    D: ['\\u0004'],\n\t    E: ['\\u0005'],\n\t    F: ['\\u0006'],\n\t    G: ['\\u0007', '\\a'],\n\t    H: ['\\u0008', '\\b'],\n\t    I: ['\\u0009', '\\t'],\n\t    J: ['\\u000A', '\\n'],\n\t    K: ['\\u000B', '\\v'],\n\t    L: ['\\u000C', '\\f'],\n\t    M: ['\\u000D', '\\r'],\n\t    N: ['\\u000E'],\n\t    O: ['\\u000F'],\n\t    P: ['\\u0010'],\n\t    Q: ['\\u0011'],\n\t    R: ['\\u0012'],\n\t    S: ['\\u0013'],\n\t    T: ['\\u0014'],\n\t    U: ['\\u0015'],\n\t    V: ['\\u0016'],\n\t    W: ['\\u0017'],\n\t    X: ['\\u0018'],\n\t    Y: ['\\u0019'],\n\t    Z: ['\\u001A'],\n\t    '[': ['\\u001B', '\\e'],\n\t    '\\\\': ['\\u001C'],\n\t    ']': ['\\u001D'],\n\t    '^': ['\\u001E'],\n\t    '_': ['\\u001F']\n\t}*/\n\n\t// ASCII printable code chart\n\t// var LOWER = 'abcdefghijklmnopqrstuvwxyz'\n\t// var UPPER = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'\n\t// var NUMBER = '0123456789'\n\t// var SYMBOL = ' !\"#$%&\\'()*+,-./' + ':;<=>?@' + '[\\\\]^_`' + '{|}~'\n\tvar LOWER = ascii(97, 122)\n\tvar UPPER = ascii(65, 90)\n\tvar NUMBER = ascii(48, 57)\n\tvar OTHER = ascii(32, 47) + ascii(58, 64) + ascii(91, 96) + ascii(123, 126) // 排除 95 _ ascii(91, 94) + ascii(96, 96)\n\tvar PRINTABLE = ascii(32, 126)\n\tvar SPACE = ' \\f\\n\\r\\t\\v\\u00A0\\u2028\\u2029'\n\tvar CHARACTER_CLASSES = {\n\t    '\\\\w': LOWER + UPPER + NUMBER + '_', // ascii(95, 95)\n\t    '\\\\W': OTHER.replace('_', ''),\n\t    '\\\\s': SPACE,\n\t    '\\\\S': function() {\n\t        var result = PRINTABLE\n\t        for (var i = 0; i < SPACE.length; i++) {\n\t            result = result.replace(SPACE[i], '')\n\t        }\n\t        return result\n\t    }(),\n\t    '\\\\d': NUMBER,\n\t    '\\\\D': LOWER + UPPER + OTHER\n\t}\n\n\tfunction ascii(from, to) {\n\t    var result = ''\n\t    for (var i = from; i <= to; i++) {\n\t        result += String.fromCharCode(i)\n\t    }\n\t    return result\n\t}\n\n\t// var ast = RegExpParser.parse(regexp.source)\n\tHandler.gen = function(node, result, cache) {\n\t    cache = cache || {\n\t        guid: 1\n\t    }\n\t    return Handler[node.type] ? Handler[node.type](node, result, cache) :\n\t        Handler.token(node, result, cache)\n\t}\n\n\tHandler.extend({\n\t    /* jshint unused:false */\n\t    token: function(node, result, cache) {\n\t        switch (node.type) {\n\t            case 'start':\n\t            case 'end':\n\t                return ''\n\t            case 'any-character':\n\t                return Random.character()\n\t            case 'backspace':\n\t                return ''\n\t            case 'word-boundary': // TODO\n\t                return ''\n\t            case 'non-word-boundary': // TODO\n\t                break\n\t            case 'digit':\n\t                return Random.pick(\n\t                    NUMBER.split('')\n\t                )\n\t            case 'non-digit':\n\t                return Random.pick(\n\t                    (LOWER + UPPER + OTHER).split('')\n\t                )\n\t            case 'form-feed':\n\t                break\n\t            case 'line-feed':\n\t                return node.body || node.text\n\t            case 'carriage-return':\n\t                break\n\t            case 'white-space':\n\t                return Random.pick(\n\t                    SPACE.split('')\n\t                )\n\t            case 'non-white-space':\n\t                return Random.pick(\n\t                    (LOWER + UPPER + NUMBER).split('')\n\t                )\n\t            case 'tab':\n\t                break\n\t            case 'vertical-tab':\n\t                break\n\t            case 'word': // \\w [a-zA-Z0-9]\n\t                return Random.pick(\n\t                    (LOWER + UPPER + NUMBER).split('')\n\t                )\n\t            case 'non-word': // \\W [^a-zA-Z0-9]\n\t                return Random.pick(\n\t                    OTHER.replace('_', '').split('')\n\t                )\n\t            case 'null-character':\n\t                break\n\t        }\n\t        return node.body || node.text\n\t    },\n\t    /*\n\t        {\n\t            type: 'alternate',\n\t            offset: 0,\n\t            text: '',\n\t            left: {\n\t                boyd: []\n\t            },\n\t            right: {\n\t                boyd: []\n\t            }\n\t        }\n\t    */\n\t    alternate: function(node, result, cache) {\n\t        // node.left/right {}\n\t        return this.gen(\n\t            Random.boolean() ? node.left : node.right,\n\t            result,\n\t            cache\n\t        )\n\t    },\n\t    /*\n\t        {\n\t            type: 'match',\n\t            offset: 0,\n\t            text: '',\n\t            body: []\n\t        }\n\t    */\n\t    match: function(node, result, cache) {\n\t        result = ''\n\t            // node.body []\n\t        for (var i = 0; i < node.body.length; i++) {\n\t            result += this.gen(node.body[i], result, cache)\n\t        }\n\t        return result\n\t    },\n\t    // ()\n\t    'capture-group': function(node, result, cache) {\n\t        // node.body {}\n\t        result = this.gen(node.body, result, cache)\n\t        cache[cache.guid++] = result\n\t        return result\n\t    },\n\t    // (?:...)\n\t    'non-capture-group': function(node, result, cache) {\n\t        // node.body {}\n\t        return this.gen(node.body, result, cache)\n\t    },\n\t    // (?=p)\n\t    'positive-lookahead': function(node, result, cache) {\n\t        // node.body\n\t        return this.gen(node.body, result, cache)\n\t    },\n\t    // (?!p)\n\t    'negative-lookahead': function(node, result, cache) {\n\t        // node.body\n\t        return ''\n\t    },\n\t    /*\n\t        {\n\t            type: 'quantified',\n\t            offset: 3,\n\t            text: 'c*',\n\t            body: {\n\t                type: 'literal',\n\t                offset: 3,\n\t                text: 'c',\n\t                body: 'c',\n\t                escaped: false\n\t            },\n\t            quantifier: {\n\t                type: 'quantifier',\n\t                offset: 4,\n\t                text: '*',\n\t                min: 0,\n\t                max: Infinity,\n\t                greedy: true\n\t            }\n\t        }\n\t    */\n\t    quantified: function(node, result, cache) {\n\t        result = ''\n\t            // node.quantifier {}\n\t        var count = this.quantifier(node.quantifier);\n\t        // node.body {}\n\t        for (var i = 0; i < count; i++) {\n\t            result += this.gen(node.body, result, cache)\n\t        }\n\t        return result\n\t    },\n\t    /*\n\t        quantifier: {\n\t            type: 'quantifier',\n\t            offset: 4,\n\t            text: '*',\n\t            min: 0,\n\t            max: Infinity,\n\t            greedy: true\n\t        }\n\t    */\n\t    quantifier: function(node, result, cache) {\n\t        var min = Math.max(node.min, 0)\n\t        var max = isFinite(node.max) ? node.max :\n\t            min + Random.integer(3, 7)\n\t        return Random.integer(min, max)\n\t    },\n\t    /*\n\t        \n\t    */\n\t    charset: function(node, result, cache) {\n\t        // node.invert\n\t        if (node.invert) return this['invert-charset'](node, result, cache)\n\n\t        // node.body []\n\t        var literal = Random.pick(node.body)\n\t        return this.gen(literal, result, cache)\n\t    },\n\t    'invert-charset': function(node, result, cache) {\n\t        var pool = PRINTABLE\n\t        for (var i = 0, item; i < node.body.length; i++) {\n\t            item = node.body[i]\n\t            switch (item.type) {\n\t                case 'literal':\n\t                    pool = pool.replace(item.body, '')\n\t                    break\n\t                case 'range':\n\t                    var min = this.gen(item.start, result, cache).charCodeAt()\n\t                    var max = this.gen(item.end, result, cache).charCodeAt()\n\t                    for (var ii = min; ii <= max; ii++) {\n\t                        pool = pool.replace(String.fromCharCode(ii), '')\n\t                    }\n\t                    /* falls through */\n\t                default:\n\t                    var characters = CHARACTER_CLASSES[item.text]\n\t                    if (characters) {\n\t                        for (var iii = 0; iii <= characters.length; iii++) {\n\t                            pool = pool.replace(characters[iii], '')\n\t                        }\n\t                    }\n\t            }\n\t        }\n\t        return Random.pick(pool.split(''))\n\t    },\n\t    range: function(node, result, cache) {\n\t        // node.start, node.end\n\t        var min = this.gen(node.start, result, cache).charCodeAt()\n\t        var max = this.gen(node.end, result, cache).charCodeAt()\n\t        return String.fromCharCode(\n\t            Random.integer(min, max)\n\t        )\n\t    },\n\t    literal: function(node, result, cache) {\n\t        return node.escaped ? node.body : node.text\n\t    },\n\t    // Unicode \\u\n\t    unicode: function(node, result, cache) {\n\t        return String.fromCharCode(\n\t            parseInt(node.code, 16)\n\t        )\n\t    },\n\t    // 十六进制 \\xFF\n\t    hex: function(node, result, cache) {\n\t        return String.fromCharCode(\n\t            parseInt(node.code, 16)\n\t        )\n\t    },\n\t    // 八进制 \\0\n\t    octal: function(node, result, cache) {\n\t        return String.fromCharCode(\n\t            parseInt(node.code, 8)\n\t        )\n\t    },\n\t    // 反向引用\n\t    'back-reference': function(node, result, cache) {\n\t        return cache[node.code] || ''\n\t    },\n\t    /*\n\t        http://en.wikipedia.org/wiki/C0_and_C1_control_codes\n\t    */\n\t    CONTROL_CHARACTER_MAP: function() {\n\t        var CONTROL_CHARACTER = '@ A B C D E F G H I J K L M N O P Q R S T U V W X Y Z [ \\\\ ] ^ _'.split(' ')\n\t        var CONTROL_CHARACTER_UNICODE = '\\u0000 \\u0001 \\u0002 \\u0003 \\u0004 \\u0005 \\u0006 \\u0007 \\u0008 \\u0009 \\u000A \\u000B \\u000C \\u000D \\u000E \\u000F \\u0010 \\u0011 \\u0012 \\u0013 \\u0014 \\u0015 \\u0016 \\u0017 \\u0018 \\u0019 \\u001A \\u001B \\u001C \\u001D \\u001E \\u001F'.split(' ')\n\t        var map = {}\n\t        for (var i = 0; i < CONTROL_CHARACTER.length; i++) {\n\t            map[CONTROL_CHARACTER[i]] = CONTROL_CHARACTER_UNICODE[i]\n\t        }\n\t        return map\n\t    }(),\n\t    'control-character': function(node, result, cache) {\n\t        return this.CONTROL_CHARACTER_MAP[node.code]\n\t    }\n\t})\n\n\tmodule.exports = Handler\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tmodule.exports = __webpack_require__(24)\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## toJSONSchema\n\n\t    把 Mock.js 风格的数据模板转换成 JSON Schema。\n\n\t    > [JSON Schema](http://json-schema.org/)\n\t */\n\tvar Constant = __webpack_require__(2)\n\tvar Util = __webpack_require__(3)\n\tvar Parser = __webpack_require__(4)\n\n\tfunction toJSONSchema(template, name, path /* Internal Use Only */ ) {\n\t    // type rule properties items\n\t    path = path || []\n\t    var result = {\n\t        name: typeof name === 'string' ? name.replace(Constant.RE_KEY, '$1') : name,\n\t        template: template,\n\t        type: Util.type(template), // 可能不准确，例如 { 'name|1': [{}, {} ...] }\n\t        rule: Parser.parse(name)\n\t    }\n\t    result.path = path.slice(0)\n\t    result.path.push(name === undefined ? 'ROOT' : result.name)\n\n\t    switch (result.type) {\n\t        case 'array':\n\t            result.items = []\n\t            Util.each(template, function(value, index) {\n\t                result.items.push(\n\t                    toJSONSchema(value, index, result.path)\n\t                )\n\t            })\n\t            break\n\t        case 'object':\n\t            result.properties = []\n\t            Util.each(template, function(value, name) {\n\t                result.properties.push(\n\t                    toJSONSchema(value, name, result.path)\n\t                )\n\t            })\n\t            break\n\t    }\n\n\t    return result\n\n\t}\n\n\tmodule.exports = toJSONSchema\n\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tmodule.exports = __webpack_require__(26)\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*\n\t    ## valid(template, data)\n\n\t    校验真实数据 data 是否与数据模板 template 匹配。\n\t    \n\t    实现思路：\n\t    1. 解析规则。\n\t        先把数据模板 template 解析为更方便机器解析的 JSON-Schame\n\t        name               属性名 \n\t        type               属性值类型\n\t        template           属性值模板\n\t        properties         对象属性数组\n\t        items              数组元素数组\n\t        rule               属性值生成规则\n\t    2. 递归验证规则。\n\t        然后用 JSON-Schema 校验真实数据，校验项包括属性名、值类型、值、值生成规则。\n\n\t    提示信息 \n\t    https://github.com/fge/json-schema-validator/blob/master/src/main/resources/com/github/fge/jsonschema/validator/validation.properties\n\t    [JSON-Schama validator](http://json-schema-validator.herokuapp.com/)\n\t    [Regexp Demo](http://demos.forbeslindesay.co.uk/regexp/)\n\t*/\n\tvar Constant = __webpack_require__(2)\n\tvar Util = __webpack_require__(3)\n\tvar toJSONSchema = __webpack_require__(23)\n\n\tfunction valid(template, data) {\n\t    var schema = toJSONSchema(template)\n\t    var result = Diff.diff(schema, data)\n\t    for (var i = 0; i < result.length; i++) {\n\t        // console.log(template, data)\n\t        // console.warn(Assert.message(result[i]))\n\t    }\n\t    return result\n\t}\n\n\t/*\n\t    ## name\n\t        有生成规则：比较解析后的 name\n\t        无生成规则：直接比较\n\t    ## type\n\t        无类型转换：直接比较\n\t        有类型转换：先试着解析 template，然后再检查？\n\t    ## value vs. template\n\t        基本类型\n\t            无生成规则：直接比较\n\t            有生成规则：\n\t                number\n\t                    min-max.dmin-dmax\n\t                    min-max.dcount\n\t                    count.dmin-dmax\n\t                    count.dcount\n\t                    +step\n\t                    整数部分\n\t                    小数部分\n\t                boolean \n\t                string  \n\t                    min-max\n\t                    count\n\t    ## properties\n\t        对象\n\t            有生成规则：检测期望的属性个数，继续递归\n\t            无生成规则：检测全部的属性个数，继续递归\n\t    ## items\n\t        数组\n\t            有生成规则：\n\t                `'name|1': [{}, {} ...]`            其中之一，继续递归\n\t                `'name|+1': [{}, {} ...]`           顺序检测，继续递归\n\t                `'name|min-max': [{}, {} ...]`      检测个数，继续递归\n\t                `'name|count': [{}, {} ...]`        检测个数，继续递归\n\t            无生成规则：检测全部的元素个数，继续递归\n\t*/\n\tvar Diff = {\n\t    diff: function diff(schema, data, name /* Internal Use Only */ ) {\n\t        var result = []\n\n\t        // 先检测名称 name 和类型 type，如果匹配，才有必要继续检测\n\t        if (\n\t            this.name(schema, data, name, result) &&\n\t            this.type(schema, data, name, result)\n\t        ) {\n\t            this.value(schema, data, name, result)\n\t            this.properties(schema, data, name, result)\n\t            this.items(schema, data, name, result)\n\t        }\n\n\t        return result\n\t    },\n\t    /* jshint unused:false */\n\t    name: function(schema, data, name, result) {\n\t        var length = result.length\n\n\t        Assert.equal('name', schema.path, name + '', schema.name + '', result)\n\n\t        return result.length === length\n\t    },\n\t    type: function(schema, data, name, result) {\n\t        var length = result.length\n\n\t        switch (schema.type) {\n\t            case 'string':\n\t                // 跳过含有『占位符』的属性值，因为『占位符』返回值的类型可能和模板不一致，例如 '@int' 会返回一个整形值\n\t                if (schema.template.match(Constant.RE_PLACEHOLDER)) return true\n\t                break\n\t            case 'array':\n\t                if (schema.rule.parameters) {\n\t                    // name|count: array\n\t                    if (schema.rule.min !== undefined && schema.rule.max === undefined) {\n\t                        // 跳过 name|1: array，因为最终值的类型（很可能）不是数组，也不一定与 `array` 中的类型一致\n\t                        if (schema.rule.count === 1) return true\n\t                    }\n\t                    // 跳过 name|+inc: array\n\t                    if (schema.rule.parameters[2]) return true\n\t                }\n\t                break\n\t            case 'function':\n\t                // 跳过 `'name': function`，因为函数可以返回任何类型的值。\n\t                return true\n\t        }\n\n\t        Assert.equal('type', schema.path, Util.type(data), schema.type, result)\n\n\t        return result.length === length\n\t    },\n\t    value: function(schema, data, name, result) {\n\t        var length = result.length\n\n\t        var rule = schema.rule\n\t        var templateType = schema.type\n\t        if (templateType === 'object' || templateType === 'array' || templateType === 'function') return true\n\n\t        // 无生成规则\n\t        if (!rule.parameters) {\n\t            switch (templateType) {\n\t                case 'regexp':\n\t                    Assert.match('value', schema.path, data, schema.template, result)\n\t                    return result.length === length\n\t                case 'string':\n\t                    // 同样跳过含有『占位符』的属性值，因为『占位符』的返回值会通常会与模板不一致\n\t                    if (schema.template.match(Constant.RE_PLACEHOLDER)) return result.length === length\n\t                    break\n\t            }\n\t            Assert.equal('value', schema.path, data, schema.template, result)\n\t            return result.length === length\n\t        }\n\n\t        // 有生成规则\n\t        var actualRepeatCount\n\t        switch (templateType) {\n\t            case 'number':\n\t                var parts = (data + '').split('.')\n\t                parts[0] = +parts[0]\n\n\t                // 整数部分\n\t                // |min-max\n\t                if (rule.min !== undefined && rule.max !== undefined) {\n\t                    Assert.greaterThanOrEqualTo('value', schema.path, parts[0], Math.min(rule.min, rule.max), result)\n\t                        // , 'numeric instance is lower than the required minimum (minimum: {expected}, found: {actual})')\n\t                    Assert.lessThanOrEqualTo('value', schema.path, parts[0], Math.max(rule.min, rule.max), result)\n\t                }\n\t                // |count\n\t                if (rule.min !== undefined && rule.max === undefined) {\n\t                    Assert.equal('value', schema.path, parts[0], rule.min, result, '[value] ' + name)\n\t                }\n\n\t                // 小数部分\n\t                if (rule.decimal) {\n\t                    // |dmin-dmax\n\t                    if (rule.dmin !== undefined && rule.dmax !== undefined) {\n\t                        Assert.greaterThanOrEqualTo('value', schema.path, parts[1].length, rule.dmin, result)\n\t                        Assert.lessThanOrEqualTo('value', schema.path, parts[1].length, rule.dmax, result)\n\t                    }\n\t                    // |dcount\n\t                    if (rule.dmin !== undefined && rule.dmax === undefined) {\n\t                        Assert.equal('value', schema.path, parts[1].length, rule.dmin, result)\n\t                    }\n\t                }\n\n\t                break\n\n\t            case 'boolean':\n\t                break\n\n\t            case 'string':\n\t                // 'aaa'.match(/a/g)\n\t                actualRepeatCount = data.match(new RegExp(schema.template, 'g'))\n\t                actualRepeatCount = actualRepeatCount ? actualRepeatCount.length : 0\n\n\t                // |min-max\n\t                if (rule.min !== undefined && rule.max !== undefined) {\n\t                    Assert.greaterThanOrEqualTo('repeat count', schema.path, actualRepeatCount, rule.min, result)\n\t                    Assert.lessThanOrEqualTo('repeat count', schema.path, actualRepeatCount, rule.max, result)\n\t                }\n\t                // |count\n\t                if (rule.min !== undefined && rule.max === undefined) {\n\t                    Assert.equal('repeat count', schema.path, actualRepeatCount, rule.min, result)\n\t                }\n\n\t                break\n\n\t            case 'regexp':\n\t                actualRepeatCount = data.match(new RegExp(schema.template.source.replace(/^\\^|\\$$/g, ''), 'g'))\n\t                actualRepeatCount = actualRepeatCount ? actualRepeatCount.length : 0\n\n\t                // |min-max\n\t                if (rule.min !== undefined && rule.max !== undefined) {\n\t                    Assert.greaterThanOrEqualTo('repeat count', schema.path, actualRepeatCount, rule.min, result)\n\t                    Assert.lessThanOrEqualTo('repeat count', schema.path, actualRepeatCount, rule.max, result)\n\t                }\n\t                // |count\n\t                if (rule.min !== undefined && rule.max === undefined) {\n\t                    Assert.equal('repeat count', schema.path, actualRepeatCount, rule.min, result)\n\t                }\n\t                break\n\t        }\n\n\t        return result.length === length\n\t    },\n\t    properties: function(schema, data, name, result) {\n\t        var length = result.length\n\n\t        var rule = schema.rule\n\t        var keys = Util.keys(data)\n\t        if (!schema.properties) return\n\n\t        // 无生成规则\n\t        if (!schema.rule.parameters) {\n\t            Assert.equal('properties length', schema.path, keys.length, schema.properties.length, result)\n\t        } else {\n\t            // 有生成规则\n\t            // |min-max\n\t            if (rule.min !== undefined && rule.max !== undefined) {\n\t                Assert.greaterThanOrEqualTo('properties length', schema.path, keys.length, Math.min(rule.min, rule.max), result)\n\t                Assert.lessThanOrEqualTo('properties length', schema.path, keys.length, Math.max(rule.min, rule.max), result)\n\t            }\n\t            // |count\n\t            if (rule.min !== undefined && rule.max === undefined) {\n\t                // |1, |>1\n\t                if (rule.count !== 1) Assert.equal('properties length', schema.path, keys.length, rule.min, result)\n\t            }\n\t        }\n\n\t        if (result.length !== length) return false\n\n\t        for (var i = 0; i < keys.length; i++) {\n\t            result.push.apply(\n\t                result,\n\t                this.diff(\n\t                    function() {\n\t                        var property\n\t                        Util.each(schema.properties, function(item /*, index*/ ) {\n\t                            if (item.name === keys[i]) property = item\n\t                        })\n\t                        return property || schema.properties[i]\n\t                    }(),\n\t                    data[keys[i]],\n\t                    keys[i]\n\t                )\n\t            )\n\t        }\n\n\t        return result.length === length\n\t    },\n\t    items: function(schema, data, name, result) {\n\t        var length = result.length\n\n\t        if (!schema.items) return\n\n\t        var rule = schema.rule\n\n\t        // 无生成规则\n\t        if (!schema.rule.parameters) {\n\t            Assert.equal('items length', schema.path, data.length, schema.items.length, result)\n\t        } else {\n\t            // 有生成规则\n\t            // |min-max\n\t            if (rule.min !== undefined && rule.max !== undefined) {\n\t                Assert.greaterThanOrEqualTo('items', schema.path, data.length, (Math.min(rule.min, rule.max) * schema.items.length), result,\n\t                    '[{utype}] array is too short: {path} must have at least {expected} elements but instance has {actual} elements')\n\t                Assert.lessThanOrEqualTo('items', schema.path, data.length, (Math.max(rule.min, rule.max) * schema.items.length), result,\n\t                    '[{utype}] array is too long: {path} must have at most {expected} elements but instance has {actual} elements')\n\t            }\n\t            // |count\n\t            if (rule.min !== undefined && rule.max === undefined) {\n\t                // |1, |>1\n\t                if (rule.count === 1) return result.length === length\n\t                else Assert.equal('items length', schema.path, data.length, (rule.min * schema.items.length), result)\n\t            }\n\t            // |+inc\n\t            if (rule.parameters[2]) return result.length === length\n\t        }\n\n\t        if (result.length !== length) return false\n\n\t        for (var i = 0; i < data.length; i++) {\n\t            result.push.apply(\n\t                result,\n\t                this.diff(\n\t                    schema.items[i % schema.items.length],\n\t                    data[i],\n\t                    i % schema.items.length\n\t                )\n\t            )\n\t        }\n\n\t        return result.length === length\n\t    }\n\t}\n\n\t/*\n\t    完善、友好的提示信息\n\t    \n\t    Equal, not equal to, greater than, less than, greater than or equal to, less than or equal to\n\t    路径 验证类型 描述 \n\n\t    Expect path.name is less than or equal to expected, but path.name is actual.\n\n\t    Expect path.name is less than or equal to expected, but path.name is actual.\n\t    Expect path.name is greater than or equal to expected, but path.name is actual.\n\n\t*/\n\tvar Assert = {\n\t    message: function(item) {\n\t        return (item.message ||\n\t                '[{utype}] Expect {path}\\'{ltype} {action} {expected}, but is {actual}')\n\t            .replace('{utype}', item.type.toUpperCase())\n\t            .replace('{ltype}', item.type.toLowerCase())\n\t            .replace('{path}', Util.isArray(item.path) && item.path.join('.') || item.path)\n\t            .replace('{action}', item.action)\n\t            .replace('{expected}', item.expected)\n\t            .replace('{actual}', item.actual)\n\t    },\n\t    equal: function(type, path, actual, expected, result, message) {\n\t        if (actual === expected) return true\n\t        switch (type) {\n\t            case 'type':\n\t                // 正则模板 === 字符串最终值\n\t                if (expected === 'regexp' && actual === 'string') return true\n\t                break\n\t        }\n\n\t        var item = {\n\t            path: path,\n\t            type: type,\n\t            actual: actual,\n\t            expected: expected,\n\t            action: 'is equal to',\n\t            message: message\n\t        }\n\t        item.message = Assert.message(item)\n\t        result.push(item)\n\t        return false\n\t    },\n\t    // actual matches expected\n\t    match: function(type, path, actual, expected, result, message) {\n\t        if (expected.test(actual)) return true\n\n\t        var item = {\n\t            path: path,\n\t            type: type,\n\t            actual: actual,\n\t            expected: expected,\n\t            action: 'matches',\n\t            message: message\n\t        }\n\t        item.message = Assert.message(item)\n\t        result.push(item)\n\t        return false\n\t    },\n\t    notEqual: function(type, path, actual, expected, result, message) {\n\t        if (actual !== expected) return true\n\t        var item = {\n\t            path: path,\n\t            type: type,\n\t            actual: actual,\n\t            expected: expected,\n\t            action: 'is not equal to',\n\t            message: message\n\t        }\n\t        item.message = Assert.message(item)\n\t        result.push(item)\n\t        return false\n\t    },\n\t    greaterThan: function(type, path, actual, expected, result, message) {\n\t        if (actual > expected) return true\n\t        var item = {\n\t            path: path,\n\t            type: type,\n\t            actual: actual,\n\t            expected: expected,\n\t            action: 'is greater than',\n\t            message: message\n\t        }\n\t        item.message = Assert.message(item)\n\t        result.push(item)\n\t        return false\n\t    },\n\t    lessThan: function(type, path, actual, expected, result, message) {\n\t        if (actual < expected) return true\n\t        var item = {\n\t            path: path,\n\t            type: type,\n\t            actual: actual,\n\t            expected: expected,\n\t            action: 'is less to',\n\t            message: message\n\t        }\n\t        item.message = Assert.message(item)\n\t        result.push(item)\n\t        return false\n\t    },\n\t    greaterThanOrEqualTo: function(type, path, actual, expected, result, message) {\n\t        if (actual >= expected) return true\n\t        var item = {\n\t            path: path,\n\t            type: type,\n\t            actual: actual,\n\t            expected: expected,\n\t            action: 'is greater than or equal to',\n\t            message: message\n\t        }\n\t        item.message = Assert.message(item)\n\t        result.push(item)\n\t        return false\n\t    },\n\t    lessThanOrEqualTo: function(type, path, actual, expected, result, message) {\n\t        if (actual <= expected) return true\n\t        var item = {\n\t            path: path,\n\t            type: type,\n\t            actual: actual,\n\t            expected: expected,\n\t            action: 'is less than or equal to',\n\t            message: message\n\t        }\n\t        item.message = Assert.message(item)\n\t        result.push(item)\n\t        return false\n\t    }\n\t}\n\n\tvalid.Diff = Diff\n\tvalid.Assert = Assert\n\n\tmodule.exports = valid\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\tmodule.exports = __webpack_require__(28)\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global window, document, location, Event, setTimeout */\n\t/*\n\t    ## MockXMLHttpRequest\n\n\t    期望的功能：\n\t    1. 完整地覆盖原生 XHR 的行为\n\t    2. 完整地模拟原生 XHR 的行为\n\t    3. 在发起请求时，自动检测是否需要拦截\n\t    4. 如果不必拦截，则执行原生 XHR 的行为\n\t    5. 如果需要拦截，则执行虚拟 XHR 的行为\n\t    6. 兼容 XMLHttpRequest 和 ActiveXObject\n\t        new window.XMLHttpRequest()\n\t        new window.ActiveXObject(\"Microsoft.XMLHTTP\")\n\n\t    关键方法的逻辑：\n\t    * new   此时尚无法确定是否需要拦截，所以创建原生 XHR 对象是必须的。\n\t    * open  此时可以取到 URL，可以决定是否进行拦截。\n\t    * send  此时已经确定了请求方式。\n\n\t    规范：\n\t    http://xhr.spec.whatwg.org/\n\t    http://www.w3.org/TR/XMLHttpRequest2/\n\n\t    参考实现：\n\t    https://github.com/philikon/MockHttpRequest/blob/master/lib/mock.js\n\t    https://github.com/trek/FakeXMLHttpRequest/blob/master/fake_xml_http_request.js\n\t    https://github.com/ilinsky/xmlhttprequest/blob/master/XMLHttpRequest.js\n\t    https://github.com/firebug/firebug-lite/blob/master/content/lite/xhr.js\n\t    https://github.com/thx/RAP/blob/master/lab/rap.plugin.xinglie.js\n\n\t    **需不需要全面重写 XMLHttpRequest？**\n\t        http://xhr.spec.whatwg.org/#interface-xmlhttprequest\n\t        关键属性 readyState、status、statusText、response、responseText、responseXML 是 readonly，所以，试图通过修改这些状态，来模拟响应是不可行的。\n\t        因此，唯一的办法是模拟整个 XMLHttpRequest，就像 jQuery 对事件模型的封装。\n\n\t    // Event handlers\n\t    onloadstart         loadstart\n\t    onprogress          progress\n\t    onabort             abort\n\t    onerror             error\n\t    onload              load\n\t    ontimeout           timeout\n\t    onloadend           loadend\n\t    onreadystatechange  readystatechange\n\t */\n\n\tvar Util = __webpack_require__(3)\n\n\t// 备份原生 XMLHttpRequest\n\twindow._XMLHttpRequest = window.XMLHttpRequest\n\twindow._ActiveXObject = window.ActiveXObject\n\n\t/*\n\t    PhantomJS\n\t    TypeError: '[object EventConstructor]' is not a constructor (evaluating 'new Event(\"readystatechange\")')\n\n\t    https://github.com/bluerail/twitter-bootstrap-rails-confirm/issues/18\n\t    https://github.com/ariya/phantomjs/issues/11289\n\t*/\n\ttry {\n\t    new window.Event('custom')\n\t} catch (exception) {\n\t    window.Event = function(type, bubbles, cancelable, detail) {\n\t        var event = document.createEvent('CustomEvent') // MUST be 'CustomEvent'\n\t        event.initCustomEvent(type, bubbles, cancelable, detail)\n\t        return event\n\t    }\n\t}\n\n\tvar XHR_STATES = {\n\t    // The object has been constructed.\n\t    UNSENT: 0,\n\t    // The open() method has been successfully invoked.\n\t    OPENED: 1,\n\t    // All redirects (if any) have been followed and all HTTP headers of the response have been received.\n\t    HEADERS_RECEIVED: 2,\n\t    // The response's body is being received.\n\t    LOADING: 3,\n\t    // The data transfer has been completed or something went wrong during the transfer (e.g. infinite redirects).\n\t    DONE: 4\n\t}\n\n\tvar XHR_EVENTS = 'readystatechange loadstart progress abort error load timeout loadend'.split(' ')\n\tvar XHR_REQUEST_PROPERTIES = 'timeout withCredentials'.split(' ')\n\tvar XHR_RESPONSE_PROPERTIES = 'readyState responseURL status statusText responseType response responseText responseXML'.split(' ')\n\n\t// https://github.com/trek/FakeXMLHttpRequest/blob/master/fake_xml_http_request.js#L32\n\tvar HTTP_STATUS_CODES = {\n\t    100: \"Continue\",\n\t    101: \"Switching Protocols\",\n\t    200: \"OK\",\n\t    201: \"Created\",\n\t    202: \"Accepted\",\n\t    203: \"Non-Authoritative Information\",\n\t    204: \"No Content\",\n\t    205: \"Reset Content\",\n\t    206: \"Partial Content\",\n\t    300: \"Multiple Choice\",\n\t    301: \"Moved Permanently\",\n\t    302: \"Found\",\n\t    303: \"See Other\",\n\t    304: \"Not Modified\",\n\t    305: \"Use Proxy\",\n\t    307: \"Temporary Redirect\",\n\t    400: \"Bad Request\",\n\t    401: \"Unauthorized\",\n\t    402: \"Payment Required\",\n\t    403: \"Forbidden\",\n\t    404: \"Not Found\",\n\t    405: \"Method Not Allowed\",\n\t    406: \"Not Acceptable\",\n\t    407: \"Proxy Authentication Required\",\n\t    408: \"Request Timeout\",\n\t    409: \"Conflict\",\n\t    410: \"Gone\",\n\t    411: \"Length Required\",\n\t    412: \"Precondition Failed\",\n\t    413: \"Request Entity Too Large\",\n\t    414: \"Request-URI Too Long\",\n\t    415: \"Unsupported Media Type\",\n\t    416: \"Requested Range Not Satisfiable\",\n\t    417: \"Expectation Failed\",\n\t    422: \"Unprocessable Entity\",\n\t    500: \"Internal Server Error\",\n\t    501: \"Not Implemented\",\n\t    502: \"Bad Gateway\",\n\t    503: \"Service Unavailable\",\n\t    504: \"Gateway Timeout\",\n\t    505: \"HTTP Version Not Supported\"\n\t}\n\n\t/*\n\t    MockXMLHttpRequest\n\t*/\n\n\tfunction MockXMLHttpRequest() {\n\t    // 初始化 custom 对象，用于存储自定义属性\n\t    this.custom = {\n\t        events: {},\n\t        requestHeaders: {},\n\t        responseHeaders: {}\n\t    }\n\t}\n\n\tMockXMLHttpRequest._settings = {\n\t    timeout: '10-100',\n\t    /*\n\t        timeout: 50,\n\t        timeout: '10-100',\n\t     */\n\t}\n\n\tMockXMLHttpRequest.setup = function(settings) {\n\t    Util.extend(MockXMLHttpRequest._settings, settings)\n\t    return MockXMLHttpRequest._settings\n\t}\n\n\tUtil.extend(MockXMLHttpRequest, XHR_STATES)\n\tUtil.extend(MockXMLHttpRequest.prototype, XHR_STATES)\n\n\t// 标记当前对象为 MockXMLHttpRequest\n\tMockXMLHttpRequest.prototype.mock = true\n\n\t// 是否拦截 Ajax 请求\n\tMockXMLHttpRequest.prototype.match = false\n\n\t// 初始化 Request 相关的属性和方法\n\tUtil.extend(MockXMLHttpRequest.prototype, {\n\t    // https://xhr.spec.whatwg.org/#the-open()-method\n\t    // Sets the request method, request URL, and synchronous flag.\n\t    open: function(method, url, async, username, password) {\n\t        var that = this\n\n\t        Util.extend(this.custom, {\n\t            method: method,\n\t            url: url,\n\t            async: typeof async === 'boolean' ? async : true,\n\t            username: username,\n\t            password: password,\n\t            options: {\n\t                url: url,\n\t                type: method\n\t            }\n\t        })\n\n\t        this.custom.timeout = function(timeout) {\n\t            if (typeof timeout === 'number') return timeout\n\t            if (typeof timeout === 'string' && !~timeout.indexOf('-')) return parseInt(timeout, 10)\n\t            if (typeof timeout === 'string' && ~timeout.indexOf('-')) {\n\t                var tmp = timeout.split('-')\n\t                var min = parseInt(tmp[0], 10)\n\t                var max = parseInt(tmp[1], 10)\n\t                return Math.round(Math.random() * (max - min)) + min\n\t            }\n\t        }(MockXMLHttpRequest._settings.timeout)\n\n\t        // 查找与请求参数匹配的数据模板\n\t        var item = find(this.custom.options)\n\n\t        function handle(event) {\n\t            // 同步属性 NativeXMLHttpRequest => MockXMLHttpRequest\n\t            for (var i = 0; i < XHR_RESPONSE_PROPERTIES.length; i++) {\n\t                try {\n\t                    that[XHR_RESPONSE_PROPERTIES[i]] = xhr[XHR_RESPONSE_PROPERTIES[i]]\n\t                } catch (e) {}\n\t            }\n\t            // 触发 MockXMLHttpRequest 上的同名事件\n\t            that.dispatchEvent(new Event(event.type /*, false, false, that*/ ))\n\t        }\n\n\t        // 如果未找到匹配的数据模板，则采用原生 XHR 发送请求。\n\t        if (!item) {\n\t            // 创建原生 XHR 对象，调用原生 open()，监听所有原生事件\n\t            var xhr = createNativeXMLHttpRequest()\n\t            this.custom.xhr = xhr\n\n\t            // 初始化所有事件，用于监听原生 XHR 对象的事件\n\t            for (var i = 0; i < XHR_EVENTS.length; i++) {\n\t                xhr.addEventListener(XHR_EVENTS[i], handle)\n\t            }\n\n\t            // xhr.open()\n\t            if (username) xhr.open(method, url, async, username, password)\n\t            else xhr.open(method, url, async)\n\n\t            // 同步属性 MockXMLHttpRequest => NativeXMLHttpRequest\n\t            for (var j = 0; j < XHR_REQUEST_PROPERTIES.length; j++) {\n\t                try {\n\t                    xhr[XHR_REQUEST_PROPERTIES[j]] = that[XHR_REQUEST_PROPERTIES[j]]\n\t                } catch (e) {}\n\t            }\n\n\t            return\n\t        }\n\n\t        // 找到了匹配的数据模板，开始拦截 XHR 请求\n\t        this.match = true\n\t        this.custom.template = item\n\t        this.readyState = MockXMLHttpRequest.OPENED\n\t        this.dispatchEvent(new Event('readystatechange' /*, false, false, this*/ ))\n\t    },\n\t    // https://xhr.spec.whatwg.org/#the-setrequestheader()-method\n\t    // Combines a header in author request headers.\n\t    setRequestHeader: function(name, value) {\n\t        // 原生 XHR\n\t        if (!this.match) {\n\t            this.custom.xhr.setRequestHeader(name, value)\n\t            return\n\t        }\n\n\t        // 拦截 XHR\n\t        var requestHeaders = this.custom.requestHeaders\n\t        if (requestHeaders[name]) requestHeaders[name] += ',' + value\n\t        else requestHeaders[name] = value\n\t    },\n\t    timeout: 0,\n\t    withCredentials: false,\n\t    upload: {},\n\t    // https://xhr.spec.whatwg.org/#the-send()-method\n\t    // Initiates the request.\n\t    send: function send(data) {\n\t        var that = this\n\t        this.custom.options.body = data\n\n\t        // 原生 XHR\n\t        if (!this.match) {\n\t            this.custom.xhr.send(data)\n\t            return\n\t        }\n\n\t        // 拦截 XHR\n\n\t        // X-Requested-With header\n\t        this.setRequestHeader('X-Requested-With', 'MockXMLHttpRequest')\n\n\t        // loadstart The fetch initiates.\n\t        this.dispatchEvent(new Event('loadstart' /*, false, false, this*/ ))\n\n\t        if (this.custom.async) setTimeout(done, this.custom.timeout) // 异步\n\t        else done() // 同步\n\n\t        function done() {\n\t            that.readyState = MockXMLHttpRequest.HEADERS_RECEIVED\n\t            that.dispatchEvent(new Event('readystatechange' /*, false, false, that*/ ))\n\t            that.readyState = MockXMLHttpRequest.LOADING\n\t            that.dispatchEvent(new Event('readystatechange' /*, false, false, that*/ ))\n\n\t            that.status = 200\n\t            that.statusText = HTTP_STATUS_CODES[200]\n\n\t            // fix #92 #93 by @qddegtya\n\t            that.response = that.responseText = JSON.stringify(\n\t                convert(that.custom.template, that.custom.options),\n\t                null, 4\n\t            )\n\n\t            that.readyState = MockXMLHttpRequest.DONE\n\t            that.dispatchEvent(new Event('readystatechange' /*, false, false, that*/ ))\n\t            that.dispatchEvent(new Event('load' /*, false, false, that*/ ));\n\t            that.dispatchEvent(new Event('loadend' /*, false, false, that*/ ));\n\t        }\n\t    },\n\t    // https://xhr.spec.whatwg.org/#the-abort()-method\n\t    // Cancels any network activity.\n\t    abort: function abort() {\n\t        // 原生 XHR\n\t        if (!this.match) {\n\t            this.custom.xhr.abort()\n\t            return\n\t        }\n\n\t        // 拦截 XHR\n\t        this.readyState = MockXMLHttpRequest.UNSENT\n\t        this.dispatchEvent(new Event('abort', false, false, this))\n\t        this.dispatchEvent(new Event('error', false, false, this))\n\t    }\n\t})\n\n\t// 初始化 Response 相关的属性和方法\n\tUtil.extend(MockXMLHttpRequest.prototype, {\n\t    responseURL: '',\n\t    status: MockXMLHttpRequest.UNSENT,\n\t    statusText: '',\n\t    // https://xhr.spec.whatwg.org/#the-getresponseheader()-method\n\t    getResponseHeader: function(name) {\n\t        // 原生 XHR\n\t        if (!this.match) {\n\t            return this.custom.xhr.getResponseHeader(name)\n\t        }\n\n\t        // 拦截 XHR\n\t        return this.custom.responseHeaders[name.toLowerCase()]\n\t    },\n\t    // https://xhr.spec.whatwg.org/#the-getallresponseheaders()-method\n\t    // http://www.utf8-chartable.de/\n\t    getAllResponseHeaders: function() {\n\t        // 原生 XHR\n\t        if (!this.match) {\n\t            return this.custom.xhr.getAllResponseHeaders()\n\t        }\n\n\t        // 拦截 XHR\n\t        var responseHeaders = this.custom.responseHeaders\n\t        var headers = ''\n\t        for (var h in responseHeaders) {\n\t            if (!responseHeaders.hasOwnProperty(h)) continue\n\t            headers += h + ': ' + responseHeaders[h] + '\\r\\n'\n\t        }\n\t        return headers\n\t    },\n\t    overrideMimeType: function( /*mime*/ ) {},\n\t    responseType: '', // '', 'text', 'arraybuffer', 'blob', 'document', 'json'\n\t    response: null,\n\t    responseText: '',\n\t    responseXML: null\n\t})\n\n\t// EventTarget\n\tUtil.extend(MockXMLHttpRequest.prototype, {\n\t    addEventListener: function addEventListener(type, handle) {\n\t        var events = this.custom.events\n\t        if (!events[type]) events[type] = []\n\t        events[type].push(handle)\n\t    },\n\t    removeEventListener: function removeEventListener(type, handle) {\n\t        var handles = this.custom.events[type] || []\n\t        for (var i = 0; i < handles.length; i++) {\n\t            if (handles[i] === handle) {\n\t                handles.splice(i--, 1)\n\t            }\n\t        }\n\t    },\n\t    dispatchEvent: function dispatchEvent(event) {\n\t        var handles = this.custom.events[event.type] || []\n\t        for (var i = 0; i < handles.length; i++) {\n\t            handles[i].call(this, event)\n\t        }\n\n\t        var ontype = 'on' + event.type\n\t        if (this[ontype]) this[ontype](event)\n\t    }\n\t})\n\n\t// Inspired by jQuery\n\tfunction createNativeXMLHttpRequest() {\n\t    var isLocal = function() {\n\t        var rlocalProtocol = /^(?:about|app|app-storage|.+-extension|file|res|widget):$/\n\t        var rurl = /^([\\w.+-]+:)(?:\\/\\/([^\\/?#:]*)(?::(\\d+)|)|)/\n\t        var ajaxLocation = location.href\n\t        var ajaxLocParts = rurl.exec(ajaxLocation.toLowerCase()) || []\n\t        return rlocalProtocol.test(ajaxLocParts[1])\n\t    }()\n\n\t    return window.ActiveXObject ?\n\t        (!isLocal && createStandardXHR() || createActiveXHR()) : createStandardXHR()\n\n\t    function createStandardXHR() {\n\t        try {\n\t            return new window._XMLHttpRequest();\n\t        } catch (e) {}\n\t    }\n\n\t    function createActiveXHR() {\n\t        try {\n\t            return new window._ActiveXObject(\"Microsoft.XMLHTTP\");\n\t        } catch (e) {}\n\t    }\n\t}\n\n\n\t// 查找与请求参数匹配的数据模板：URL，Type\n\tfunction find(options) {\n\n\t    for (var sUrlType in MockXMLHttpRequest.Mock._mocked) {\n\t        var item = MockXMLHttpRequest.Mock._mocked[sUrlType]\n\t        if (\n\t            (!item.rurl || match(item.rurl, options.url)) &&\n\t            (!item.rtype || match(item.rtype, options.type.toLowerCase()))\n\t        ) {\n\t            // console.log('[mock]', options.url, '>', item.rurl)\n\t            return item\n\t        }\n\t    }\n\n\t    function match(expected, actual) {\n\t        if (Util.type(expected) === 'string') {\n\t            return expected === actual\n\t        }\n\t        if (Util.type(expected) === 'regexp') {\n\t            return expected.test(actual)\n\t        }\n\t    }\n\n\t}\n\n\t// 数据模板 ＝> 响应数据\n\tfunction convert(item, options) {\n\t    return Util.isFunction(item.template) ?\n\t        item.template(options) : MockXMLHttpRequest.Mock.mock(item.template)\n\t}\n\n\tmodule.exports = MockXMLHttpRequest\n\n/***/ })\n/******/ ])\n});\n;"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,iCAAiC,MAAM,SAAS;AACzD,UAAG,OAAO,YAAY,YAAY,OAAO,WAAW;AACnD,eAAO,UAAU,QAAQ;AAAA,eAClB,OAAO,WAAW,cAAc,OAAO;AAC9C,eAAO,CAAC,GAAG,OAAO;AAAA,eACX,OAAO,YAAY;AAC1B,gBAAQ,MAAM,IAAI,QAAQ;AAAA;AAE1B,aAAK,MAAM,IAAI,QAAQ;AAAA,IACzB,GAAG,SAAM,WAAW;AACpB;AAAA;AAAA,QAAiB,SAAS,SAAS;AAEzB,cAAI,mBAAmB,CAAC;AAGxB,mBAASA,qBAAoB,UAAU;AAGtC,gBAAG,iBAAiB,QAAQ;AAC3B,qBAAO,iBAAiB,QAAQ,EAAE;AAGnC,gBAAIC,UAAS,iBAAiB,QAAQ,IAAI;AAAA;AAAA,cACzC,SAAS,CAAC;AAAA;AAAA,cACV,IAAI;AAAA;AAAA,cACJ,QAAQ;AAAA;AAAA,YACT;AAGA,oBAAQ,QAAQ,EAAE,KAAKA,QAAO,SAASA,SAAQA,QAAO,SAASD,oBAAmB;AAGlF,YAAAC,QAAO,SAAS;AAGhB,mBAAOA,QAAO;AAAA,UACf;AAIA,UAAAD,qBAAoB,IAAI;AAGxB,UAAAA,qBAAoB,IAAI;AAGxB,UAAAA,qBAAoB,IAAI;AAGxB,iBAAOA,qBAAoB,CAAC;AAAA,QAC7B,EAEC;AAAA;AAAA;AAAA,UAEH,SAASC,SAAQC,UAASF,sBAAqB;AAGrD,gBAAIG,WAAUH,qBAAoB,CAAC;AACnC,gBAAII,QAAOJ,qBAAoB,CAAC;AAChC,gBAAIK,UAASL,qBAAoB,CAAC;AAClC,gBAAIM,MAAKN,qBAAoB,EAAE;AAC/B,gBAAI,eAAeA,qBAAoB,EAAE;AACzC,gBAAI,QAAQA,qBAAoB,EAAE;AAElC,gBAAI;AACJ,gBAAI,OAAO,WAAW,YAAa,OAAMA,qBAAoB,EAAE;AAO/D,gBAAI,OAAO;AAAA,cACP,SAASG;AAAA,cACT,QAAQE;AAAA,cACR,MAAMD;AAAA,cACN;AAAA,cACA,IAAIE;AAAA,cACJ;AAAA,cACA;AAAA,cACA,SAASF,MAAK;AAAA,cACd,OAAO,SAAS,UAAU;AACtB,uBAAO,IAAI,MAAM,QAAQ;AAAA,cAC7B;AAAA,cACA,SAAS,CAAC;AAAA,YACd;AAEA,iBAAK,UAAU;AAGf,gBAAI,IAAK,KAAI,OAAO;AAYpB,iBAAK,OAAO,SAAS,MAAM,OAAO,UAAU;AAExC,kBAAI,UAAU,WAAW,GAAG;AACxB,uBAAOD,SAAQ,IAAI,IAAI;AAAA,cAC3B;AAEA,kBAAI,UAAU,WAAW,GAAG;AACxB,2BAAW;AACX,wBAAQ;AAAA,cACZ;AAEA,kBAAI,IAAK,QAAO,iBAAiB;AACjC,mBAAK,QAAQ,QAAQ,SAAS,GAAG,IAAI;AAAA,gBACjC;AAAA,gBACA;AAAA,gBACA;AAAA,cACJ;AACA,qBAAO;AAAA,YACX;AAEA,YAAAF,QAAO,UAAU;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAAS,QAAQ,SAAS,qBAAqB;AAmCrD,gBAAI,WAAW,oBAAoB,CAAC;AACpC,gBAAI,OAAO,oBAAoB,CAAC;AAChC,gBAAI,SAAS,oBAAoB,CAAC;AAClC,gBAAI,SAAS,oBAAoB,CAAC;AAClC,gBAAI,KAAK,oBAAoB,EAAE;AAE/B,gBAAI,UAAU;AAAA,cACV,QAAQ,KAAK;AAAA,YACjB;AAcA,oBAAQ,MAAM,SAAS,UAAU,MAAM,SAAS;AAE5C,qBAAO,QAAQ,SAAY,KAAM,OAAO;AAExC,wBAAU,WAAW,CAAC;AACtB,wBAAU;AAAA;AAAA,gBAEF,MAAM,QAAQ,QAAQ,CAAC,SAAS,IAAI;AAAA,gBACpC,cAAc,QAAQ,gBAAgB,CAAC,SAAS,MAAM;AAAA;AAAA,gBAEtD,gBAAgB,QAAQ;AAAA;AAAA,gBAExB,wBAAwB,QAAQ,0BAA0B;AAAA;AAAA,gBAE1D,MAAM,QAAQ,QAAQ,QAAQ;AAAA;AAAA,gBAE9B,cAAc,QAAQ,gBAAgB,QAAQ,0BAA0B;AAAA,cAC5E;AAGJ,kBAAI,OAAO,OAAO,MAAM,IAAI;AAC5B,kBAAI,OAAO,KAAK,KAAK,QAAQ;AAC7B,kBAAI;AAEJ,kBAAI,QAAQ,IAAI,GAAG;AACf,uBAAO,QAAQ,IAAI,EAAE;AAAA;AAAA,kBAEjB;AAAA;AAAA,kBAEA;AAAA;AAAA,kBAEA;AAAA;AAAA,kBAEA,YAAY,OAAO,KAAK,QAAQ,SAAS,QAAQ,IAAI,IAAI;AAAA;AAAA,kBAGzD;AAAA;AAAA,kBAEA;AAAA,gBACJ,CAAC;AAED,oBAAI,CAAC,QAAQ,KAAM,SAAQ,OAAO;AAClC,uBAAO;AAAA,cACX;AAEA,qBAAO;AAAA,YACX;AAEA,oBAAQ,OAAO;AAAA,cACX,OAAO,SAASM,UAAS;AACrB,oBAAI,SAAS,CAAC,GACVC,IAAG;AAKP,oBAAID,SAAQ,SAAS,WAAW,EAAG,QAAO;AAG1C,oBAAI,CAACA,SAAQ,KAAK,YAAY;AAC1B,uBAAKC,KAAI,GAAGA,KAAID,SAAQ,SAAS,QAAQC,MAAK;AAC1C,oBAAAD,SAAQ,QAAQ,KAAK,KAAKC,EAAC;AAC3B,oBAAAD,SAAQ,QAAQ,aAAa,KAAKC,EAAC;AACnC,2BAAO;AAAA,sBACH,QAAQ,IAAID,SAAQ,SAASC,EAAC,GAAGA,IAAG;AAAA,wBAChC,MAAMD,SAAQ,QAAQ;AAAA,wBACtB,cAAcA,SAAQ,QAAQ;AAAA,wBAC9B,gBAAgB;AAAA,wBAChB,wBAAwBA,SAAQ;AAAA,wBAChC,MAAMA,SAAQ,QAAQ,QAAQ;AAAA,wBAC9B,cAAcA,SAAQ,QAAQ,gBAAgBA,SAAQ;AAAA,sBAC1D,CAAC;AAAA,oBACL;AACA,oBAAAA,SAAQ,QAAQ,KAAK,IAAI;AACzB,oBAAAA,SAAQ,QAAQ,aAAa,IAAI;AAAA,kBACrC;AAAA,gBACJ,OAAO;AAEH,sBAAIA,SAAQ,KAAK,QAAQ,KAAKA,SAAQ,KAAK,QAAQ,QAAW;AAE1D,oBAAAA,SAAQ,QAAQ,KAAK,KAAKA,SAAQ,IAAI;AACtC,oBAAAA,SAAQ,QAAQ,aAAa,KAAKA,SAAQ,IAAI;AAC9C,6BAAS,OAAO;AAAA,sBACZ,QAAQ,IAAIA,SAAQ,UAAU,QAAW;AAAA,wBACrC,MAAMA,SAAQ,QAAQ;AAAA,wBACtB,cAAcA,SAAQ,QAAQ;AAAA,wBAC9B,gBAAgB;AAAA,wBAChB,wBAAwBA,SAAQ;AAAA,wBAChC,MAAMA,SAAQ,QAAQ,QAAQ;AAAA,wBAC9B,cAAcA,SAAQ,QAAQ,gBAAgBA,SAAQ;AAAA,sBAC1D,CAAC;AAAA,oBACL;AACA,oBAAAA,SAAQ,QAAQ,KAAK,IAAI;AACzB,oBAAAA,SAAQ,QAAQ,aAAa,IAAI;AAAA,kBACrC,OAAO;AAEH,wBAAIA,SAAQ,KAAK,WAAW,CAAC,GAAG;AAC5B,sBAAAA,SAAQ,SAAS,gBAAgBA,SAAQ,SAAS,iBAAiB;AAEnE,sBAAAA,SAAQ,QAAQ,KAAK,KAAKA,SAAQ,IAAI;AACtC,sBAAAA,SAAQ,QAAQ,aAAa,KAAKA,SAAQ,IAAI;AAC9C,+BAAS,QAAQ,IAAIA,SAAQ,UAAU,QAAW;AAAA,wBAC9C,MAAMA,SAAQ,QAAQ;AAAA,wBACtB,cAAcA,SAAQ,QAAQ;AAAA,wBAC9B,gBAAgB;AAAA,wBAChB,wBAAwBA,SAAQ;AAAA,wBAChC,MAAMA,SAAQ,QAAQ,QAAQ;AAAA,wBAC9B,cAAcA,SAAQ,QAAQ,gBAAgBA,SAAQ;AAAA,sBAC1D,CAAC,EACGA,SAAQ,SAAS,gBAAgBA,SAAQ,SAAS,MACtD;AAEA,sBAAAA,SAAQ,SAAS,iBAAiB,CAACA,SAAQ,KAAK,WAAW,CAAC;AAE5D,sBAAAA,SAAQ,QAAQ,KAAK,IAAI;AACzB,sBAAAA,SAAQ,QAAQ,aAAa,IAAI;AAAA,oBAErC,OAAO;AAEH,2BAAKC,KAAI,GAAGA,KAAID,SAAQ,KAAK,OAAOC,MAAK;AAErC,6BAAK,KAAK,GAAG,KAAKD,SAAQ,SAAS,QAAQ,MAAM;AAC7C,0BAAAA,SAAQ,QAAQ,KAAK,KAAK,OAAO,MAAM;AACvC,0BAAAA,SAAQ,QAAQ,aAAa,KAAK,EAAE;AACpC,iCAAO;AAAA,4BACH,QAAQ,IAAIA,SAAQ,SAAS,EAAE,GAAG,OAAO,QAAQ;AAAA,8BAC7C,MAAMA,SAAQ,QAAQ;AAAA,8BACtB,cAAcA,SAAQ,QAAQ;AAAA,8BAC9B,gBAAgB;AAAA,8BAChB,wBAAwBA,SAAQ;AAAA,8BAChC,MAAMA,SAAQ,QAAQ,QAAQ;AAAA,8BAC9B,cAAcA,SAAQ,QAAQ,gBAAgBA,SAAQ;AAAA,4BAC1D,CAAC;AAAA,0BACL;AACA,0BAAAA,SAAQ,QAAQ,KAAK,IAAI;AACzB,0BAAAA,SAAQ,QAAQ,aAAa,IAAI;AAAA,wBACrC;AAAA,sBACJ;AAAA,oBACJ;AAAA,kBACJ;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAAA,cACA,QAAQ,SAASA,UAAS;AACtB,oBAAI,SAAS,CAAC,GACV,MAAM,QAAQE,MAAK,WAAW,KAAKD;AAIvC,oBAAID,SAAQ,KAAK,OAAO,QAAW;AAC/B,yBAAO,KAAK,KAAKA,SAAQ,QAAQ;AACjC,yBAAO,OAAO,QAAQ,IAAI;AAC1B,yBAAO,KAAK,MAAM,GAAGA,SAAQ,KAAK,KAAK;AACvC,uBAAKC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AAC9B,oBAAAC,OAAM,KAAKD,EAAC;AACZ,gCAAYC,KAAI,QAAQ,SAAS,QAAQ,IAAI;AAC7C,oBAAAF,SAAQ,QAAQ,KAAK,KAAK,SAAS;AACnC,oBAAAA,SAAQ,QAAQ,aAAa,KAAKE,IAAG;AACrC,2BAAO,SAAS,IAAI,QAAQ,IAAIF,SAAQ,SAASE,IAAG,GAAGA,MAAK;AAAA,sBACxD,MAAMF,SAAQ,QAAQ;AAAA,sBACtB,cAAcA,SAAQ,QAAQ;AAAA,sBAC9B,gBAAgB;AAAA,sBAChB,wBAAwBA,SAAQ;AAAA,sBAChC,MAAMA,SAAQ,QAAQ,QAAQ;AAAA,sBAC9B,cAAcA,SAAQ,QAAQ,gBAAgBA,SAAQ;AAAA,oBAC1D,CAAC;AACD,oBAAAA,SAAQ,QAAQ,KAAK,IAAI;AACzB,oBAAAA,SAAQ,QAAQ,aAAa,IAAI;AAAA,kBACrC;AAAA,gBAEJ,OAAO;AAEH,yBAAO,CAAC;AACR,2BAAS,CAAC;AACV,uBAAKE,QAAOF,SAAQ,UAAU;AAC1B,qBAAC,OAAOA,SAAQ,SAASE,IAAG,MAAM,aAAa,SAAS,MAAM,KAAKA,IAAG;AAAA,kBAC1E;AACA,yBAAO,KAAK,OAAO,MAAM;AAczB,uBAAKD,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AAC9B,oBAAAC,OAAM,KAAKD,EAAC;AACZ,gCAAYC,KAAI,QAAQ,SAAS,QAAQ,IAAI;AAC7C,oBAAAF,SAAQ,QAAQ,KAAK,KAAK,SAAS;AACnC,oBAAAA,SAAQ,QAAQ,aAAa,KAAKE,IAAG;AACrC,2BAAO,SAAS,IAAI,QAAQ,IAAIF,SAAQ,SAASE,IAAG,GAAGA,MAAK;AAAA,sBACxD,MAAMF,SAAQ,QAAQ;AAAA,sBACtB,cAAcA,SAAQ,QAAQ;AAAA,sBAC9B,gBAAgB;AAAA,sBAChB,wBAAwBA,SAAQ;AAAA,sBAChC,MAAMA,SAAQ,QAAQ,QAAQ;AAAA,sBAC9B,cAAcA,SAAQ,QAAQ,gBAAgBA,SAAQ;AAAA,oBAC1D,CAAC;AACD,oBAAAA,SAAQ,QAAQ,KAAK,IAAI;AACzB,oBAAAA,SAAQ,QAAQ,aAAa,IAAI;AAEjC,0BAAME,KAAI,MAAM,SAAS,MAAM;AAC/B,wBAAI,OAAO,IAAI,CAAC,KAAK,KAAK,KAAKF,SAAQ,SAASE,IAAG,CAAC,MAAM,UAAU;AAChE,sBAAAF,SAAQ,SAASE,IAAG,KAAK,SAAS,IAAI,CAAC,GAAG,EAAE;AAAA,oBAChD;AAAA,kBACJ;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAAA,cACA,QAAQ,SAASF,UAAS;AACtB,oBAAI,QAAQG;AACZ,oBAAIH,SAAQ,KAAK,SAAS;AACtB,kBAAAA,SAAQ,YAAY;AACpB,kBAAAG,SAAQH,SAAQ,SAAS,MAAM,GAAG;AAKlC,kBAAAG,OAAM,CAAC,IAAIH,SAAQ,KAAK,QAAQA,SAAQ,KAAK,QAAQG,OAAM,CAAC;AAC5D,kBAAAA,OAAM,CAAC,KAAKA,OAAM,CAAC,KAAK,IAAI,MAAM,GAAGH,SAAQ,KAAK,MAAM;AACxD,yBAAOG,OAAM,CAAC,EAAE,SAASH,SAAQ,KAAK,QAAQ;AAC1C,oBAAAG,OAAM,CAAC;AAAA,oBAEFA,OAAM,CAAC,EAAE,SAASH,SAAQ,KAAK,SAAS,IAAK,OAAO,UAAU,QAAQ,IAAI,OAAO,UAAU,WAAW;AAAA,kBAE/G;AACA,2BAAS,WAAWG,OAAM,KAAK,GAAG,GAAG,EAAE;AAAA,gBAC3C,OAAO;AAEH,2BAASH,SAAQ,KAAK,SAAS,CAACA,SAAQ,KAAK,WAAW,CAAC,IAAIA,SAAQ,KAAK,QAAQA,SAAQ;AAAA,gBAC9F;AACA,uBAAO;AAAA,cACX;AAAA,cACA,SAAS,SAASA,UAAS;AACvB,oBAAI;AAGJ,yBAASA,SAAQ,KAAK,aAAa,OAAO,KAAKA,SAAQ,KAAK,KAAKA,SAAQ,KAAK,KAAKA,SAAQ,QAAQ,IAAIA,SAAQ;AAC/G,uBAAO;AAAA,cACX;AAAA,cACA,QAAQ,SAASA,UAAS;AACtB,oBAAI,SAAS,IACTC,IAAG,cAAc,IAAI;AACzB,oBAAID,SAAQ,SAAS,QAAQ;AAIzB,sBAAIA,SAAQ,KAAK,SAAS,QAAW;AACjC,8BAAUA,SAAQ;AAAA,kBACtB;AAGA,uBAAKC,KAAI,GAAGA,KAAID,SAAQ,KAAK,OAAOC,MAAK;AACrC,8BAAUD,SAAQ;AAAA,kBACtB;AAEA,iCAAe,OAAO,MAAM,SAAS,cAAc,KAAK,CAAC;AACzD,uBAAKC,KAAI,GAAGA,KAAI,aAAa,QAAQA,MAAK;AACtC,yBAAK,aAAaA,EAAC;AAGnB,wBAAI,MAAM,KAAK,EAAE,GAAG;AAChB,mCAAa,OAAOA,MAAK,CAAC;AAC1B;AAAA,oBACJ;AAEA,2BAAO,QAAQ,YAAY,IAAID,SAAQ,QAAQ,gBAAgBA,SAAQ,QAAQ,wBAAwBA,QAAO;AAG9G,wBAAI,aAAa,WAAW,KAAK,OAAO,UAAU,OAAO,SAAS,OAAO,QAAQ;AAC7E,+BAAS;AACT;AAEA,0BAAI,KAAK,UAAU,IAAI,GAAG;AACtB,iCAAS,WAAW,MAAM,EAAE;AAC5B;AAAA,sBACJ;AACA,0BAAI,iBAAiB,KAAK,IAAI,GAAG;AAC7B,iCAAS,SAAS,SAAS,OACvB,SAAS,UAAU,QACnB;AACJ;AAAA,sBACJ;AAAA,oBACJ;AACA,6BAAS,OAAO,QAAQ,IAAI,IAAI;AAAA,kBACpC;AAAA,gBAEJ,OAAO;AAGH,2BAASA,SAAQ,KAAK,QAAQ,OAAO,OAAOA,SAAQ,KAAK,KAAK,IAAIA,SAAQ;AAAA,gBAC9E;AACA,uBAAO;AAAA,cACX;AAAA,cACA,YAAY,SAASA,UAAS;AAE1B,uBAAOA,SAAQ,SAAS,KAAKA,SAAQ,QAAQ,gBAAgBA,QAAO;AAAA,cACxE;AAAA,cACA,UAAU,SAASA,UAAS;AACxB,oBAAI,SAAS;AAIb,oBAAIA,SAAQ,KAAK,SAAS,QAAW;AACjC,4BAAUA,SAAQ,SAAS;AAAA,gBAC/B;AAGA,yBAASC,KAAI,GAAGA,KAAID,SAAQ,KAAK,OAAOC,MAAK;AACzC,4BAAUD,SAAQ,SAAS;AAAA,gBAC/B;AAEA,uBAAO,GAAG,QAAQ;AAAA,kBACd,GAAG,OAAO;AAAA,oBACN;AAAA,kBACJ;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,CAAC;AAED,oBAAQ,OAAO;AAAA,cACX,MAAM,WAAW;AACb,oBAAII,MAAK,CAAC;AACV,yBAASF,QAAO,OAAQ,CAAAE,IAAGF,KAAI,YAAY,CAAC,IAAIA;AAChD,uBAAOE;AAAA,cACX;AAAA;AAAA,cAEA,aAAa,SAAS,aAAa,KAAK,iBAAiB,SAAS;AAG9D,yBAAS,eAAe,KAAK,EAAE;AAC/B,oBAAI,QAAQ,SAAS,eAAe,KAAK,WAAW,GAChD,MAAM,SAAS,MAAM,CAAC,GACtB,OAAO,OAAO,IAAI,YAAY,GAC9B,OAAO,KAAK,KAAK,EAAE,IAAI,GACvB,SAAS,SAAS,MAAM,CAAC,KAAK;AAClC,oBAAI,YAAY,KAAK,iBAAiB,GAAG;AAGzC,oBAAI;AAQA,2BAAS,KAAK,0DAA0D,SAAS,GAAG;AAAA,gBACxF,SAAS,OAAO;AAKZ,2BAAS,MAAM,CAAC,EAAE,MAAM,MAAM;AAAA,gBAClC;AAGA,oBAAI,OAAQ,OAAO,IAAM,QAAO,IAAI,GAAG;AAOvC,oBACI,IAAI,OAAO,CAAC,MAAM,OAClB,UAAU,SAAS,EACrB,QAAO,KAAK,kBAAkB,KAAK,OAAO;AAG5C,oBAAI,mBACC,OAAO,oBAAoB,YAC3B,OAAO,mBACP,gBAAgB,gBAAgB,GAAG,GACtC;AAEE,kCAAgB,GAAG,IAAI,QAAQ,IAAI,gBAAgB,GAAG,GAAG,KAAK;AAAA,oBAC1D,gBAAgB;AAAA,oBAChB,wBAAwB;AAAA,kBAC5B,CAAC;AACD,yBAAO,gBAAgB,GAAG;AAAA,gBAC9B;AAGA,oBAAI,EAAE,OAAO,WAAW,EAAE,QAAQ,WAAW,EAAE,QAAQ,QAAS,QAAO;AAGvE,yBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,2BAAS,eAAe,KAAK,EAAE;AAC/B,sBAAI,SAAS,eAAe,KAAK,OAAO,CAAC,CAAC,GAAG;AACzC,2BAAO,CAAC,IAAI,QAAQ,YAAY,OAAO,CAAC,GAAG,KAAK,iBAAiB,OAAO;AAAA,kBAC5E;AAAA,gBACJ;AAEA,oBAAI,SAAS,OAAO,GAAG,KAAK,OAAO,IAAI,KAAK,OAAO,IAAI;AACvD,wBAAQ,KAAK,KAAK,MAAM,GAAG;AAAA,kBACvB,KAAK;AAED,2BAAO,OAAO,KAAK,MAAM;AAAA,kBAC7B,KAAK;AAED,2BAAO,UAAU;AACjB,wBAAI,KAAK,OAAO,MAAM,QAAQ,MAAM;AACpC,wBAAI,OAAO,OAAW,MAAK;AAC3B,2BAAO,OAAO;AACd,2BAAO;AAAA,gBACf;AAAA,cACJ;AAAA,cACA,mBAAmB,SAASF,MAAKF,UAAS;AACtC,oBAAI,cAAcE;AAClB,oBAAI,eAAe,KAAK,iBAAiBA,IAAG;AAC5C,oBAAI,oBAAoB,CAAC;AAGzB,oBAAIA,KAAI,OAAO,CAAC,MAAM,KAAK;AACvB,sCAAoB,CAACF,SAAQ,QAAQ,KAAK,CAAC,CAAC,EAAE;AAAA,oBAC1C,KAAK,cAAc,YAAY;AAAA,kBACnC;AAAA,gBACJ,OAAO;AAEH,sBAAI,aAAa,SAAS,GAAG;AACzB,wCAAoBA,SAAQ,QAAQ,KAAK,MAAM,CAAC;AAChD,sCAAkB,IAAI;AACtB,wCAAoB,KAAK;AAAA,sBACrB,kBAAkB,OAAO,YAAY;AAAA,oBACzC;AAAA,kBAEJ;AAAA,gBACJ;AAEA,oBAAI;AACA,kBAAAE,OAAM,aAAa,aAAa,SAAS,CAAC;AAC1C,sBAAI,iBAAiBF,SAAQ,QAAQ;AACrC,sBAAI,yBAAyBA,SAAQ,QAAQ;AAC7C,2BAASC,KAAI,GAAGA,KAAI,kBAAkB,SAAS,GAAGA,MAAK;AACnD,qCAAiB,eAAe,kBAAkBA,EAAC,CAAC;AACpD,6CAAyB,uBAAuB,kBAAkBA,EAAC,CAAC;AAAA,kBACxE;AAEA,sBAAI,kBAAmBC,QAAO,eAAiB,QAAO,eAAeA,IAAG;AAGxE,sBAAI,0BACC,OAAO,2BAA2B,YAClCA,QAAO,0BACP,gBAAgB,uBAAuBA,IAAG,GAC7C;AAEE,2CAAuBA,IAAG,IAAI,QAAQ,IAAI,uBAAuBA,IAAG,GAAGA,MAAK;AAAA,sBACxE;AAAA,sBACA;AAAA,oBACJ,CAAC;AACD,2BAAO,uBAAuBA,IAAG;AAAA,kBACrC;AAAA,gBACJ,SAAQ,KAAK;AAAA,gBAAE;AAEf,uBAAO,MAAM,aAAa,KAAK,GAAG;AAAA,cACtC;AAAA;AAAA,cAEA,eAAe,SAASG,YAAW;AAC/B,oBAAI,eAAe,CAAC;AACpB,yBAASJ,KAAI,GAAGA,KAAII,WAAU,QAAQJ,MAAK;AACvC,0BAAQI,WAAUJ,EAAC,GAAG;AAAA,oBAClB,KAAK;AACD,mCAAa,IAAI;AACjB;AAAA,oBACJ,KAAK;AACD;AAAA,oBACJ;AACI,mCAAa,KAAKI,WAAUJ,EAAC,CAAC;AAAA,kBACtC;AAAA,gBACJ;AACA,uBAAO;AAAA,cACX;AAAA,cACA,kBAAkB,SAAS,MAAM;AAC7B,oBAAIE,SAAQ,KAAK,MAAM,KAAK;AAC5B,oBAAI,CAACA,OAAMA,OAAM,SAAS,CAAC,EAAG,CAAAA,SAAQA,OAAM,MAAM,GAAG,EAAE;AACvD,oBAAI,CAACA,OAAM,CAAC,EAAG,CAAAA,SAAQA,OAAM,MAAM,CAAC;AACpC,uBAAOA;AAAA,cACX;AAAA,YACJ,CAAC;AAED,mBAAO,UAAU;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAAST,SAAQC,UAAS;AA0BhC,YAAAD,QAAO,UAAU;AAAA,cACb,MAAM;AAAA,cACN,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,gBAAgB;AAAA;AAAA;AAAA;AAAA,YAIpB;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS;AAKhC,gBAAIE,QAAO,CAAC;AAEZ,YAAAA,MAAK,SAAS,SAAS,SAAS;AAC5B,kBAAI,SAAS,UAAU,CAAC,KAAK,CAAC,GAC1BI,KAAI,GACJ,SAAS,UAAU,QACnBD,UAAS,MAAM,KAAK,MAAM;AAE9B,kBAAI,WAAW,GAAG;AACd,yBAAS;AACT,gBAAAC,KAAI;AAAA,cACR;AAEA,qBAAOA,KAAI,QAAQA,MAAK;AACpB,gBAAAD,WAAU,UAAUC,EAAC;AACrB,oBAAI,CAACD,SAAS;AAEd,qBAAK,QAAQA,UAAS;AAClB,wBAAM,OAAO,IAAI;AACjB,yBAAOA,SAAQ,IAAI;AAEnB,sBAAI,WAAW,KAAM;AACrB,sBAAI,SAAS,OAAW;AAExB,sBAAIH,MAAK,QAAQ,IAAI,KAAKA,MAAK,SAAS,IAAI,GAAG;AAC3C,wBAAIA,MAAK,QAAQ,IAAI,EAAG,SAAQ,OAAOA,MAAK,QAAQ,GAAG,IAAI,MAAM,CAAC;AAClE,wBAAIA,MAAK,SAAS,IAAI,EAAG,SAAQ,OAAOA,MAAK,SAAS,GAAG,IAAI,MAAM,CAAC;AAEpE,2BAAO,IAAI,IAAIA,MAAK,OAAO,OAAO,IAAI;AAAA,kBAC1C,OAAO;AACH,2BAAO,IAAI,IAAI;AAAA,kBACnB;AAAA,gBACJ;AAAA,cACJ;AAEA,qBAAO;AAAA,YACX;AAEA,YAAAA,MAAK,OAAO,SAAS,KAAKS,MAAK,UAAU,SAAS;AAC9C,kBAAIL,IAAGC;AACP,kBAAI,KAAK,KAAKI,IAAG,MAAM,UAAU;AAC7B,qBAAKL,KAAI,GAAGA,KAAIK,MAAKL,MAAK;AACtB,2BAASA,IAAGA,EAAC;AAAA,gBACjB;AAAA,cACJ,WAAWK,KAAI,WAAW,CAACA,KAAI,QAAQ;AACnC,qBAAKL,KAAI,GAAGA,KAAIK,KAAI,QAAQL,MAAK;AAC7B,sBAAI,SAAS,KAAK,SAASK,KAAIL,EAAC,GAAGA,IAAGK,IAAG,MAAM,MAAO;AAAA,gBAC1D;AAAA,cACJ,OAAO;AACH,qBAAKJ,QAAOI,MAAK;AACb,sBAAI,SAAS,KAAK,SAASA,KAAIJ,IAAG,GAAGA,MAAKI,IAAG,MAAM,MAAO;AAAA,gBAC9D;AAAA,cACJ;AAAA,YACJ;AAEA,YAAAT,MAAK,OAAO,SAAS,KAAKS,MAAK;AAC3B,qBAAQA,SAAQ,QAAQA,SAAQ,SAAa,OAAOA,IAAG,IAAI,OAAO,UAAU,SAAS,KAAKA,IAAG,EAAE,MAAM,kBAAkB,EAAE,CAAC,EAAE,YAAY;AAAA,YAC5I;AAEA,YAAAT,MAAK,KAAK,sCAAsC,MAAM,GAAG,GAAG,SAAS,OAAO;AACxE,cAAAA,MAAK,OAAO,KAAK,IAAI,SAASS,MAAK;AAC/B,uBAAOT,MAAK,KAAKS,IAAG,MAAM,MAAM,YAAY;AAAA,cAChD;AAAA,YACJ,CAAC;AAED,YAAAT,MAAK,kBAAkB,SAAS,OAAO;AACnC,qBAAOA,MAAK,SAAS,KAAK,KAAKA,MAAK,QAAQ,KAAK;AAAA,YACrD;AAEA,YAAAA,MAAK,YAAY,SAAS,OAAO;AAC7B,qBAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,SAAS,KAAK;AAAA,YACtD;AAEA,YAAAA,MAAK,OAAO,SAASS,MAAK;AACtB,kBAAI,OAAO,CAAC;AACZ,uBAASJ,QAAOI,MAAK;AACjB,oBAAIA,KAAI,eAAeJ,IAAG,EAAG,MAAK,KAAKA,IAAG;AAAA,cAC9C;AACA,qBAAO;AAAA,YACX;AACA,YAAAL,MAAK,SAAS,SAASS,MAAK;AACxB,kBAAI,SAAS,CAAC;AACd,uBAASJ,QAAOI,MAAK;AACjB,oBAAIA,KAAI,eAAeJ,IAAG,EAAG,QAAO,KAAKI,KAAIJ,IAAG,CAAC;AAAA,cACrD;AACA,qBAAO;AAAA,YACX;AAwBA,YAAAL,MAAK,UAAU,SAAS,QAAQ,IAAI;AAIhC,qBAAO,GAAG,SAAS,EACd,QAAQ,iBAAiB,EAAE,EAC3B,QAAQ,eAAe,EAAE,EACzB,QAAQ,cAAc,EAAE,EAAE,QAAQ,cAAc,EAAE;AAAA,YAC3D;AAEA,YAAAA,MAAK,OAAO,WAAW;AAAA,YAAC;AAExB,YAAAH,QAAO,UAAUG;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAASH,SAAQC,UAASF,sBAAqB;AA0BrD,gBAAIc,YAAWd,qBAAoB,CAAC;AACpC,gBAAIK,UAASL,qBAAoB,CAAC;AAGlC,YAAAC,QAAO,UAAU;AAAA,cAChB,OAAO,SAAS,MAAM;AACrB,uBAAO,QAAQ,SAAY,KAAM,OAAO;AAExC,oBAAI,cAAc,QAAQ,IAAI,MAAMa,UAAS,MAAM;AAEnD,oBAAI,QAAQ,cAAc,WAAW,CAAC,KAAK,WAAW,CAAC,EAAE,MAAMA,UAAS,QAAQ;AAChF,oBAAI,MAAM,SAAS,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,GAAG,EAAE;AACpD,oBAAI,MAAM,SAAS,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,GAAG,EAAE;AAGpD,oBAAI,QAAQ,QAAQ,CAAC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE,IAAIT,QAAO,QAAQ,KAAK,GAAG,IAAI;AAEpF,oBAAI,UAAU,cAAc,WAAW,CAAC,KAAK,WAAW,CAAC,EAAE,MAAMS,UAAS,QAAQ;AAClF,oBAAI,OAAO,WAAW,QAAQ,CAAC,KAAK,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC3D,oBAAI,OAAO,WAAW,QAAQ,CAAC,KAAK,SAAS,QAAQ,CAAC,GAAG,EAAE;AAE3D,oBAAI,SAAS,UAAU,CAAC,QAAQ,CAAC,KAAK,SAAS,QAAQ,CAAC,GAAG,EAAE,KAAKT,QAAO,QAAQ,MAAM,IAAI,IAAI;AAE/F,oBAAI,SAAS;AAAA;AAAA,kBAEZ;AAAA;AAAA,kBAEA;AAAA,kBACA;AAAA,kBACA;AAAA;AAAA,kBAEA;AAAA;AAAA,kBAEA;AAAA,kBACA;AAAA,kBACA;AAAA;AAAA,kBAEA;AAAA,gBACD;AAEA,yBAAS,KAAK,QAAQ;AACrB,sBAAI,OAAO,CAAC,KAAK,OAAW,QAAO;AAAA,gBACpC;AAEA,uBAAO,CAAC;AAAA,cACT;AAAA,YACD;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASJ,SAAQC,UAASF,sBAAqB;AAQrD,gBAAII,QAAOJ,qBAAoB,CAAC;AAEhC,gBAAIK,UAAS;AAAA,cACT,QAAQD,MAAK;AAAA,YACjB;AAEA,YAAAC,QAAO,OAAOL,qBAAoB,CAAC,CAAC;AACpC,YAAAK,QAAO,OAAOL,qBAAoB,CAAC,CAAC;AACpC,YAAAK,QAAO,OAAOL,qBAAoB,CAAC,CAAC;AACpC,YAAAK,QAAO,OAAOL,qBAAoB,EAAE,CAAC;AACrC,YAAAK,QAAO,OAAOL,qBAAoB,EAAE,CAAC;AACrC,YAAAK,QAAO,OAAOL,qBAAoB,EAAE,CAAC;AACrC,YAAAK,QAAO,OAAOL,qBAAoB,EAAE,CAAC;AACrC,YAAAK,QAAO,OAAOL,qBAAoB,EAAE,CAAC;AACrC,YAAAK,QAAO,OAAOL,qBAAoB,EAAE,CAAC;AACrC,YAAAK,QAAO,OAAOL,qBAAoB,EAAE,CAAC;AAErC,YAAAC,QAAO,UAAUI;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAASJ,SAAQC,UAAS;AAKhC,YAAAD,QAAO,UAAU;AAAA;AAAA,cAEb,SAAS,SAAS,KAAK,KAAK,KAAK;AAC7B,oBAAI,QAAQ,QAAW;AACnB,wBAAM,OAAO,QAAQ,eAAe,CAAC,MAAM,GAAG,IAAI,SAAS,KAAK,EAAE,IAAI;AACtE,wBAAM,OAAO,QAAQ,eAAe,CAAC,MAAM,GAAG,IAAI,SAAS,KAAK,EAAE,IAAI;AACtE,yBAAO,KAAK,OAAO,IAAI,KAAO,MAAM,OAAO,MAAM,CAAC,MAAM;AAAA,gBAC5D;AAEA,uBAAO,KAAK,OAAO,KAAK;AAAA,cAC5B;AAAA,cACA,MAAM,SAAS,KAAK,KAAK,KAAK;AAC1B,uBAAO,KAAK,QAAQ,KAAK,KAAK,GAAG;AAAA,cACrC;AAAA;AAAA,cAEA,SAAS,SAAS,KAAK,KAAK;AACxB,sBAAM,OAAO,QAAQ,cAAc,SAAS,KAAK,EAAE,IAAI;AACvD,sBAAM,OAAO,QAAQ,cAAc,SAAS,KAAK,EAAE,IAAI;AACvD,uBAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI,IAAI;AAAA,cACrD;AAAA;AAAA,cAEA,SAAS,SAAS,KAAK,KAAK;AACxB,sBAAM,OAAO,QAAQ,cAAc,SAAS,KAAK,EAAE,IAAI;AACvD,sBAAM,OAAO,QAAQ,cAAc,SAAS,KAAK,EAAE,IAAI;AACvD,uBAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI,IAAI;AAAA,cACrD;AAAA,cACA,KAAK,SAAS,KAAK,KAAK;AACpB,uBAAO,KAAK,QAAQ,KAAK,GAAG;AAAA,cAChC;AAAA;AAAA,cAEA,OAAO,SAAS,KAAK,KAAK,MAAM,MAAM;AAClC,uBAAO,SAAS,SAAY,IAAI;AAChC,uBAAO,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,GAAG,CAAC;AACrC,uBAAO,SAAS,SAAY,KAAK;AACjC,uBAAO,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,GAAG,CAAC;AACrC,oBAAI,MAAM,KAAK,QAAQ,KAAK,GAAG,IAAI;AACnC,yBAASO,KAAI,GAAG,SAAS,KAAK,QAAQ,MAAM,IAAI,GAAGA,KAAI,QAAQA,MAAK;AAChE;AAAA,kBAEKA,KAAI,SAAS,IAAK,KAAK,UAAU,QAAQ,IAAI,KAAK,UAAU,WAAW;AAAA,gBAEhF;AACA,uBAAO,WAAW,KAAK,EAAE;AAAA,cAC7B;AAAA;AAAA,cAEA,WAAW,SAAS,MAAM;AACtB,oBAAI,QAAQ;AAAA,kBACR,OAAO;AAAA,kBACP,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,QAAQ;AAAA,gBACZ;AACA,sBAAM,QAAQ,MAAM,QAAQ,MAAM;AAClC,sBAAM,WAAW,IAAI,MAAM,QAAQ,MAAM,QAAQ,MAAM,SAAS,MAAM;AAEtE,uBAAO,OAAO,KAAK,MAAM,YAAY,CAAC,KAAK;AAC3C,uBAAO,KAAK,OAAO,KAAK,QAAQ,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,cACvD;AAAA,cACA,MAAM,SAAS,MAAM;AACjB,uBAAO,KAAK,UAAU,IAAI;AAAA,cAC9B;AAAA;AAAA,cAEA,QAAQ,SAAS,MAAM,KAAK,KAAK;AAC7B,oBAAI;AACJ,wBAAQ,UAAU,QAAQ;AAAA,kBACtB,KAAK;AACD,0BAAM,KAAK,QAAQ,GAAG,CAAC;AACvB;AAAA,kBACJ,KAAK;AACD,0BAAM;AACN,2BAAO;AACP;AAAA,kBACJ,KAAK;AAED,wBAAI,OAAO,UAAU,CAAC,MAAM,UAAU;AAClC,4BAAM;AAAA,oBACV,OAAO;AAEH,4BAAM,KAAK,QAAQ,MAAM,GAAG;AAC5B,6BAAO;AAAA,oBACX;AACA;AAAA,kBACJ,KAAK;AACD,0BAAM,KAAK,QAAQ,KAAK,GAAG;AAC3B;AAAA,gBACR;AAEA,oBAAI,OAAO;AACX,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,0BAAQ,KAAK,UAAU,IAAI;AAAA,gBAC/B;AAEA,uBAAO;AAAA,cACX;AAAA,cACA,KAAK,WAA+B;AAChC,uBAAO,KAAK,OAAO,MAAM,MAAM,SAAS;AAAA,cAC5C;AAAA;AAAA,cAEA,OAAO,SAAS,OAAO,MAAM,MAAM;AAE/B,oBAAI,UAAU,UAAU,GAAG;AACvB,yBAAO,SAAS;AAChB,0BAAQ;AAAA,gBACZ;AAEA,uBAAO,UAAU,CAAC,KAAK;AAEvB,wBAAQ,CAAC;AACT,uBAAO,CAAC;AACR,uBAAO,CAAC;AAER,oBAAI,MAAM,KAAK,IAAI,KAAK,MAAM,OAAO,SAAS,IAAI,GAAG,CAAC;AACtD,oBAAI,MAAM;AACV,oBAAI,QAAQ,IAAI,MAAM,GAAG;AAEzB,uBAAO,MAAM,KAAK;AACd,wBAAM,KAAK,IAAI;AACf,2BAAS;AAAA,gBACb;AAEA,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASP,SAAQC,UAAS;AAKhC,gBAAI,iBAAiB;AAAA,cACjB,MAAM;AAAA,cACN,IAAI,SAAS,MAAM;AACf,wBAAQ,KAAK,KAAK,YAAY,GAAG,MAAM,CAAC;AAAA,cAC5C;AAAA,cACA,GAAG;AAAA,cAEH,IAAI,SAAS,MAAM;AACf,oBAAI,IAAI,KAAK,SAAS,IAAI;AAC1B,uBAAO,IAAI,KAAK,MAAM,IAAI;AAAA,cAC9B;AAAA,cACA,GAAG,SAAS,MAAM;AACd,uBAAO,KAAK,SAAS,IAAI;AAAA,cAC7B;AAAA,cAEA,IAAI,SAAS,MAAM;AACf,oBAAI,IAAI,KAAK,QAAQ;AACrB,uBAAO,IAAI,KAAK,MAAM,IAAI;AAAA,cAC9B;AAAA,cACA,GAAG;AAAA,cAEH,IAAI,SAAS,MAAM;AACf,oBAAI,IAAI,KAAK,SAAS;AACtB,uBAAO,IAAI,KAAK,MAAM,IAAI;AAAA,cAC9B;AAAA,cACA,GAAG;AAAA,cACH,IAAI,SAAS,MAAM;AACf,oBAAI,IAAI,KAAK,SAAS,IAAI;AAC1B,uBAAO,IAAI,KAAK,MAAM,IAAI;AAAA,cAC9B;AAAA,cACA,GAAG,SAAS,MAAM;AACd,uBAAO,KAAK,SAAS,IAAI;AAAA,cAC7B;AAAA,cAEA,IAAI,SAAS,MAAM;AACf,oBAAI,IAAI,KAAK,WAAW;AACxB,uBAAO,IAAI,KAAK,MAAM,IAAI;AAAA,cAC9B;AAAA,cACA,GAAG;AAAA,cAEH,IAAI,SAAS,MAAM;AACf,oBAAI,IAAI,KAAK,WAAW;AACxB,uBAAO,IAAI,KAAK,MAAM,IAAI;AAAA,cAC9B;AAAA,cACA,GAAG;AAAA,cAEH,IAAI,SAAS,MAAM;AACf,oBAAI,KAAK,KAAK,gBAAgB;AAC9B,uBAAO,KAAK,MAAM,OAAO,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,cAC3D;AAAA,cACA,GAAG;AAAA,cAEH,GAAG,SAAS,MAAM;AACd,uBAAO,KAAK,SAAS,IAAI,KAAK,OAAO;AAAA,cACzC;AAAA,cACA,GAAG,SAAS,MAAM;AACd,uBAAO,KAAK,SAAS,IAAI,KAAK,OAAO;AAAA,cACzC;AAAA,cACA,GAAG;AAAA,YACP;AACA,YAAAD,QAAO,UAAU;AAAA;AAAA,cAEb,iBAAiB;AAAA;AAAA,cAEjB,UAAU,IAAI,OAAQ,WAAW;AAC7B,oBAAIU,MAAK,CAAC;AACV,yBAASH,MAAK,eAAgB,CAAAG,IAAG,KAAKH,EAAC;AACvC,uBAAO,MAAMG,IAAG,KAAK,GAAG,IAAI;AAAA,cAChC,EAAG,GAAG,GAAG;AAAA;AAAA,cAET,aAAa,SAAS,MAAM,QAAQ;AAChC,uBAAO,OAAO,QAAQ,KAAK,UAAU,SAAS,kBAAkB,IAAI,MAAM;AACtE,yBAAO,OAAO,eAAe,IAAI,MAAM,aAAa,eAAe,IAAI,EAAE,IAAI,IACzE,eAAe,IAAI,KAAK,iBAAiB,kBAAkB,IAAI,eAAe,IAAI,CAAC,IACnF,KAAK,eAAe,IAAI,CAAC,EAAE;AAAA,gBACnC,CAAC;AAAA,cACL;AAAA;AAAA,cAEA,aAAa,SAAS,KAAK,KAAK;AAC5B,sBAAM,QAAQ,SAAY,oBAAI,KAAK,CAAC,IAAI;AACxC,sBAAM,QAAQ,SAAY,oBAAI,KAAK,IAAI;AACvC,uBAAO,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI,QAAQ,IAAI,IAAI,QAAQ,EAAE;AAAA,cACnE;AAAA;AAAA,cAEA,MAAM,SAAS,QAAQ;AACnB,yBAAS,UAAU;AACnB,uBAAO,KAAK,YAAY,KAAK,YAAY,GAAG,MAAM;AAAA,cACtD;AAAA;AAAA,cAEA,MAAM,SAAS,QAAQ;AACnB,yBAAS,UAAU;AACnB,uBAAO,KAAK,YAAY,KAAK,YAAY,GAAG,MAAM;AAAA,cACtD;AAAA;AAAA,cAEA,UAAU,SAAS,QAAQ;AACvB,yBAAS,UAAU;AACnB,uBAAO,KAAK,YAAY,KAAK,YAAY,GAAG,MAAM;AAAA,cACtD;AAAA;AAAA,cAEA,KAAK,SAAS,MAAM,QAAQ;AAExB,oBAAI,UAAU,WAAW,GAAG;AAExB,sBAAI,CAAC,yCAAyC,KAAK,IAAI,GAAG;AACtD,6BAAS;AACT,2BAAO;AAAA,kBACX;AAAA,gBACJ;AACA,wBAAQ,QAAQ,IAAI,YAAY;AAChC,yBAAS,UAAU;AAEnB,oBAAI,OAAO,oBAAI,KAAK;AAIpB,wBAAQ,MAAM;AAAA,kBACV,KAAK;AACD,yBAAK,SAAS,CAAC;AAAA,kBACnB,KAAK;AACD,yBAAK,QAAQ,CAAC;AAAA,kBAClB,KAAK;AAAA,kBACL,KAAK;AACD,yBAAK,SAAS,CAAC;AAAA,kBACnB,KAAK;AACD,yBAAK,WAAW,CAAC;AAAA,kBACrB,KAAK;AACD,yBAAK,WAAW,CAAC;AAAA,kBACrB,KAAK;AACD,yBAAK,gBAAgB,CAAC;AAAA,gBAC9B;AACA,wBAAQ,MAAM;AAAA,kBACV,KAAK;AACD,yBAAK,QAAQ,KAAK,QAAQ,IAAI,KAAK,OAAO,CAAC;AAAA,gBACnD;AAEA,uBAAO,KAAK,YAAY,MAAM,MAAM;AAAA,cACxC;AAAA,YACJ;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASV,SAAQC,UAASF,sBAAqB;AAE1B,aAAC,SAASC,SAAQ;AAI7C,cAAAA,QAAO,UAAU;AAAA;AAAA,gBAEb,SAAS;AAAA,kBACL;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAC5C;AAAA,kBAAW;AAAA,kBAAU;AAAA,kBAAU;AAAA,kBAAS;AAAA,kBACxC;AAAA,kBAAU;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAU;AAAA,kBAC1C;AAAA,kBAAW;AAAA,gBACf;AAAA;AAAA,gBAEA,aAAa;AAAA,kBACT;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAC5C;AAAA,kBAAY;AAAA,kBAAY;AAAA,kBAAY;AAAA,kBAAY;AAAA,kBAChD;AAAA,gBACJ;AAAA;AAAA,gBAEA,YAAY,CAAC,WAAW,WAAW,YAAY,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAU1D,OAAO,SAAS,MAAM,YAAY,YAAY,QAAQ,MAAM;AAExD,sBAAI,UAAU,WAAW,GAAG;AACxB,2BAAO;AACP,6BAAS;AAAA,kBACb;AAEA,sBAAI,UAAU,WAAW,GAAG;AACxB,2BAAO;AACP,iCAAa;AAAA,kBACjB;AAEA,sBAAI,CAAC,KAAM,QAAO,KAAK,KAAK,KAAK,OAAO;AAExC,sBAAI,cAAc,CAAC,WAAW,QAAQ,GAAG,EAAG,cAAa,WAAW,MAAM,CAAC;AAC3E,sBAAI,cAAc,CAAC,WAAW,QAAQ,GAAG,EAAG,cAAa,WAAW,MAAM,CAAC;AAG3E,yBAAO,2BAA2B,QAC7B,aAAa,MAAM,aAAa,OAChC,aAAa,MAAM,aAAa,OAChC,SAAS,MAAM,SAAS,OACxB,OAAO,WAAW,OAAO;AAAA,gBAClC;AAAA,gBACA,KAAK,WAAW;AACZ,yBAAO,KAAK,MAAM,MAAM,MAAM,SAAS;AAAA,gBAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAcA,cAAc;AAAA,kBACV,UAAU;AAAA,kBACV,SAAS;AAAA,kBACT,mBAAmB;AAAA,kBACnB,qBAAqB;AAAA,kBACrB,aAAa;AAAA,kBACb,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,UAAU;AAAA,kBACV,WAAW;AAAA,kBACX,gBAAiB;AAAA,kBACjB,OAAO;AAAA,kBACP,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,cAAc;AAAA,kBACd,SAAS;AAAA,kBACT,WAAW;AAAA,kBACX,UAAU;AAAA,kBACV,eAAe;AAAA,kBACf,cAAc;AAAA,kBACd,WAAW;AAAA,kBACX,eAAe;AAAA,kBACf,aAAa;AAAA,kBACb,QAAQ;AAAA,kBACR,aAAa;AAAA,kBACb,cAAc;AAAA,kBACd,iBAAiB;AAAA,kBACjB,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,iBAAiB;AAAA,kBACjB,mBAAmB;AAAA,kBACnB,YAAY;AAAA,kBACZ,WAAW;AAAA,kBACX,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,QAAQ;AAAA,kBACR,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,UAAU;AAAA,kBACV,QAAQ;AAAA,kBACR,YAAY;AAAA,kBACZ,WAAW;AAAA,kBACX,YAAY;AAAA,kBACZ,WAAW;AAAA,kBACX,iBAAiB;AAAA,kBACjB,iBAAiB;AAAA,kBACjB,UAAU;AAAA,kBACV,cAAc;AAAA,kBACd,UAAU;AAAA,kBACV,WAAW;AAAA,kBACX,YAAY;AAAA,kBACZ,UAAU;AAAA,kBACV,eAAe;AAAA,kBACf,gBAAgB;AAAA,kBAChB,cAAc;AAAA,kBACd,iBAAiB;AAAA,kBACjB,WAAW;AAAA,kBACX,eAAe;AAAA,kBACf,WAAW;AAAA,kBACX,eAAe;AAAA,kBACf,eAAe;AAAA,kBACf,kBAAkB;AAAA,kBAClB,iBAAiB;AAAA,kBACjB,aAAa;AAAA,kBACb,SAAS;AAAA,kBACT,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,QAAQ;AAAA,kBACR,aAAa;AAAA,kBACb,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,eAAe;AAAA,kBACf,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,YAAY;AAAA,kBACZ,cAAc;AAAA,kBACd,QAAQ;AAAA,kBACR,YAAY;AAAA,kBACZ,UAAU;AAAA,kBACV,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,iBAAiB;AAAA,kBACjB,kBAAkB;AAAA,kBAClB,YAAY;AAAA,kBACZ,aAAa;AAAA,kBACb,eAAe;AAAA,kBACf,UAAU;AAAA,kBACV,SAAS;AAAA,kBACT,SAAS;AAAA,kBACT,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,QAAQ;AAAA,kBACR,eAAe;AAAA,kBACf,WAAW;AAAA,kBACX,YAAY;AAAA,kBACZ,YAAY;AAAA,kBACZ,QAAQ;AAAA,kBACR,OAAO;AAAA,kBACP,cAAc;AAAA,kBACd,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,YAAY;AAAA,kBACZ,cAAc;AAAA,kBACd,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,UAAU;AAAA,kBACV,eAAe;AAAA,kBACf,iBAAiB;AAAA,kBACjB,WAAW;AAAA,kBACX,gBAAgB;AAAA,kBAChB,UAAU;AAAA,kBACV,aAAa;AAAA,kBACb,eAAe;AAAA,kBACf,YAAY;AAAA,kBACZ,cAAc;AAAA,kBACd,gBAAgB;AAAA,kBAChB,aAAa;AAAA,kBACb,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,aAAa;AAAA,kBACb,WAAW;AAAA,kBACX,SAAS;AAAA,kBACT,UAAU;AAAA,kBACV,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,SAAS;AAAA,kBACT,QAAQ;AAAA,kBACR,QAAQ;AAAA,kBACR,gBAAgB;AAAA,kBAChB,SAAS;AAAA,kBACT,oBAAoB;AAAA,kBACpB,sBAAsB;AAAA,kBACtB,oBAAoB;AAAA,kBACpB,cAAc;AAAA,kBACd,QAAQ;AAAA,kBACR,QAAQ;AAAA,kBACR,UAAU;AAAA,kBACV,UAAU;AAAA,kBACV,QAAQ;AAAA,kBACR,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,WAAW;AAAA,kBACX,UAAU;AAAA,kBACV,WAAW;AAAA,gBACf;AAAA,gBACA,aAAa,WAAW;AACpB,sBAAI,SAAS,CAAC;AACd,2BAAS,KAAK,KAAK,cAAc;AAC7B,2BAAO,KAAK,CAAC;AAAA,kBACjB;AACA,yBAAO;AAAA,gBACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAWA,WAAW,SAAS,MAAM,MAAM;AAC5B,sBAAI;AACJ,sBAAI,OAAO,aAAa,aAAa;AACjC,6BAAS,SAAS,cAAc,QAAQ;AAAA,kBAC5C,OAAO;AAWH,wBAAI,SAASA,QAAO,QAAQ,QAAQ;AACpC,6BAAS,IAAI,OAAO;AAAA,kBACxB;AAEA,sBAAI,MAAM,UAAU,OAAO,cAAc,OAAO,WAAW,IAAI;AAC/D,sBAAI,CAAC,UAAU,CAAC,IAAK,QAAO;AAE5B,sBAAI,CAAC,KAAM,QAAO,KAAK,KAAK,KAAK,OAAO;AACxC,yBAAO,SAAS,SAAY,OAAO;AAEnC,yBAAO,KAAK,MAAM,GAAG;AAErB,sBAAI,QAAQ,SAAS,KAAK,CAAC,GAAG,EAAE,GAC5B,SAAS,SAAS,KAAK,CAAC,GAAG,EAAE,GAC7B,aAAa,KAAK,aAAa,KAAK,KAAK,KAAK,YAAY,CAAC,CAAC,GAC5D,aAAa,QACb,cAAc,IACd,OAAO;AAEX,yBAAO,QAAQ;AACf,yBAAO,SAAS;AAChB,sBAAI,YAAY;AAChB,sBAAI,eAAe;AACnB,sBAAI,YAAY;AAChB,sBAAI,SAAS,GAAG,GAAG,OAAO,MAAM;AAChC,sBAAI,YAAY;AAChB,sBAAI,OAAO,UAAU,cAAc,QAAQ;AAC3C,sBAAI,SAAS,MAAO,QAAQ,GAAK,SAAS,GAAI,KAAK;AACnD,yBAAO,OAAO,UAAU,WAAW;AAAA,gBACvC;AAAA,cACJ;AAAA,YAC2B,GAAE,KAAKC,UAASF,qBAAoB,CAAC,EAAEC,OAAM,CAAC;AAAA,UAEpE;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS;AAEhC,YAAAD,QAAO,UAAU,SAASA,SAAQ;AACjC,kBAAG,CAACA,QAAO,iBAAiB;AAC3B,gBAAAA,QAAO,YAAY,WAAW;AAAA,gBAAC;AAC/B,gBAAAA,QAAO,QAAQ,CAAC;AAEhB,gBAAAA,QAAO,WAAW,CAAC;AACnB,gBAAAA,QAAO,kBAAkB;AAAA,cAC1B;AACA,qBAAOA;AAAA,YACR;AAAA,UAGK;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAASF,sBAAqB;AA4ErD,gBAAI,UAAUA,qBAAoB,EAAE;AACpC,gBAAI,OAAOA,qBAAoB,EAAE;AAEjC,YAAAC,QAAO,UAAU;AAAA;AAAA,cAEb,OAAO,SAAS,MAAM;AAClB,oBAAI,QAAQ,KAAK,IAAI,EAAG,QAAO,KAAK,IAAI,EAAE;AAC1C,uBAAO,KAAK,IAAI;AAAA,cACpB;AAAA;AAAA,cAEA,KAAK,WAAW;AACZ,oBAAI,MAAM,KAAK,kBAAkB;AACjC,oBAAI,MAAM,QAAQ,QAAQ,GAAG;AAC7B,oBAAI,MAAM,QAAQ,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAChD,uBAAO;AAAA,cACX;AAAA;AAAA,cAEA,KAAK,WAAW;AACZ,oBAAI,MAAM,KAAK,kBAAkB;AACjC,oBAAI,MAAM,QAAQ,QAAQ,GAAG;AAC7B,uBAAO,SACH,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,OACvB,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,OACvB,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI;AAAA,cAC/B;AAAA;AAAA,cAEA,MAAM,WAAW;AACb,oBAAI,MAAM,KAAK,kBAAkB;AACjC,oBAAI,MAAM,QAAQ,QAAQ,GAAG;AAC7B,uBAAO,UACH,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,OACvB,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,OACvB,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,OACvB,KAAK,OAAO,EAAE,QAAQ,CAAC,IAAI;AAAA,cACnC;AAAA;AAAA,cAEA,KAAK,WAAW;AACZ,oBAAI,MAAM,KAAK,kBAAkB;AACjC,oBAAI,MAAM,QAAQ,QAAQ,GAAG;AAC7B,uBAAO,SACH,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,OACvB,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,OACvB,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI;AAAA,cAC/B;AAAA;AAAA;AAAA;AAAA,cAIA,mBAAmB,SAAS,YAAY,OAAO;AAC3C,qBAAK,eAAe;AACpB,qBAAK,OAAO,KAAK,QAAQ,KAAK,OAAO;AACrC,qBAAK,QAAQ,KAAK;AAClB,qBAAK,QAAQ;AAEb,oBAAI,OAAO,eAAe,SAAU,cAAa;AACjD,oBAAI,OAAO,UAAU,SAAU,SAAQ;AAEvC,uBAAO;AAAA,kBACH,KAAK,OAAO;AAAA,kBACZ,aAAa;AAAA,kBACb,QAAQ;AAAA,gBACZ;AAAA,cACJ;AAAA,YACJ;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS;AAShC,YAAAD,QAAO,UAAU;AAAA,cAChB,SAAS,SAAS,QAAQ,KAAK;AAC9B,oBAAI,IAAI,IAAI,CAAC,IAAI,KAChB,IAAI,IAAI,CAAC,IAAI,KACb,IAAI,IAAI,CAAC,IAAI,KACb,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,QAAQ,MAAM,KACd,GAAG,GAAG;AAEP,oBAAI,OAAO;AACV,sBAAI;AAAA,yBACI,KAAK;AACb,uBAAK,IAAI,KAAK;AAAA,yBACN,KAAK;AACb,sBAAI,KAAK,IAAI,KAAK;AAAA,yBACV,KAAK;AACb,sBAAI,KAAK,IAAI,KAAK;AAEnB,oBAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AAExB,oBAAI,IAAI;AACP,uBAAK;AAEN,qBAAK,MAAM,OAAO;AAElB,oBAAI,OAAO;AACV,sBAAI;AAAA,yBACI,KAAK;AACb,sBAAI,SAAS,MAAM;AAAA;AAEnB,sBAAI,SAAS,IAAI,MAAM;AAExB,uBAAO,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,cAC5B;AAAA,cACA,SAAS,SAAS,QAAQ,KAAK;AAC9B,oBAAI,IAAI,IAAI,CAAC,GACZ,IAAI,IAAI,CAAC,GACT,IAAI,IAAI,CAAC,GACT,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACtB,QAAQ,MAAM,KACd,GAAG,GAAG;AAEP,oBAAI,QAAQ;AACX,sBAAI;AAAA;AAEJ,sBAAK,QAAQ,MAAM,MAAQ;AAE5B,oBAAI,OAAO;AACV,sBAAI;AAAA,yBACI,KAAK;AACb,uBAAK,IAAI,KAAK;AAAA,yBACN,KAAK;AACb,sBAAI,KAAK,IAAI,KAAK;AAAA,yBACV,KAAK;AACb,sBAAI,KAAK,IAAI,KAAK;AAEnB,oBAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AAExB,oBAAI,IAAI;AACP,uBAAK;AAEN,oBAAM,MAAM,MAAO,MAAQ;AAE3B,uBAAO,CAAC,GAAG,GAAG,CAAC;AAAA,cAChB;AAAA,cACA,SAAS,SAAS,QAAQ,KAAK;AAC9B,oBAAI,IAAI,IAAI,CAAC,IAAI,KAChB,IAAI,IAAI,CAAC,IAAI,KACb,IAAI,IAAI,CAAC,IAAI,KACb,IAAI,IAAI,IAAI,KAAK;AAElB,oBAAI,MAAM,GAAG;AACZ,wBAAM,IAAI;AACV,yBAAO,CAAC,KAAK,KAAK,GAAG;AAAA,gBACtB;AAEA,oBAAI,IAAI;AACP,uBAAK,KAAK,IAAI;AAAA;AAEd,uBAAK,IAAI,IAAI,IAAI;AAClB,qBAAK,IAAI,IAAI;AAEb,sBAAM,CAAC,GAAG,GAAG,CAAC;AACd,yBAASO,KAAI,GAAGA,KAAI,GAAGA,MAAK;AAC3B,uBAAK,IAAI,IAAI,IAAI,EAAEA,KAAI;AACvB,sBAAI,KAAK,EAAG;AACZ,sBAAI,KAAK,EAAG;AAEZ,sBAAI,IAAI,KAAK;AACZ,0BAAM,MAAM,KAAK,MAAM,IAAI;AAAA,2BACnB,IAAI,KAAK;AACjB,0BAAM;AAAA,2BACE,IAAI,KAAK;AACjB,0BAAM,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM;AAAA;AAEtC,0BAAM;AAEP,sBAAIA,EAAC,IAAI,MAAM;AAAA,gBAChB;AAEA,uBAAO;AAAA,cACR;AAAA,cACA,SAAS,SAAS,QAAQ,KAAK;AAC9B,oBAAI,IAAI,IAAI,CAAC,GACZ,IAAI,IAAI,CAAC,IAAI,KACb,IAAI,IAAI,CAAC,IAAI,KACb,IAAI;AACL,qBAAK;AACL,qBAAM,KAAK,IAAK,IAAI,IAAI;AACxB,qBAAK,IAAI,KAAK;AACd,qBAAM,IAAI,KAAM,IAAI;AACpB,uBAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,cAC7B;AAAA,cACA,SAAS,SAAS,QAAQ,KAAK;AAC9B,oBAAI,IAAI,IAAI,CAAC,IAAI;AACjB,oBAAI,IAAI,IAAI,CAAC,IAAI;AACjB,oBAAI,IAAI,IAAI,CAAC,IAAI;AACjB,oBAAI,KAAK,KAAK,MAAM,CAAC,IAAI;AAEzB,oBAAI,IAAI,IAAI,KAAK,MAAM,CAAC;AACxB,oBAAI,IAAI,MAAM,KAAK,IAAI;AACvB,oBAAI,IAAI,MAAM,KAAK,IAAK,IAAI;AAC5B,oBAAI,IAAI,MAAM,KAAK,IAAK,KAAK,IAAI;AAEjC,oBAAI,MAAM;AAEV,wBAAQ,IAAI;AAAA,kBACX,KAAK;AACJ,2BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,kBAChB,KAAK;AACJ,2BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,kBAChB,KAAK;AACJ,2BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,kBAChB,KAAK;AACJ,2BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,kBAChB,KAAK;AACJ,2BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,kBAChB,KAAK;AACJ,2BAAO,CAAC,GAAG,GAAG,CAAC;AAAA,gBACjB;AAAA,cACD;AAAA,cACA,SAAS,SAAS,QAAQ,KAAK;AAC9B,oBAAI,IAAI,IAAI,CAAC,GACZ,IAAI,IAAI,CAAC,IAAI,KACb,IAAI,IAAI,CAAC,IAAI,KACb,IAAI;AAEL,qBAAK,IAAI,KAAK;AACd,qBAAK,IAAI;AACT,sBAAO,KAAK,IAAK,IAAI,IAAI;AACzB,qBAAK;AACL,uBAAO,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG;AAAA,cAC7B;AAAA;AAAA,cAEA,SAAS,SACR,GACA,GACA,GACC;AACD,uBAAO,QAAQ,MAAM,KAAK,IAAI,MAAM,IAAI,GAAG,SAAS,EAAE,EAAE,MAAM,CAAC;AAAA,cAChE;AAAA,cACA,SAAS,SACR,GACC;AACD,oBAAI,OAAO,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,SAAS,IAAI,IAAI,MAAM,MAAM,IAAI;AACjE,uBAAO,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG;AAAA,cACvC;AAAA,YACD;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASP,SAAQC,UAAS;AAOhC,YAAAD,QAAO,UAAU;AAAA;AAAA,cAEb,MAAM;AAAA,gBACF,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,MAAM;AAAA,gBACF,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,MAAM;AAAA,gBACF,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,MAAM;AAAA,gBACF,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,OAAO;AAAA,gBACH,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,OAAO;AAAA,gBACH,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,MAAM;AAAA,gBACF,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,QAAQ;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,QAAQ;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,KAAK;AAAA,gBACD,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,QAAQ;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,SAAS;AAAA,gBACL,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,QAAQ;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,QAAQ;AAAA,gBACJ,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,MAAM;AAAA,gBACF,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,OAAO;AAAA,gBACH,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,cACA,OAAO;AAAA,gBACH,OAAO;AAAA,gBACP,OAAO;AAAA,cACX;AAAA,YACJ;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAASF,sBAAqB;AAOrD,gBAAI,QAAQA,qBAAoB,CAAC;AACjC,gBAAI,SAASA,qBAAoB,EAAE;AAEnC,qBAAS,MAAM,YAAY,YAAY,KAAK,KAAK;AAC7C,qBAAO,QAAQ,SAAY,MAAM,QAAQ,YAAY,UAAU;AAAA;AAAA,gBAC3D,QAAQ,SAAY;AAAA;AAAA,kBACpB,MAAM,QAAQ,SAAS,KAAK,EAAE,GAAG,SAAS,KAAK,EAAE,CAAC;AAAA;AAAA;AAAA,YAC1D;AAEA,YAAAC,QAAO,UAAU;AAAA;AAAA,cAEb,WAAW,SAAS,KAAK,KAAK;AAC1B,oBAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG;AAC9B,oBAAI,SAAS,CAAC;AACd,yBAASO,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,yBAAO,KAAK,KAAK,SAAS,CAAC;AAAA,gBAC/B;AACA,uBAAO,OAAO,KAAK,GAAG;AAAA,cAC1B;AAAA;AAAA,cAEA,YAAY,SAAS,KAAK,KAAK;AAC3B,oBAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG;AAC9B,oBAAI,SAAS,CAAC;AACd,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,yBAAO,KAAK,KAAK,UAAU,CAAC;AAAA,gBAChC;AACA,uBAAO,OAAO,KAAK,EAAE;AAAA,cACzB;AAAA;AAAA,cAEA,UAAU,SAAS,KAAK,KAAK;AACzB,oBAAI,MAAM,MAAM,IAAI,IAAI,KAAK,GAAG;AAChC,oBAAI,SAAS,CAAC;AACd,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,yBAAO,KAAK,KAAK,KAAK,CAAC;AAAA,gBAC3B;AACA,uBAAO,OAAO,WAAW,OAAO,KAAK,GAAG,CAAC,IAAI;AAAA,cACjD;AAAA;AAAA,cAEA,WAAW,SAAS,KAAK,KAAK;AAC1B,oBAAI,MAAM,MAAM,IAAI,IAAI,KAAK,GAAG;AAChC,oBAAI,SAAS,CAAC;AACd,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,yBAAO,KAAK,KAAK,MAAM,CAAC;AAAA,gBAC5B;AAEA,uBAAO,OAAO,KAAK,EAAE,IAAI;AAAA,cAC7B;AAAA;AAAA,cAEA,MAAM,SAAS,KAAK,KAAK;AACrB,oBAAI,MAAM,MAAM,GAAG,IAAI,KAAK,GAAG;AAC/B,oBAAI,SAAS;AACb,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,4BAAU,MAAM,UAAU,OAAO;AAAA,gBACrC;AACA,uBAAO;AAAA,cACX;AAAA;AAAA,cAEA,OAAO,SAAS,MAAM,KAAK,KAAK;AAE5B,oBAAI,aAAa;AAEjB,oBAAI;AACJ,wBAAQ,UAAU,QAAQ;AAAA,kBACtB,KAAK;AACD,2BAAO;AACP,0BAAM;AACN;AAAA,kBACJ,KAAK;AACD,wBAAI,OAAO,UAAU,CAAC,MAAM,UAAU;AAClC,4BAAM;AAAA,oBACV,OAAO;AAEH,4BAAM;AACN,6BAAO;AAAA,oBACX;AACA;AAAA,kBACJ,KAAK;AAED,wBAAI,OAAO,UAAU,CAAC,MAAM,UAAU;AAClC,4BAAM;AAAA,oBACV,OAAO;AAEH,4BAAM,KAAK,QAAQ,MAAM,GAAG;AAC5B,6BAAO;AAAA,oBACX;AACA;AAAA,kBACJ,KAAK;AACD,0BAAM,KAAK,QAAQ,KAAK,GAAG;AAC3B;AAAA,gBACR;AAEA,oBAAI,SAAS;AACb,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,4BAAU,KAAK,OAAO,KAAK,QAAQ,GAAG,KAAK,SAAS,CAAC,CAAC;AAAA,gBAC1D;AACA,uBAAO;AAAA,cACX;AAAA;AAAA,cAEA,OAAO,SAAS,KAAK,KAAK;AACtB,oBAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG;AAC9B,oBAAI,SAAS,CAAC;AACd,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,yBAAO,KAAK,KAAK,WAAW,KAAK,KAAK,CAAC,CAAC;AAAA,gBAC5C;AACA,uBAAO,OAAO,KAAK,GAAG;AAAA,cAC1B;AAAA;AAAA,cAEA,QAAQ,SAAS,KAAK,KAAK;AACvB,oBAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG;AAC9B,oBAAI,SAAS,CAAC;AACd,yBAASA,KAAI,GAAGA,KAAI,KAAKA,MAAK;AAC1B,yBAAO,KAAK,KAAK,MAAM,CAAC;AAAA,gBAC5B;AACA,uBAAO,OAAO,KAAK,EAAE;AAAA,cACzB;AAAA,YACJ;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASP,SAAQC,UAASF,sBAAqB;AAMrD,gBAAII,QAAOJ,qBAAoB,CAAC;AAEhC,YAAAC,QAAO,UAAU;AAAA;AAAA,cAEhB,YAAY,SAAS,MAAM;AAC1B,wBAAQ,OAAO,IAAI,OAAO,CAAC,EAAE,YAAY,KAAK,OAAO,IAAI,OAAO,CAAC;AAAA,cAClE;AAAA;AAAA,cAEA,OAAO,SAAS,KAAK;AACpB,wBAAQ,MAAM,IAAI,YAAY;AAAA,cAC/B;AAAA;AAAA,cAEA,OAAO,SAAS,KAAK;AACpB,wBAAQ,MAAM,IAAI,YAAY;AAAA,cAC/B;AAAA;AAAA,cAEA,MAAM,SAAS,KAAK,KAAK,KAAK,KAAK;AAElC,oBAAI,CAACG,MAAK,QAAQ,GAAG,GAAG;AACvB,wBAAM,CAAC,EAAE,MAAM,KAAK,SAAS;AAC7B,wBAAM;AACN,wBAAM;AAAA,gBACP,OAAO;AAEN,sBAAI,QAAQ,OAAW,OAAM;AAG7B,sBAAI,QAAQ,OAAW,OAAM;AAAA,gBAC9B;AAEA,oBAAI,QAAQ,KAAK,QAAQ,EAAG,QAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,SAAS,CAAC,CAAC;AAGtE,uBAAO,KAAK,QAAQ,KAAK,KAAK,GAAG;AAAA,cAelC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAWA,SAAS,SAAS,QAAQ,KAAK,KAAK,KAAK;AACxC,sBAAM,OAAO,CAAC;AACd,oBAAI,MAAM,IAAI,MAAM,CAAC,GACpB,SAAS,CAAC,GACV,QAAQ,GACR,SAAS,IAAI;AACd,yBAASI,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAChC,0BAAQ,KAAK,QAAQ,GAAG,IAAI,SAAS,CAAC;AACtC,yBAAO,KAAK,IAAI,KAAK,CAAC;AACtB,sBAAI,OAAO,OAAO,CAAC;AAAA,gBACpB;AACA,wBAAQ,UAAU,QAAQ;AAAA,kBACzB,KAAK;AAAA,kBACL,KAAK;AACJ,2BAAO;AAAA,kBACR,KAAK;AACJ,0BAAM;AAAA;AAAA,kBAEP,KAAK;AACJ,0BAAM,SAAS,KAAK,EAAE;AACtB,0BAAM,SAAS,KAAK,EAAE;AACtB,2BAAO,OAAO,MAAM,GAAG,KAAK,QAAQ,KAAK,GAAG,CAAC;AAAA,gBAC/C;AAAA,cACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAWA,OAAO,SAAS,MAAM,OAAO;AAC5B,sBAAM,QAAQ,MAAM,SAAS,CAAC;AAE9B,oBAAI,UAAU,SAAS,EAAG,SAAQ,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AAG5D,oBAAID,WAAU,MAAM;AACpB,oBAAI,eAAeA,SAAQ,QAAQ,aAAa,KAAK,GAAG;AAExD,oBAAI,QACH,MAAM,MAAM,YAAY,IAAI,MAAM,MAAM,YAAY,KAAK;AAAA,kBACxD,OAAO;AAAA,kBACP;AAAA,gBACD;AAGD,uBAAO,MAAM,MAAM,MAAM,UAAU,MAAM,MAAM,MAAM;AAAA,cACtD;AAAA,YACD;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASN,SAAQC,UAAS;AAOhC,YAAAD,QAAO,UAAU;AAAA;AAAA,cAEhB,OAAO,WAAW;AACjB,oBAAI,QAAQ;AAAA;AAAA,kBAEX;AAAA,kBAAS;AAAA,kBAAQ;AAAA,kBAAU;AAAA,kBAAW;AAAA,kBACtC;AAAA,kBAAS;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAU;AAAA,kBACzC;AAAA,kBAAe;AAAA,kBAAU;AAAA,kBAAQ;AAAA,kBAAQ;AAAA,kBACzC;AAAA,kBAAU;AAAA,kBAAW;AAAA,kBAAU;AAAA,kBAAU;AAAA,kBACzC;AAAA,kBAAU;AAAA,kBAAW;AAAA,kBAAS;AAAA,kBAAS;AAAA,kBACvC;AAAA,kBAAQ;AAAA,kBAAW;AAAA,kBAAQ;AAAA,kBAAS;AAAA,kBACpC;AAAA,kBAAS;AAAA,kBAAS;AAAA,gBACnB,EAAE,OAAO;AAAA;AAAA,kBAER;AAAA,kBAAQ;AAAA,kBAAY;AAAA,kBAAS;AAAA,kBAAW;AAAA,kBACxC;AAAA,kBAAY;AAAA,kBAAS;AAAA,kBAAS;AAAA,kBAAY;AAAA,kBAC1C;AAAA,kBAAQ;AAAA,kBAAS;AAAA,kBAAS;AAAA,kBAAS;AAAA,kBACnC;AAAA,kBAAU;AAAA,kBAAS;AAAA,kBAAS;AAAA,kBAAQ;AAAA,kBACpC;AAAA,kBAAY;AAAA,kBAAS;AAAA,kBAAS;AAAA,kBAAY;AAAA,kBAC1C;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAW;AAAA,kBAAU;AAAA,kBAC3C;AAAA,kBAAU;AAAA,kBAAO;AAAA,gBAClB,CAAC;AACD,uBAAO,KAAK,KAAK,KAAK;AAAA,cAEvB;AAAA;AAAA,cAEA,MAAM,WAAW;AAChB,oBAAI,QAAQ;AAAA,kBACX;AAAA,kBAAS;AAAA,kBAAW;AAAA,kBAAY;AAAA,kBAAS;AAAA,kBACzC;AAAA,kBAAU;AAAA,kBAAS;AAAA,kBAAU;AAAA,kBAAa;AAAA,kBAC1C;AAAA,kBAAY;AAAA,kBAAY;AAAA,kBAAU;AAAA,kBAAU;AAAA,kBAC5C;AAAA,kBAAS;AAAA,kBAAU;AAAA,kBAAW;AAAA,kBAAY;AAAA,kBAC1C;AAAA,kBAAS;AAAA,kBAAO;AAAA,kBAAY;AAAA,kBAAU;AAAA,kBACtC;AAAA,kBAAS;AAAA,kBAAY;AAAA,kBAAU;AAAA,kBAAS;AAAA,kBACxC;AAAA,kBAAS;AAAA,gBACV;AACA,uBAAO,KAAK,KAAK,KAAK;AAAA,cAEvB;AAAA;AAAA,cAEA,MAAM,SAAS,QAAQ;AACtB,uBAAO,KAAK,MAAM,IAAI,OACpB,SAAS,KAAK,MAAM,IAAI,MAAM,MAC/B,KAAK,KAAK;AAAA,cACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMA,QAAQ,WAAW;AAClB,oBAAI,QACH,0MAUC,MAAM,GAAG;AACX,uBAAO,KAAK,KAAK,KAAK;AAAA,cACvB;AAAA;AAAA;AAAA;AAAA;AAAA,cAKA,OAAO,WAAW;AACjB,oBAAI,QACH,mDAGC,MAAM,GAAG;AACX,uBAAO,KAAK,KAAK,KAAK;AAAA,cACvB;AAAA;AAAA,cAEA,OAAO,WAAW;AACjB,uBAAO,KAAK,OAAO,IAAI,KAAK,MAAM;AAAA,cACnC;AAAA,YACD;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAAS;AAKhC,YAAAD,QAAO,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAiBb,KAAK,SAAS,UAAU,MAAM;AAC1B,wBAAQ,YAAY,KAAK,SAAS,KAAK;AAAA,iBAClC,QAAQ,KAAK,OAAO;AAAA,gBACrB,MAAM,KAAK,KAAK;AAAA,cACxB;AAAA;AAAA,cAEA,UAAU,WAAW;AACjB,uBAAO,KAAK;AAAA;AAAA,kBAER,8EAA8E,MAAM,GAAG;AAAA,gBAC3F;AAAA,cACJ;AAAA;AAAA,cAEA,QAAQ,SAAS,KAAK;AAClB,uBAAO,KAAK,KAAK,IAAI,OAAO,OAAO,KAAK,IAAI;AAAA,cAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOA,KAAK,WAAW;AACZ,uBAAO,KAAK;AAAA;AAAA,kBAGJ,+0BASF,MAAM,GAAG;AAAA,gBACf;AAAA,cACJ;AAAA;AAAA,cAEA,OAAO,SAAS,QAAQ;AACpB,uBAAO,KAAK,UAAU,OAAO,IAAI,MAAM,KAAK,KAAK,IAAI,OAE7C,UACC,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI;AAAA,cAI1C;AAAA;AAAA,cAEA,IAAI,WAAW;AACX,uBAAO,KAAK,QAAQ,GAAG,GAAG,IAAI,MAC1B,KAAK,QAAQ,GAAG,GAAG,IAAI,MACvB,KAAK,QAAQ,GAAG,GAAG,IAAI,MACvB,KAAK,QAAQ,GAAG,GAAG;AAAA,cAC3B;AAAA,YACJ;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAASF,sBAAqB;AAMrD,gBAAI,OAAOA,qBAAoB,EAAE;AACjC,gBAAI,SAAS,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAEtD,YAAAC,QAAO,UAAU;AAAA;AAAA,cAEb,QAAQ,WAAW;AACf,uBAAO,KAAK,KAAK,MAAM;AAAA,cAC3B;AAAA;AAAA,cAEA,UAAU,WAAW;AACjB,uBAAO,KAAK,KAAK,IAAI,EAAE;AAAA,cAC3B;AAAA;AAAA,cAEA,MAAM,SAAS,QAAQ;AACnB,oBAAI,WAAW,KAAK,KAAK,IAAI;AAC7B,oBAAI,OAAO,KAAK,KAAK,SAAS,QAAQ;AACtC,uBAAO,SAAS,CAAC,SAAS,MAAM,KAAK,IAAI,EAAE,KAAK,GAAG,IAAI,KAAK;AAAA,cAChE;AAAA;AAAA,cAEA,QAAQ,SAAS,QAAQ;AACrB,oBAAI,WAAW,KAAK,KAAK,IAAI;AAC7B,oBAAI,OAAO,KAAK,KAAK,SAAS,QAAQ;AACtC,oBAAI,SAAS,KAAK,KAAK,KAAK,QAAQ,KAAK;AAAA,kBACrC,MAAM;AAAA,gBACV;AACA,uBAAO,SAAS,CAAC,SAAS,MAAM,KAAK,MAAM,OAAO,IAAI,EAAE,KAAK,GAAG,IAAI,OAAO;AAAA,cAC/E;AAAA;AAAA,cAEA,KAAK,SAAS,KAAK;AACf,oBAAI,MAAM;AACV,yBAASO,KAAI,GAAGA,MAAK,OAAO,IAAIA,KAAK,QAAO,KAAK,QAAQ,GAAG,CAAC;AAC7D,uBAAO;AAAA,cACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAUJ;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASP,SAAQC,UAAS;AA4BhC,gBAAI,OAAO;AAAA,cACP,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,YACd;AAGA,qBAAS,KAAK,MAAM;AAChB,kBAAI,SAAS,CAAC;AACd,uBAASM,KAAI,GAAG,MAAMA,KAAI,KAAK,QAAQA,MAAK;AACxC,uBAAO,KAAKA,EAAC;AACb,oBAAI,CAAC,QAAQ,CAAC,KAAK,GAAI;AACvB,uBAAO,KAAK,EAAE,IAAI;AAAA,cACtB;AAEA,kBAAI,SAAS,CAAC;AACd,uBAAS,KAAK,GAAG,KAAK,KAAK,QAAQ,MAAM;AACrC,uBAAO,KAAK,EAAE;AAEd,oBAAI,CAAC,KAAM;AAEX,oBAAI,KAAK,OAAO,UAAa,KAAK,YAAY,QAAW;AACrD,yBAAO,KAAK,IAAI;AAChB;AAAA,gBACJ;AACA,oBAAI,SAAS,OAAO,KAAK,GAAG,KAAK,OAAO,KAAK,QAAQ;AACrD,oBAAI,CAAC,OAAQ;AACb,oBAAI,CAAC,OAAO,SAAU,QAAO,WAAW,CAAC;AACzC,uBAAO,SAAS,KAAK,IAAI;AAAA,cAC7B;AACA,qBAAO;AAAA,YACX;AAEA,gBAAI,aAAa,WAAW;AACxB,kBAAI,QAAQ,CAAC;AACb,uBAAS,MAAM,MAAM;AACjB,oBAAI,MAAM,GAAG,MAAM,GAAG,CAAC,MAAM,SAAS,SAClC,GAAG,MAAM,GAAG,CAAC,KAAK,OAAQ,GAAG,MAAM,GAAG,CAAC,IAAI,SAC3C,GAAG,MAAM,GAAG,CAAC,IAAI;AACrB,sBAAM,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA,MAAM,KAAK,EAAE;AAAA,gBACjB,CAAC;AAAA,cACL;AACA,qBAAO,KAAK,KAAK;AAAA,YACrB,EAAE;AAEF,YAAAP,QAAO,UAAU;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAASF,sBAAqB;AAKrD,gBAAI,OAAOA,qBAAoB,EAAE;AACjC,YAAAC,QAAO,UAAU;AAAA;AAAA,cAEhB,IAAI,WAAW;AACd,uBAAO,KAAK,QAAQ,GAAG,CAAC;AAAA,cACzB;AAAA,cACA,IAAI,WAAW;AACd,uBAAO,KAAK,QAAQ,GAAG,CAAC;AAAA,cACzB;AAAA,cACA,IAAI,WAAW;AACd,uBAAO,KAAK,QAAQ,GAAG,CAAC;AAAA,cACzB;AAAA,cACA,KAAK,WAAW;AACf,uBAAO,KAAK,QAAQ,GAAG,EAAE;AAAA,cAC1B;AAAA,cACA,KAAK,WAAW;AACf,uBAAO,KAAK,QAAQ,GAAG,EAAE;AAAA,cAC1B;AAAA,cACA,MAAM,WAAW;AAChB,uBAAO,KAAK,QAAQ,GAAG,GAAG;AAAA,cAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cA2BA,MAAM,WAAW;AAChB,oBAAI,OAAO,0BACV,OAAO,KAAK,OAAO,MAAM,CAAC,IAAI,MAC9B,KAAK,OAAO,MAAM,CAAC,IAAI,MACvB,KAAK,OAAO,MAAM,CAAC,IAAI,MACvB,KAAK,OAAO,MAAM,CAAC,IAAI,MACvB,KAAK,OAAO,MAAM,EAAE;AACrB,uBAAO;AAAA,cACR;AAAA,cACA,MAAM,WAAW;AAChB,uBAAO,KAAK,KAAK;AAAA,cAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAQA,IAAI,WAAW;AACd,oBAAI,IACH,MAAM,GACN,OAAO;AAAA,kBACN;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAM;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAM;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,gBACnF,GACA,OAAO;AAAA,kBACN;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,kBAAK;AAAA,gBACnD;AAED,qBAAK,KAAK,KAAK,IAAI,EAAE,KACpB,KAAK,KAAK,UAAU,IACpB,KAAK,OAAO,UAAU,CAAC;AAExB,yBAASO,KAAI,GAAGA,KAAI,GAAG,QAAQA,MAAK;AACnC,yBAAO,GAAGA,EAAC,IAAI,KAAKA,EAAC;AAAA,gBACtB;AACA,sBAAM,KAAK,MAAM,EAAE;AAEnB,uBAAO;AAAA,cACR;AAAA;AAAA;AAAA;AAAA;AAAA,cAMA,WAAW,2BAAW;AACrB,oBAAIC,OAAM;AACV,uBAAO,SAAS,MAAM;AACrB,yBAAOA,QAAQ,CAAC,QAAQ;AAAA,gBACzB;AAAA,cACD,EAAE;AAAA,cACF,KAAK,SAAS,MAAM;AACnB,uBAAO,KAAK,UAAU,IAAI;AAAA,cAC3B;AAAA,YACD;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASR,SAAQC,UAASF,sBAAqB;AAErD,gBAAIe,UAASf,qBAAoB,EAAE;AACnC,gBAAIG,WAAUH,qBAAoB,EAAE;AACpC,YAAAC,QAAO,UAAU;AAAA,cAChB,QAAQc;AAAA,cACR,SAASZ;AAAA,YACV;AAAA,UAEK;AAAA;AAAA;AAAA,UAEC,SAASF,SAAQC,UAAS;AAKhC,qBAAS,MAAM,GAAG;AACd,kBAAI,YAAY,OAAO,GAAG;AACtB,oBAAI,IAAI,IAAI,UAAU,sDAAsD;AAC5E,sBAAM;AAAA,cACV;AACA,qBAAO,QAAQ,GAAG,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC;AAAA,YAC9C;AAEA,qBAAS,MAAM,GAAG;AACd,mBAAK,OAAO,GAAG,KAAK,SAAS,MAAM,OAAO,GAAG,KAAK,OAAO,MAAM,KAAK;AAAA,YACxE;AAEA,qBAAS,UAAU,GAAG,GAAG;AACrB,oBAAM,KAAK,MAAM,WAAW,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ;AAAA,YAC/D;AAEA,qBAAS,MAAM,GAAG;AACd,oBAAM,KAAK,MAAM,OAAO,GAAG,KAAK,OAAO,EAAE,OAAO,OAAO;AAAA,YAC3D;AAEA,qBAAS,MAAM,GAAG,GAAG;AACjB,oBAAM,KAAK,MAAM,CAAC,GAAG,KAAK,OAAO;AAAA,YACrC;AAEA,qBAAS,aAAa,GAAG;AACrB,oBAAM,KAAK,MAAM,eAAe,GAAG,KAAK,QAAQ,IAAI,KAAK,MAAM,MAAM,IAAI,KAAK,MAAM,IAAI,UACxF,KAAK,OAAO;AAAA,YAChB;AAEA,qBAAS,WAAW,GAAG,GAAG;AACtB,oBAAM,KAAK,MAAM,YAAY,GAAG,KAAK,OAAO,GAAG,KAAK,aAAa;AAAA,YACrE;AAEA,qBAAS,WAAW,GAAG,GAAG;AACtB,oBAAM,KAAK,MAAM,YAAY,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,SAAS;AAAA,YAC9E;AAEA,qBAAS,QAAQ,GAAG,GAAG;AACnB,oBAAM,KAAK,MAAM,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,OAAO;AAAA,YAC9D;AAEA,qBAAS,eAAe,GAAG,GAAG;AAC1B,oBAAM,KAAK,MAAM,OAAO,GAAG,KAAK,QAAQ,GAAG,KAAK,MAAM;AAAA,YAC1D;AAEA,qBAAS,QAAQ,GAAG;AAChB,oBAAM,KAAK,MAAM,SAAS,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU,KAAK,QAAQ,KAAK;AAAA,YACjF;AAEA,qBAAS,QAAQ,GAAG;AAChB,oBAAM,KAAK,MAAM,SAAS,GAAG,KAAK,OAAO,EAAE,YAAY;AAAA,YAC3D;AAEA,qBAAS,IAAI,GAAG;AACZ,oBAAM,KAAK,MAAM,KAAK,GAAG,KAAK,OAAO,EAAE,YAAY;AAAA,YACvD;AAEA,qBAAS,MAAM,GAAG;AACd,oBAAM,KAAK,MAAM,OAAO,GAAG,KAAK,OAAO,EAAE,YAAY;AAAA,YACzD;AAEA,qBAAS,cAAc,GAAG;AACtB,oBAAM,KAAK,MAAM,gBAAgB,GAAG,KAAK,OAAO,EAAE,YAAY;AAAA,YAClE;AAEA,qBAAS,iBAAiB,GAAG;AACzB,oBAAM,KAAK,MAAM,mBAAmB,GAAG,KAAK,OAAO,EAAE,YAAY;AAAA,YACrE;AAEA,gBAAI,SAAS,WAAW;AACpB,uBAAS,EAAEc,IAAGC,IAAG;AACb,yBAASC,KAAI;AACT,uBAAK,cAAcF;AAAA,gBACvB;AACA,gBAAAE,GAAE,YAAYD,GAAE,WAAWD,GAAE,YAAY,IAAIE,GAAE;AAAA,cACnD;AACA,uBAAS,EAAEF,IAAGC,IAAGC,IAAG,GAAG,GAAG;AACtB,yBAAS,EAAEF,IAAGC,IAAG;AACb,2BAASC,GAAEF,IAAG;AACV,6BAASC,GAAED,IAAG;AACV,6BAAOA,GAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AAAA,oBACpD;AACA,2BAAOA,GAAE,QAAQ,OAAO,MAAM,EAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,SAAS,KAAK,EAAE,QAAQ,OAAO,KAAK,EAAE,QAAQ,OAAO,KAAK,EAAE,QAAQ,OAAO,KAAK,EAAE,QAAQ,OAAO,KAAK,EAAE,QAAQ,4BAA4B,SAASA,IAAG;AACjN,6BAAO,SAASC,GAAED,EAAC;AAAA,oBACvB,CAAC,EAAE,QAAQ,yBAAyB,SAASA,IAAG;AAC5C,6BAAO,QAAQC,GAAED,EAAC;AAAA,oBACtB,CAAC,EAAE,QAAQ,oBAAoB,SAASA,IAAG;AACvC,6BAAO,SAASC,GAAED,EAAC;AAAA,oBACvB,CAAC,EAAE,QAAQ,oBAAoB,SAASA,IAAG;AACvC,6BAAO,QAAQC,GAAED,EAAC;AAAA,oBACtB,CAAC;AAAA,kBACL;AACA,sBAAIG,IAAGC;AACP,0BAAQJ,GAAE,QAAQ;AAAA,oBAChB,KAAK;AACH,sBAAAG,KAAI;AACJ;AAAA,oBAEF,KAAK;AACH,sBAAAA,KAAIH,GAAE,CAAC;AACP;AAAA,oBAEF;AACE,sBAAAG,KAAIH,GAAE,MAAM,GAAG,EAAE,EAAE,KAAK,IAAI,IAAI,SAASA,GAAEA,GAAE,SAAS,CAAC;AAAA,kBAC3D;AACA,yBAAOI,KAAIH,KAAI,MAAMC,GAAED,EAAC,IAAI,MAAM,gBAAgB,cAAcE,KAAI,UAAUC,KAAI;AAAA,gBACtF;AACA,qBAAK,WAAWJ,IAAG,KAAK,QAAQC,IAAG,KAAK,SAASC,IAAG,KAAK,OAAO,GAAG,KAAK,SAAS,GACjF,KAAK,OAAO,eAAe,KAAK,UAAU,EAAEF,IAAGC,EAAC;AAAA,cACpD;AACA,uBAAS,EAAED,IAAG;AACV,yBAASE,KAAI;AACT,yBAAOF,GAAE,UAAU,IAAI,EAAE;AAAA,gBAC7B;AACA,yBAAS,IAAI;AACT,yBAAO;AAAA,gBACX;AACA,yBAAS,EAAEC,IAAG;AACV,2BAASC,GAAED,IAAGC,IAAGC,IAAG;AAChB,wBAAIC,IAAGC;AACP,yBAAKD,KAAIF,IAAGC,KAAIC,IAAGA,KAAK,CAAAC,KAAIL,GAAE,OAAOI,EAAC,GAAG,SAASC,MAAKJ,GAAE,UAAUA,GAAE,QAAQA,GAAE,SAAS,GACxFA,GAAE,SAAS,SAAM,SAASI,MAAK,aAAaA,MAAK,aAAaA,MAAKJ,GAAE,QAAQA,GAAE,SAAS,GACxFA,GAAE,SAAS,SAAOA,GAAE,UAAUA,GAAE,SAAS;AAAA,kBAC7C;AACA,yBAAO,OAAOA,OAAM,KAAKA,OAAM,KAAK,GAAG,KAAK;AAAA,oBACxC,MAAM;AAAA,oBACN,QAAQ;AAAA,oBACR,QAAQ;AAAA,kBACZ,IAAIC,GAAE,IAAI,IAAID,EAAC,GAAG,KAAKA,KAAI;AAAA,gBAC/B;AACA,yBAAS,EAAED,IAAG;AACV,uBAAK,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG,KAAKA,EAAC;AAAA,gBACxD;AACA,yBAAS,EAAEA,IAAG;AACV,sBAAIC,KAAI;AACR,uBAAKD,GAAE,KAAK,GAAGC,KAAID,GAAE,SAAU,CAAAA,GAAEC,KAAI,CAAC,MAAMD,GAAEC,EAAC,IAAID,GAAE,OAAOC,IAAG,CAAC,IAAIA;AAAA,gBACxE;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC,IAAGC,IAAGE;AAChB,yBAAOL,KAAI,IAAIC,KAAIV,GAAE,GAAG,SAASU,MAAKC,KAAI,IAAI,QAAQH,GAAE,WAAW,EAAE,KAAKI,KAAI,IAC9E,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,MAAKE,KAAI,EAAE,GAAG,SAASA,MAAKF,KAAI,CAAEA,IAAGE,EAAE,GACvFH,KAAIC,OAAM,KAAKD,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAK,SAASA,OAAMA,KAAI,KAAK,SAASA,MAAK,KAAKF,IAC1FC,KAAI,GAAGA,IAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAAGA,KAAI,QAAQ,KAAKA,IAChFA,KAAI,KAAKA;AAAA,gBACb;AACA,yBAAST,KAAI;AACT,sBAAIQ,IAAGC,IAAGC,IAAGC,IAAGC;AAChB,sBAAIJ,KAAI,IAAIC,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,KAAK,SAASA,GAAG,KAAIC,KAAI,IAAI,MAAMC,KAAI,EAAE,GACjF,MAAM,SAASA,KAAID,KAAI,MAAM,KAAKA,IAAGA,KAAI,KAAK,SAASA,IAAG;AACtD,yBAAKC,KAAI,CAAC,GAAGC,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,IAAI,SAASA,KAAK,CAAAD,GAAE,KAAKC,EAAC,GAAGA,KAAI,EAAE,GAC9E,SAASA,OAAMA,KAAI,EAAE;AACrB,6BAASD,MAAKC,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,KAAK,SAASA,MAAK,KAAKJ,IAAGC,KAAI,GAAGA,IAAGE,IAAGC,EAAC,GACnF,SAASH,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI;AAAA,kBAC7E,MAAO,MAAKA,IAAGA,KAAI;AAAA,sBAAS,MAAKA,IAAGA,KAAI;AACxC,yBAAOA;AAAA,gBACX;AACA,yBAAS,IAAI;AACT,sBAAIA;AACJ,yBAAOA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,KAAKA;AAAA,gBACtE;AACA,yBAAS,IAAI;AACT,sBAAIC,IAAGC;AACP,yBAAOD,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAID,IAAGC,IAAGC;AACV,yBAAOF,KAAI,IAAIC,KAAI,EAAE,GAAG,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGA,IAAGC,EAAC,GAChF,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBAClF;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC;AACV,yBAAO,MAAMF,KAAI,IAAIC,KAAI,EAAE,GAAG,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,KAAK,SAASA,MAAK,KAAKF,IAChGC,KAAI,GAAGA,IAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAAGA,KAAI,QAAQ,KAAKA,IAChFA,KAAI,KAAK,MAAM,SAASA,OAAMC,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAAID;AAAA,gBAChE;AACA,yBAAS,IAAI;AACT,sBAAIA;AACJ,yBAAOA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GACrF,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,QAAQA;AAAA,gBACzD;AACA,yBAAS,IAAI;AACT,sBAAIC,IAAGC,IAAGC,IAAGC,IAAGE,IAAGC;AACnB,yBAAON,KAAI,IAAI,QAAQD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACtF,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAK,OAAOH,GAAE,WAAW,EAAE,KAAKI,KAAI,IAAI,SAASA,KAAI,MACrF,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,MAAKE,KAAI,EAAE,GAAG,SAASA,MAAK,QAAQN,GAAE,WAAW,EAAE,KAAKO,KAAI,IACzF,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,MAAK,KAAKN,IAAGC,KAAI,GAAGC,IAAGG,EAAC,GAAG,SAASJ,MAAK,KAAKD,IAC9FA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,QAAQ,KAAKA,IACpFA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACjC;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC,IAAGC;AACb,yBAAOH,KAAI,IAAI,QAAQD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACtF,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAKH,GAAE,OAAO,IAAI,CAAC,MAAM,MAAMI,KAAI,IAAI,MAAM,MAAMA,KAAI,MACvF,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,MAAK,KAAKH,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IACnGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACrD;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC,IAAGC;AACb,yBAAOH,KAAI,IAAI,QAAQD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACtF,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAK,QAAQH,GAAE,WAAW,EAAE,KAAKI,KAAI,IAAI,SAASA,KAAI,MACtF,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,MAAK,KAAKH,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IACnGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACrD;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA;AACJ,yBAAO,OAAOD,GAAE,WAAW,EAAE,KAAKC,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAC7EA;AAAA,gBACJ;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC;AACV,sBAAIF,KAAI,IAAIC,KAAI,CAAC,GAAG,GAAG,KAAKF,GAAE,OAAO,EAAE,CAAC,KAAKG,KAAIH,GAAE,OAAO,EAAE,GAAG,SAASG,KAAI,MAC5E,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,GAAG,QAAM,SAASA,KAAK,CAAAD,GAAE,KAAKC,EAAC,GAAG,GAAG,KAAKH,GAAE,OAAO,EAAE,CAAC,KAAKG,KAAIH,GAAE,OAAO,EAAE,GACvG,SAASG,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE;AAAA,sBAAS,CAAAD,KAAI;AAChD,yBAAO,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAGA,EAAC,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAC7ED;AAAA,gBACJ;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC,IAAGC;AACb,yBAAOH,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,MAC5F,SAASA,MAAK,OAAOH,GAAE,WAAW,EAAE,KAAKI,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACpF,SAASA,MAAK,KAAKH,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAC/EA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACrD;AACA,yBAAS,IAAI;AACT,sBAAID,IAAGC;AACP,yBAAOD,KAAI,IAAIC,KAAI,EAAE,GAAG,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAGA,EAAC,IAAI,SAASA,MAAK,KAAKD,IAC9EA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBACpB;AACA,yBAAS,IAAI;AACT,sBAAIC,IAAGC,IAAGC;AACV,yBAAOF,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IACtGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACjC;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC;AACV,yBAAOF,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IACtGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACjC;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC;AACV,yBAAOF,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IACtGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACjC;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC,IAAGC,IAAGE;AAChB,sBAAI,MAAML,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACxF,SAASA,GAAG,KAAI,OAAOF,GAAE,WAAW,EAAE,KAAKG,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACtF,SAASA,OAAMA,KAAI,KAAK,SAASA,IAAG;AAChC,yBAAKC,KAAI,CAAC,GAAGE,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,IAAI,SAASA,KAAK,CAAAF,GAAE,KAAKE,EAAC,GAAGA,KAAI,EAAE,GAC9E,SAASA,OAAMA,KAAI,EAAE;AACrB,6BAASF,MAAK,OAAOJ,GAAE,WAAW,EAAE,KAAKM,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACpF,SAASA,MAAK,KAAKL,IAAGC,KAAI,GAAGC,IAAGC,EAAC,GAAG,SAASF,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAClFA,KAAI,QAAQ,KAAKA,IAAGA,KAAI;AAAA,kBAC5B,MAAO,MAAKA,IAAGA,KAAI;AAAA,sBAAS,MAAKA,IAAGA,KAAI;AACxC,yBAAO,MAAM,SAASA,OAAMC,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAAID;AAAA,gBAC9D;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC,IAAGC;AACb,yBAAO,MAAMH,KAAI,IAAIC,KAAI,EAAE,GAAG,SAASA,MAAK,OAAOF,GAAE,WAAW,EAAE,KAAKG,KAAI,IAAI,SAASA,KAAI,MAC5F,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,MAAKC,KAAI,EAAE,GAAG,SAASA,MAAK,KAAKH,IAAGC,KAAI,GAAGA,IAAGE,EAAC,GAAG,SAASF,MAAK,KAAKD,IAClGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAK,MAC5E,SAASA,OAAMC,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAAID;AAAA,gBACjD;AACA,yBAAS,IAAI;AACT,sBAAID,IAAGC;AACP,yBAAO,MAAMD,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,IAAI,MAAM,SAASA,OAAMC,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAC9FD;AAAA,gBACJ;AACA,yBAAS,IAAI;AACT,sBAAIC,IAAGC;AACP,yBAAOD,KAAI,IAAI,GAAG,KAAKD,GAAE,OAAO,EAAE,CAAC,KAAKE,KAAIF,GAAE,OAAO,EAAE,GAAG,SAASE,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAC7F,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAGA,EAAC,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC7E;AACA,yBAAS,IAAI;AACT,sBAAID;AACJ,yBAAOA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GACrF,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAC7F,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAC7F,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,GAAG,GAAG,SAASA,OAAMA,KAAI,GAAG,GAC/F,SAASA,OAAMA,KAAI,GAAG,GAAG,SAASA,OAAMA,KAAI,GAAG,oBAAoBA;AAAA,gBACvE;AACA,yBAAS,IAAI;AACT,sBAAIA;AACJ,yBAAOA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,KAAKA;AAAA,gBACtE;AACA,yBAAS,IAAI;AACT,sBAAIC,IAAGC;AACP,yBAAOD,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAO,MAAMD,KAAI,IAAI,GAAG,KAAKD,GAAE,OAAO,EAAE,CAAC,KAAKE,KAAIF,GAAE,OAAO,EAAE,GAAG,SAASE,KAAI,MAC7E,MAAM,MAAM,EAAE,EAAE,IAAI,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAGA,EAAC,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAC1F,MAAM,SAASD,OAAMC,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAAID;AAAA,gBACvD;AACA,yBAAS,IAAI;AACT,sBAAID;AACJ,yBAAOA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GACrF,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAC7F,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAC7F,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAAG,SAASA,OAAMA,KAAI,EAAE,GAC7F,SAASA,OAAMA,KAAI,GAAG,GAAG,SAASA,OAAMA,KAAI,GAAG,GAAG,SAASA,OAAMA,KAAI,GAAG,GAAG,SAASA,OAAMA,KAAI,GAAG,sBACjGA;AAAA,gBACJ;AACA,yBAAS,IAAI;AACT,sBAAIC,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC;AACV,yBAAOF,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,MAAKF,GAAE,SAAS,MAAMG,KAAIH,GAAE,OAAO,EAAE,GAAG,SAASG,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACpF,SAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAC/EA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACjC;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC;AACV,yBAAOF,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,MAAK,GAAG,KAAKF,GAAE,OAAO,EAAE,CAAC,KAAKG,KAAIH,GAAE,OAAO,EAAE,GAAG,SAASG,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAC5F,SAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAC/EA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACjC;AACA,yBAAS,IAAI;AACT,sBAAIA,IAAGC,IAAGC,IAAGC;AACb,sBAAIH,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACpF,SAASA,IAAG;AACR,wBAAIC,KAAI,CAAC,GAAG,GAAG,KAAKH,GAAE,OAAO,EAAE,CAAC,KAAKI,KAAIJ,GAAE,OAAO,EAAE,GAAG,SAASI,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAC1F,SAASA,GAAG,QAAM,SAASA,KAAK,CAAAD,GAAE,KAAKC,EAAC,GAAG,GAAG,KAAKJ,GAAE,OAAO,EAAE,CAAC,KAAKI,KAAIJ,GAAE,OAAO,EAAE,GACnF,SAASI,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE;AAAA,wBAAS,CAAAD,KAAI;AAChD,6BAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAC/EA,KAAI;AAAA,kBACR,MAAO,MAAKA,IAAGA,KAAI;AACnB,yBAAOA;AAAA,gBACX;AACA,yBAAS,KAAK;AACV,sBAAIA,IAAGC,IAAGC,IAAGC;AACb,sBAAIH,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACpF,SAASA,IAAG;AACR,wBAAIC,KAAI,CAAC,GAAG,GAAG,KAAKH,GAAE,OAAO,EAAE,CAAC,KAAKI,KAAIJ,GAAE,OAAO,EAAE,GAAG,SAASI,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAC1F,SAASA,GAAG,QAAM,SAASA,KAAK,CAAAD,GAAE,KAAKC,EAAC,GAAG,GAAG,KAAKJ,GAAE,OAAO,EAAE,CAAC,KAAKI,KAAIJ,GAAE,OAAO,EAAE,GACnF,SAASI,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE;AAAA,wBAAS,CAAAD,KAAI;AAChD,6BAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAC/EA,KAAI;AAAA,kBACR,MAAO,MAAKA,IAAGA,KAAI;AACnB,yBAAOA;AAAA,gBACX;AACA,yBAAS,KAAK;AACV,sBAAIA,IAAGC,IAAGC,IAAGC;AACb,sBAAIH,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACpF,SAASA,IAAG;AACR,wBAAIC,KAAI,CAAC,GAAG,GAAG,KAAKH,GAAE,OAAO,EAAE,CAAC,KAAKI,KAAIJ,GAAE,OAAO,EAAE,GAAG,SAASI,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IAC1F,SAASA,GAAG,QAAM,SAASA,KAAK,CAAAD,GAAE,KAAKC,EAAC,GAAG,GAAG,KAAKJ,GAAE,OAAO,EAAE,CAAC,KAAKI,KAAIJ,GAAE,OAAO,EAAE,GACnF,SAASI,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE;AAAA,wBAAS,CAAAD,KAAI;AAChD,6BAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAC/EA,KAAI;AAAA,kBACR,MAAO,MAAKA,IAAGA,KAAI;AACnB,yBAAOA;AAAA,gBACX;AACA,yBAAS,KAAK;AACV,sBAAIA,IAAGC;AACP,yBAAOD,KAAI,IAAID,GAAE,OAAO,IAAI,CAAC,MAAM,MAAME,KAAI,IAAI,MAAM,MAAMA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACvF,SAASA,OAAM,KAAKD,IAAGC,KAAI,GAAG,IAAI,SAASA,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,IAAGD;AAAA,gBAC5E;AACA,yBAAS,KAAK;AACV,sBAAIA,IAAGC,IAAGC;AACV,yBAAOF,KAAI,IAAI,OAAOD,GAAE,WAAW,EAAE,KAAKE,KAAI,IAAI,SAASA,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACrF,SAASA,MAAKF,GAAE,SAAS,MAAMG,KAAIH,GAAE,OAAO,EAAE,GAAG,SAASG,KAAI,MAAM,MAAM,MAAM,EAAE,EAAE,IACpF,SAASA,MAAK,KAAKF,IAAGC,KAAI,GAAGC,EAAC,GAAG,SAASD,MAAK,KAAKD,IAAGA,KAAIC,MAAKD,KAAIC,OAAM,KAAKD,IAC/EA,KAAI,QAAQ,KAAKA,IAAGA,KAAI,KAAKA;AAAA,gBACjC;AACA,oBAAI,IAAI,KAAK,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK;AAAA,kBACxD,QAAQ;AAAA,gBACZ,GAAG,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,KAAK,SAASD,IAAGC,IAAG;AACrE,yBAAOA,KAAI,IAAI,UAAUD,IAAGC,GAAE,CAAC,CAAC,IAAID;AAAA,gBACxC,GAAG,KAAK,SAASA,IAAGC,IAAGC,IAAG;AACtB,yBAAO,IAAI,MAAM,CAAEF,EAAE,EAAE,OAAOC,EAAC,EAAE,OAAO,CAAEC,EAAE,CAAC,CAAC;AAAA,gBAClD,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW;AACrC,yBAAO,IAAI,MAAM,OAAO;AAAA,gBAC5B,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW;AACrC,yBAAO,IAAI,MAAM,KAAK;AAAA,gBAC1B,GAAG,KAAK,SAASF,IAAGC,IAAG;AACnB,yBAAO,IAAI,WAAWD,IAAGC,EAAC;AAAA,gBAC9B,GAAG,KAAK,cAAc,KAAK,SAASD,IAAGC,IAAG;AACtC,yBAAOA,OAAMD,GAAE,SAAS,QAAKA;AAAA,gBACjC,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,SAASA,IAAGC,IAAG;AACrF,yBAAO,IAAI,WAAWD,IAAGC,EAAC;AAAA,gBAC9B,GAAG,KAAK,MAAM,KAAK,QAAQ,KAAK,SAASD,IAAG;AACxC,yBAAO,IAAI,WAAWA,IAAG,IAAE,CAAC;AAAA,gBAChC,GAAG,KAAK,SAASA,IAAG;AAChB,yBAAO,IAAI,WAAWA,IAAGA,EAAC;AAAA,gBAC9B,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW;AACrC,yBAAO,IAAI,WAAW,GAAG,IAAE,CAAC;AAAA,gBAChC,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW;AACrC,yBAAO,IAAI,WAAW,GAAG,IAAE,CAAC;AAAA,gBAChC,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW;AACrC,yBAAO,IAAI,WAAW,GAAG,CAAC;AAAA,gBAC9B,GAAG,KAAK,UAAU,KAAK,SAAS,KAAK,SAASA,IAAG;AAC7C,yBAAO,CAACA,GAAE,KAAK,EAAE;AAAA,gBACrB,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,SAASA,IAAG;AAC5D,yBAAOA;AAAA,gBACX,GAAG,KAAK,SAASA,IAAG;AAChB,yBAAO,IAAI,aAAaA,EAAC;AAAA,gBAC7B,GAAG,KAAK,MAAM,KAAK,QAAQ,KAAK,SAASA,IAAG;AACxC,yBAAO,IAAI,MAAM,qBAAqBA,EAAC;AAAA,gBAC3C,GAAG,KAAK,MAAM,KAAK,QAAQ,KAAK,SAASA,IAAG;AACxC,yBAAO,IAAI,MAAM,sBAAsBA,EAAC;AAAA,gBAC5C,GAAG,KAAK,MAAM,KAAK,QAAQ,KAAK,SAASA,IAAG;AACxC,yBAAO,IAAI,MAAM,sBAAsBA,EAAC;AAAA,gBAC5C,GAAG,KAAK,gBAAgB,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,SAASA,IAAGC,IAAG;AACpF,yBAAO,IAAI,QAAQ,CAAC,CAACD,IAAGC,EAAC;AAAA,gBAC7B,GAAG,KAAK,kBAAkB,KAAK,KAAK,KAAK,OAAO,KAAK,SAASD,IAAGC,IAAG;AAChE,yBAAO,IAAI,eAAeD,IAAGC,EAAC;AAAA,gBAClC,GAAG,KAAK,aAAa,KAAK,YAAY,KAAK,cAAc,KAAK,SAASD,IAAG;AACtE,yBAAO,IAAI,QAAQA,EAAC;AAAA,gBACxB,GAAG,KAAK,KAAK,KAAK,OAAO,KAAK,WAAW;AACrC,yBAAO,IAAI,MAAM,eAAe;AAAA,gBACpC,GAAG,KAAK,WAAW,KAAK,uBAAuB,KAAK,0BAA0B,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AACtH,yBAAO,IAAI,MAAM,WAAW;AAAA,gBAChC,GAAG,KAAK,WAAW;AACf,yBAAO,IAAI,MAAM,eAAe;AAAA,gBACpC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,mBAAmB;AAAA,gBACxC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,OAAO;AAAA,gBAC5B,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,WAAW;AAAA,gBAChC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,WAAW;AAAA,gBAChC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,WAAW;AAAA,gBAChC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,iBAAiB;AAAA,gBACtC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,aAAa;AAAA,gBAClC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,iBAAiB;AAAA,gBACtC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,KAAK;AAAA,gBAC1B,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,cAAc;AAAA,gBACnC,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,MAAM;AAAA,gBAC3B,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,WAAW;AAC3C,yBAAO,IAAI,MAAM,UAAU;AAAA,gBAC/B,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,iBAAiB,KAAK,SAASA,IAAG;AAClE,yBAAO,IAAI,iBAAiBA,EAAC;AAAA,gBACjC,GAAG,KAAK,MAAM,KAAK,UAAU,KAAK,UAAU,KAAK,SAAS,KAAK,SAASA,IAAG;AACvE,yBAAO,IAAI,cAAcA,EAAC;AAAA,gBAC9B,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,UAAU,KAAK,SAAS,KAAK,SAASA,IAAG;AACzE,yBAAO,IAAI,MAAMA,GAAE,KAAK,EAAE,CAAC;AAAA,gBAC/B,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,gBAAgB,KAAK,eAAe,KAAK,SAASA,IAAG;AACrF,yBAAO,IAAI,IAAIA,GAAE,KAAK,EAAE,CAAC;AAAA,gBAC7B,GAAG,KAAK,OAAO,KAAK,WAAW,KAAK,SAASA,IAAG;AAC5C,yBAAO,IAAI,QAAQA,GAAE,KAAK,EAAE,CAAC;AAAA,gBACjC,GAAG,KAAK,WAAW;AACf,yBAAO,IAAI,MAAM,gBAAgB;AAAA,gBACrC,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AAAA,kBAC5B,MAAM;AAAA,kBACN,QAAQ;AAAA,kBACR,QAAQ;AAAA,gBACZ,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,KAAK;AACzB,oBAAI,eAAe,IAAI;AACnB,sBAAI,EAAE,GAAG,aAAa,IAAK,OAAM,IAAI,MAAM,oCAAqC,GAAG,YAAY,IAAI;AACnG,uBAAK,GAAG,GAAG,SAAS;AAAA,gBACxB;AACA,oBAAI,MAAM,SAAS,GAAG,MAAM,OAAOE,IAAG,KAAK,GAAG,GAAG,SAAS,MAAM,OAAOF,GAAE,OAAQ,QAAO;AACxF,sBAAM,EAAE,EAAE,GAAG,KAAK,KAAK,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,KAAKA,GAAE,SAASA,GAAE,OAAO,EAAE,IAAI,MAAM,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM;AAAA,cACnH;AACA,qBAAO,EAAE,GAAG,KAAK,GAAG;AAAA,gBAChB,aAAa;AAAA,gBACb,OAAO;AAAA,cACX;AAAA,YACJ,EAAE,GAAG,QAAQ,GAAG,MAAM,CAAC;AAEvB,YAAAf,QAAO,UAAU;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAASF,sBAAqB;AAyDrD,gBAAII,QAAOJ,qBAAoB,CAAC;AAChC,gBAAIK,UAASL,qBAAoB,CAAC;AAIlC,gBAAIG,WAAU;AAAA,cACV,QAAQC,MAAK;AAAA,YACjB;AA2CA,gBAAI,QAAQ,MAAM,IAAI,GAAG;AACzB,gBAAI,QAAQ,MAAM,IAAI,EAAE;AACxB,gBAAI,SAAS,MAAM,IAAI,EAAE;AACzB,gBAAI,QAAQ,MAAM,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,IAAI,EAAE,IAAI,MAAM,KAAK,GAAG;AAC1E,gBAAI,YAAY,MAAM,IAAI,GAAG;AAC7B,gBAAI,QAAQ;AACZ,gBAAI,oBAAoB;AAAA,cACpB,OAAO,QAAQ,QAAQ,SAAS;AAAA;AAAA,cAChC,OAAO,MAAM,QAAQ,KAAK,EAAE;AAAA,cAC5B,OAAO;AAAA,cACP,OAAO,WAAW;AACd,oBAAI,SAAS;AACb,yBAASI,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACnC,2BAAS,OAAO,QAAQ,MAAMA,EAAC,GAAG,EAAE;AAAA,gBACxC;AACA,uBAAO;AAAA,cACX,EAAE;AAAA,cACF,OAAO;AAAA,cACP,OAAO,QAAQ,QAAQ;AAAA,YAC3B;AAEA,qBAAS,MAAM,MAAM,IAAI;AACrB,kBAAI,SAAS;AACb,uBAASA,KAAI,MAAMA,MAAK,IAAIA,MAAK;AAC7B,0BAAU,OAAO,aAAaA,EAAC;AAAA,cACnC;AACA,qBAAO;AAAA,YACX;AAGA,YAAAL,SAAQ,MAAM,SAAS,MAAM,QAAQ,OAAO;AACxC,sBAAQ,SAAS;AAAA,gBACb,MAAM;AAAA,cACV;AACA,qBAAOA,SAAQ,KAAK,IAAI,IAAIA,SAAQ,KAAK,IAAI,EAAE,MAAM,QAAQ,KAAK,IAC9DA,SAAQ,MAAM,MAAM,QAAQ,KAAK;AAAA,YACzC;AAEA,YAAAA,SAAQ,OAAO;AAAA;AAAA,cAEX,OAAO,SAAS,MAAM,QAAQ,OAAO;AACjC,wBAAQ,KAAK,MAAM;AAAA,kBACf,KAAK;AAAA,kBACL,KAAK;AACD,2BAAO;AAAA,kBACX,KAAK;AACD,2BAAOE,QAAO,UAAU;AAAA,kBAC5B,KAAK;AACD,2BAAO;AAAA,kBACX,KAAK;AACD,2BAAO;AAAA,kBACX,KAAK;AACD;AAAA,kBACJ,KAAK;AACD,2BAAOA,QAAO;AAAA,sBACV,OAAO,MAAM,EAAE;AAAA,oBACnB;AAAA,kBACJ,KAAK;AACD,2BAAOA,QAAO;AAAA,uBACT,QAAQ,QAAQ,OAAO,MAAM,EAAE;AAAA,oBACpC;AAAA,kBACJ,KAAK;AACD;AAAA,kBACJ,KAAK;AACD,2BAAO,KAAK,QAAQ,KAAK;AAAA,kBAC7B,KAAK;AACD;AAAA,kBACJ,KAAK;AACD,2BAAOA,QAAO;AAAA,sBACV,MAAM,MAAM,EAAE;AAAA,oBAClB;AAAA,kBACJ,KAAK;AACD,2BAAOA,QAAO;AAAA,uBACT,QAAQ,QAAQ,QAAQ,MAAM,EAAE;AAAA,oBACrC;AAAA,kBACJ,KAAK;AACD;AAAA,kBACJ,KAAK;AACD;AAAA,kBACJ,KAAK;AACD,2BAAOA,QAAO;AAAA,uBACT,QAAQ,QAAQ,QAAQ,MAAM,EAAE;AAAA,oBACrC;AAAA,kBACJ,KAAK;AACD,2BAAOA,QAAO;AAAA,sBACV,MAAM,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE;AAAA,oBACnC;AAAA,kBACJ,KAAK;AACD;AAAA,gBACR;AACA,uBAAO,KAAK,QAAQ,KAAK;AAAA,cAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAcA,WAAW,SAAS,MAAM,QAAQ,OAAO;AAErC,uBAAO,KAAK;AAAA,kBACRA,QAAO,QAAQ,IAAI,KAAK,OAAO,KAAK;AAAA,kBACpC;AAAA,kBACA;AAAA,gBACJ;AAAA,cACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cASA,OAAO,SAAS,MAAM,QAAQ,OAAO;AACjC,yBAAS;AAET,yBAASG,KAAI,GAAGA,KAAI,KAAK,KAAK,QAAQA,MAAK;AACvC,4BAAU,KAAK,IAAI,KAAK,KAAKA,EAAC,GAAG,QAAQ,KAAK;AAAA,gBAClD;AACA,uBAAO;AAAA,cACX;AAAA;AAAA,cAEA,iBAAiB,SAAS,MAAM,QAAQ,OAAO;AAE3C,yBAAS,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AAC1C,sBAAM,MAAM,MAAM,IAAI;AACtB,uBAAO;AAAA,cACX;AAAA;AAAA,cAEA,qBAAqB,SAAS,MAAM,QAAQ,OAAO;AAE/C,uBAAO,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AAAA,cAC5C;AAAA;AAAA,cAEA,sBAAsB,SAAS,MAAM,QAAQ,OAAO;AAEhD,uBAAO,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AAAA,cAC5C;AAAA;AAAA,cAEA,sBAAsB,SAAS,MAAM,QAAQ,OAAO;AAEhD,uBAAO;AAAA,cACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAuBA,YAAY,SAAS,MAAM,QAAQ,OAAO;AACtC,yBAAS;AAET,oBAAI,QAAQ,KAAK,WAAW,KAAK,UAAU;AAE3C,yBAASA,KAAI,GAAGA,KAAI,OAAOA,MAAK;AAC5B,4BAAU,KAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AAAA,gBAC/C;AACA,uBAAO;AAAA,cACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAWA,YAAY,SAAS,MAAM,QAAQ,OAAO;AACtC,oBAAI,MAAM,KAAK,IAAI,KAAK,KAAK,CAAC;AAC9B,oBAAI,MAAM,SAAS,KAAK,GAAG,IAAI,KAAK,MAChC,MAAMH,QAAO,QAAQ,GAAG,CAAC;AAC7B,uBAAOA,QAAO,QAAQ,KAAK,GAAG;AAAA,cAClC;AAAA;AAAA;AAAA;AAAA,cAIA,SAAS,SAAS,MAAM,QAAQ,OAAO;AAEnC,oBAAI,KAAK,OAAQ,QAAO,KAAK,gBAAgB,EAAE,MAAM,QAAQ,KAAK;AAGlE,oBAAI,UAAUA,QAAO,KAAK,KAAK,IAAI;AACnC,uBAAO,KAAK,IAAI,SAAS,QAAQ,KAAK;AAAA,cAC1C;AAAA,cACA,kBAAkB,SAAS,MAAM,QAAQ,OAAO;AAC5C,oBAAI,OAAO;AACX,yBAASG,KAAI,GAAG,MAAMA,KAAI,KAAK,KAAK,QAAQA,MAAK;AAC7C,yBAAO,KAAK,KAAKA,EAAC;AAClB,0BAAQ,KAAK,MAAM;AAAA,oBACf,KAAK;AACD,6BAAO,KAAK,QAAQ,KAAK,MAAM,EAAE;AACjC;AAAA,oBACJ,KAAK;AACD,0BAAI,MAAM,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,EAAE,WAAW;AACzD,0BAAI,MAAM,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,EAAE,WAAW;AACvD,+BAAS,KAAK,KAAK,MAAM,KAAK,MAAM;AAChC,+BAAO,KAAK,QAAQ,OAAO,aAAa,EAAE,GAAG,EAAE;AAAA,sBACnD;AAAA;AAAA,oBAEJ;AACI,0BAAI,aAAa,kBAAkB,KAAK,IAAI;AAC5C,0BAAI,YAAY;AACZ,iCAAS,MAAM,GAAG,OAAO,WAAW,QAAQ,OAAO;AAC/C,iCAAO,KAAK,QAAQ,WAAW,GAAG,GAAG,EAAE;AAAA,wBAC3C;AAAA,sBACJ;AAAA,kBACR;AAAA,gBACJ;AACA,uBAAOH,QAAO,KAAK,KAAK,MAAM,EAAE,CAAC;AAAA,cACrC;AAAA,cACA,OAAO,SAAS,MAAM,QAAQ,OAAO;AAEjC,oBAAI,MAAM,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,EAAE,WAAW;AACzD,oBAAI,MAAM,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,EAAE,WAAW;AACvD,uBAAO,OAAO;AAAA,kBACVA,QAAO,QAAQ,KAAK,GAAG;AAAA,gBAC3B;AAAA,cACJ;AAAA,cACA,SAAS,SAAS,MAAM,QAAQ,OAAO;AACnC,uBAAO,KAAK,UAAU,KAAK,OAAO,KAAK;AAAA,cAC3C;AAAA;AAAA,cAEA,SAAS,SAAS,MAAM,QAAQ,OAAO;AACnC,uBAAO,OAAO;AAAA,kBACV,SAAS,KAAK,MAAM,EAAE;AAAA,gBAC1B;AAAA,cACJ;AAAA;AAAA,cAEA,KAAK,SAAS,MAAM,QAAQ,OAAO;AAC/B,uBAAO,OAAO;AAAA,kBACV,SAAS,KAAK,MAAM,EAAE;AAAA,gBAC1B;AAAA,cACJ;AAAA;AAAA,cAEA,OAAO,SAAS,MAAM,QAAQ,OAAO;AACjC,uBAAO,OAAO;AAAA,kBACV,SAAS,KAAK,MAAM,CAAC;AAAA,gBACzB;AAAA,cACJ;AAAA;AAAA,cAEA,kBAAkB,SAAS,MAAM,QAAQ,OAAO;AAC5C,uBAAO,MAAM,KAAK,IAAI,KAAK;AAAA,cAC/B;AAAA;AAAA;AAAA;AAAA,cAIA,uBAAuB,WAAW;AAC9B,oBAAI,oBAAoB,mEAAmE,MAAM,GAAG;AACpG,oBAAI,4BAA4B,8EAAkO,MAAM,GAAG;AAC3Q,oBAAI,MAAM,CAAC;AACX,yBAASG,KAAI,GAAGA,KAAI,kBAAkB,QAAQA,MAAK;AAC/C,sBAAI,kBAAkBA,EAAC,CAAC,IAAI,0BAA0BA,EAAC;AAAA,gBAC3D;AACA,uBAAO;AAAA,cACX,EAAE;AAAA,cACF,qBAAqB,SAAS,MAAM,QAAQ,OAAO;AAC/C,uBAAO,KAAK,sBAAsB,KAAK,IAAI;AAAA,cAC/C;AAAA,YACJ,CAAC;AAED,YAAAP,QAAO,UAAUE;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAASF,SAAQC,UAASF,sBAAqB;AAErD,YAAAC,QAAO,UAAUD,qBAAoB,EAAE;AAAA,UAElC;AAAA;AAAA;AAAA,UAEC,SAASC,SAAQC,UAASF,sBAAqB;AASrD,gBAAIc,YAAWd,qBAAoB,CAAC;AACpC,gBAAII,QAAOJ,qBAAoB,CAAC;AAChC,gBAAIe,UAASf,qBAAoB,CAAC;AAElC,qBAAS,aAAa,UAAU,MAAM,MAA+B;AAEjE,qBAAO,QAAQ,CAAC;AAChB,kBAAI,SAAS;AAAA,gBACT,MAAM,OAAO,SAAS,WAAW,KAAK,QAAQc,UAAS,QAAQ,IAAI,IAAI;AAAA,gBACvE;AAAA,gBACA,MAAMV,MAAK,KAAK,QAAQ;AAAA;AAAA,gBACxB,MAAMW,QAAO,MAAM,IAAI;AAAA,cAC3B;AACA,qBAAO,OAAO,KAAK,MAAM,CAAC;AAC1B,qBAAO,KAAK,KAAK,SAAS,SAAY,SAAS,OAAO,IAAI;AAE1D,sBAAQ,OAAO,MAAM;AAAA,gBACjB,KAAK;AACD,yBAAO,QAAQ,CAAC;AAChB,kBAAAX,MAAK,KAAK,UAAU,SAAS,OAAO,OAAO;AACvC,2BAAO,MAAM;AAAA,sBACT,aAAa,OAAO,OAAO,OAAO,IAAI;AAAA,oBAC1C;AAAA,kBACJ,CAAC;AACD;AAAA,gBACJ,KAAK;AACD,yBAAO,aAAa,CAAC;AACrB,kBAAAA,MAAK,KAAK,UAAU,SAAS,OAAOoB,OAAM;AACtC,2BAAO,WAAW;AAAA,sBACd,aAAa,OAAOA,OAAM,OAAO,IAAI;AAAA,oBACzC;AAAA,kBACJ,CAAC;AACD;AAAA,cACR;AAEA,qBAAO;AAAA,YAEX;AAEA,YAAAvB,QAAO,UAAU;AAAA,UAGZ;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAASF,sBAAqB;AAErD,YAAAC,QAAO,UAAUD,qBAAoB,EAAE;AAAA,UAElC;AAAA;AAAA;AAAA,UAEC,SAASC,SAAQC,UAASF,sBAAqB;AAwBrD,gBAAIc,YAAWd,qBAAoB,CAAC;AACpC,gBAAII,QAAOJ,qBAAoB,CAAC;AAChC,gBAAI,eAAeA,qBAAoB,EAAE;AAEzC,qBAAS,MAAM,UAAU,MAAM;AAC3B,kBAAI,SAAS,aAAa,QAAQ;AAClC,kBAAI,SAAS,KAAK,KAAK,QAAQ,IAAI;AACnC,uBAASQ,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AAAA,cAGxC;AACA,qBAAO;AAAA,YACX;AAsCA,gBAAI,OAAO;AAAA,cACP,MAAM,SAAS,KAAK,QAAQ,MAAM,MAA+B;AAC7D,oBAAI,SAAS,CAAC;AAGd,oBACI,KAAK,KAAK,QAAQ,MAAM,MAAM,MAAM,KACpC,KAAK,KAAK,QAAQ,MAAM,MAAM,MAAM,GACtC;AACE,uBAAK,MAAM,QAAQ,MAAM,MAAM,MAAM;AACrC,uBAAK,WAAW,QAAQ,MAAM,MAAM,MAAM;AAC1C,uBAAK,MAAM,QAAQ,MAAM,MAAM,MAAM;AAAA,gBACzC;AAEA,uBAAO;AAAA,cACX;AAAA;AAAA,cAEA,MAAM,SAAS,QAAQ,MAAM,MAAM,QAAQ;AACvC,oBAAI,SAAS,OAAO;AAEpB,uBAAO,MAAM,QAAQ,OAAO,MAAM,OAAO,IAAI,OAAO,OAAO,IAAI,MAAM;AAErE,uBAAO,OAAO,WAAW;AAAA,cAC7B;AAAA,cACA,MAAM,SAAS,QAAQ,MAAM,MAAM,QAAQ;AACvC,oBAAI,SAAS,OAAO;AAEpB,wBAAQ,OAAO,MAAM;AAAA,kBACjB,KAAK;AAED,wBAAI,OAAO,SAAS,MAAMM,UAAS,cAAc,EAAG,QAAO;AAC3D;AAAA,kBACJ,KAAK;AACD,wBAAI,OAAO,KAAK,YAAY;AAExB,0BAAI,OAAO,KAAK,QAAQ,UAAa,OAAO,KAAK,QAAQ,QAAW;AAEhE,4BAAI,OAAO,KAAK,UAAU,EAAG,QAAO;AAAA,sBACxC;AAEA,0BAAI,OAAO,KAAK,WAAW,CAAC,EAAG,QAAO;AAAA,oBAC1C;AACA;AAAA,kBACJ,KAAK;AAED,2BAAO;AAAA,gBACf;AAEA,uBAAO,MAAM,QAAQ,OAAO,MAAMV,MAAK,KAAK,IAAI,GAAG,OAAO,MAAM,MAAM;AAEtE,uBAAO,OAAO,WAAW;AAAA,cAC7B;AAAA,cACA,OAAO,SAAS,QAAQ,MAAM,MAAM,QAAQ;AACxC,oBAAI,SAAS,OAAO;AAEpB,oBAAI,OAAO,OAAO;AAClB,oBAAI,eAAe,OAAO;AAC1B,oBAAI,iBAAiB,YAAY,iBAAiB,WAAW,iBAAiB,WAAY,QAAO;AAGjG,oBAAI,CAAC,KAAK,YAAY;AAClB,0BAAQ,cAAc;AAAA,oBAClB,KAAK;AACD,6BAAO,MAAM,SAAS,OAAO,MAAM,MAAM,OAAO,UAAU,MAAM;AAChE,6BAAO,OAAO,WAAW;AAAA,oBAC7B,KAAK;AAED,0BAAI,OAAO,SAAS,MAAMU,UAAS,cAAc,EAAG,QAAO,OAAO,WAAW;AAC7E;AAAA,kBACR;AACA,yBAAO,MAAM,SAAS,OAAO,MAAM,MAAM,OAAO,UAAU,MAAM;AAChE,yBAAO,OAAO,WAAW;AAAA,gBAC7B;AAGA,oBAAI;AACJ,wBAAQ,cAAc;AAAA,kBAClB,KAAK;AACD,wBAAIJ,UAAS,OAAO,IAAI,MAAM,GAAG;AACjC,oBAAAA,OAAM,CAAC,IAAI,CAACA,OAAM,CAAC;AAInB,wBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,6BAAO,qBAAqB,SAAS,OAAO,MAAMA,OAAM,CAAC,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,MAAM;AAEhG,6BAAO,kBAAkB,SAAS,OAAO,MAAMA,OAAM,CAAC,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,MAAM;AAAA,oBACjG;AAEA,wBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,6BAAO,MAAM,SAAS,OAAO,MAAMA,OAAM,CAAC,GAAG,KAAK,KAAK,QAAQ,aAAa,IAAI;AAAA,oBACpF;AAGA,wBAAI,KAAK,SAAS;AAEd,0BAAI,KAAK,SAAS,UAAa,KAAK,SAAS,QAAW;AACpD,+BAAO,qBAAqB,SAAS,OAAO,MAAMA,OAAM,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM;AACpF,+BAAO,kBAAkB,SAAS,OAAO,MAAMA,OAAM,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM;AAAA,sBACrF;AAEA,0BAAI,KAAK,SAAS,UAAa,KAAK,SAAS,QAAW;AACpD,+BAAO,MAAM,SAAS,OAAO,MAAMA,OAAM,CAAC,EAAE,QAAQ,KAAK,MAAM,MAAM;AAAA,sBACzE;AAAA,oBACJ;AAEA;AAAA,kBAEJ,KAAK;AACD;AAAA,kBAEJ,KAAK;AAED,wCAAoB,KAAK,MAAM,IAAI,OAAO,OAAO,UAAU,GAAG,CAAC;AAC/D,wCAAoB,oBAAoB,kBAAkB,SAAS;AAGnE,wBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,6BAAO,qBAAqB,gBAAgB,OAAO,MAAM,mBAAmB,KAAK,KAAK,MAAM;AAC5F,6BAAO,kBAAkB,gBAAgB,OAAO,MAAM,mBAAmB,KAAK,KAAK,MAAM;AAAA,oBAC7F;AAEA,wBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,6BAAO,MAAM,gBAAgB,OAAO,MAAM,mBAAmB,KAAK,KAAK,MAAM;AAAA,oBACjF;AAEA;AAAA,kBAEJ,KAAK;AACD,wCAAoB,KAAK,MAAM,IAAI,OAAO,OAAO,SAAS,OAAO,QAAQ,YAAY,EAAE,GAAG,GAAG,CAAC;AAC9F,wCAAoB,oBAAoB,kBAAkB,SAAS;AAGnE,wBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,6BAAO,qBAAqB,gBAAgB,OAAO,MAAM,mBAAmB,KAAK,KAAK,MAAM;AAC5F,6BAAO,kBAAkB,gBAAgB,OAAO,MAAM,mBAAmB,KAAK,KAAK,MAAM;AAAA,oBAC7F;AAEA,wBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,6BAAO,MAAM,gBAAgB,OAAO,MAAM,mBAAmB,KAAK,KAAK,MAAM;AAAA,oBACjF;AACA;AAAA,gBACR;AAEA,uBAAO,OAAO,WAAW;AAAA,cAC7B;AAAA,cACA,YAAY,SAAS,QAAQ,MAAM,MAAM,QAAQ;AAC7C,oBAAI,SAAS,OAAO;AAEpB,oBAAI,OAAO,OAAO;AAClB,oBAAI,OAAON,MAAK,KAAK,IAAI;AACzB,oBAAI,CAAC,OAAO,WAAY;AAGxB,oBAAI,CAAC,OAAO,KAAK,YAAY;AACzB,yBAAO,MAAM,qBAAqB,OAAO,MAAM,KAAK,QAAQ,OAAO,WAAW,QAAQ,MAAM;AAAA,gBAChG,OAAO;AAGH,sBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,2BAAO,qBAAqB,qBAAqB,OAAO,MAAM,KAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,MAAM;AAC/G,2BAAO,kBAAkB,qBAAqB,OAAO,MAAM,KAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,MAAM;AAAA,kBAChH;AAEA,sBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAElD,wBAAI,KAAK,UAAU,EAAG,QAAO,MAAM,qBAAqB,OAAO,MAAM,KAAK,QAAQ,KAAK,KAAK,MAAM;AAAA,kBACtG;AAAA,gBACJ;AAEA,oBAAI,OAAO,WAAW,OAAQ,QAAO;AAErC,yBAASI,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AAClC,yBAAO,KAAK;AAAA,oBACR;AAAA,oBACA,KAAK;AAAA,sBACD,WAAW;AACP,4BAAI;AACJ,wBAAAJ,MAAK,KAAK,OAAO,YAAY,SAAS,MAAmB;AACrD,8BAAI,KAAK,SAAS,KAAKI,EAAC,EAAG,YAAW;AAAA,wBAC1C,CAAC;AACD,+BAAO,YAAY,OAAO,WAAWA,EAAC;AAAA,sBAC1C,EAAE;AAAA,sBACF,KAAK,KAAKA,EAAC,CAAC;AAAA,sBACZ,KAAKA,EAAC;AAAA,oBACV;AAAA,kBACJ;AAAA,gBACJ;AAEA,uBAAO,OAAO,WAAW;AAAA,cAC7B;AAAA,cACA,OAAO,SAAS,QAAQ,MAAM,MAAM,QAAQ;AACxC,oBAAI,SAAS,OAAO;AAEpB,oBAAI,CAAC,OAAO,MAAO;AAEnB,oBAAI,OAAO,OAAO;AAGlB,oBAAI,CAAC,OAAO,KAAK,YAAY;AACzB,yBAAO,MAAM,gBAAgB,OAAO,MAAM,KAAK,QAAQ,OAAO,MAAM,QAAQ,MAAM;AAAA,gBACtF,OAAO;AAGH,sBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAClD,2BAAO;AAAA,sBAAqB;AAAA,sBAAS,OAAO;AAAA,sBAAM,KAAK;AAAA,sBAAS,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI,OAAO,MAAM;AAAA,sBAAS;AAAA,sBACjH;AAAA,oBAAgH;AACpH,2BAAO;AAAA,sBAAkB;AAAA,sBAAS,OAAO;AAAA,sBAAM,KAAK;AAAA,sBAAS,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI,OAAO,MAAM;AAAA,sBAAS;AAAA,sBAC9G;AAAA,oBAA8G;AAAA,kBACtH;AAEA,sBAAI,KAAK,QAAQ,UAAa,KAAK,QAAQ,QAAW;AAElD,wBAAI,KAAK,UAAU,EAAG,QAAO,OAAO,WAAW;AAAA,wBAC1C,QAAO,MAAM,gBAAgB,OAAO,MAAM,KAAK,QAAS,KAAK,MAAM,OAAO,MAAM,QAAS,MAAM;AAAA,kBACxG;AAEA,sBAAI,KAAK,WAAW,CAAC,EAAG,QAAO,OAAO,WAAW;AAAA,gBACrD;AAEA,oBAAI,OAAO,WAAW,OAAQ,QAAO;AAErC,yBAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AAClC,yBAAO,KAAK;AAAA,oBACR;AAAA,oBACA,KAAK;AAAA,sBACD,OAAO,MAAMA,KAAI,OAAO,MAAM,MAAM;AAAA,sBACpC,KAAKA,EAAC;AAAA,sBACNA,KAAI,OAAO,MAAM;AAAA,oBACrB;AAAA,kBACJ;AAAA,gBACJ;AAEA,uBAAO,OAAO,WAAW;AAAA,cAC7B;AAAA,YACJ;AAcA,gBAAI,SAAS;AAAA,cACT,SAAS,SAAS,MAAM;AACpB,wBAAQ,KAAK,WACL,wEACH,QAAQ,WAAW,KAAK,KAAK,YAAY,CAAC,EAC1C,QAAQ,WAAW,KAAK,KAAK,YAAY,CAAC,EAC1C,QAAQ,UAAUJ,MAAK,QAAQ,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI,EAC7E,QAAQ,YAAY,KAAK,MAAM,EAC/B,QAAQ,cAAc,KAAK,QAAQ,EACnC,QAAQ,YAAY,KAAK,MAAM;AAAA,cACxC;AAAA,cACA,OAAO,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ,SAAS;AAC3D,oBAAI,WAAW,SAAU,QAAO;AAChC,wBAAQ,MAAM;AAAA,kBACV,KAAK;AAED,wBAAI,aAAa,YAAY,WAAW,SAAU,QAAO;AACzD;AAAA,gBACR;AAEA,oBAAI,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,gBACJ;AACA,qBAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,uBAAO,KAAK,IAAI;AAChB,uBAAO;AAAA,cACX;AAAA;AAAA,cAEA,OAAO,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ,SAAS;AAC3D,oBAAI,SAAS,KAAK,MAAM,EAAG,QAAO;AAElC,oBAAI,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,gBACJ;AACA,qBAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,uBAAO,KAAK,IAAI;AAChB,uBAAO;AAAA,cACX;AAAA,cACA,UAAU,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ,SAAS;AAC9D,oBAAI,WAAW,SAAU,QAAO;AAChC,oBAAI,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,gBACJ;AACA,qBAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,uBAAO,KAAK,IAAI;AAChB,uBAAO;AAAA,cACX;AAAA,cACA,aAAa,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ,SAAS;AACjE,oBAAI,SAAS,SAAU,QAAO;AAC9B,oBAAI,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,gBACJ;AACA,qBAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,uBAAO,KAAK,IAAI;AAChB,uBAAO;AAAA,cACX;AAAA,cACA,UAAU,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ,SAAS;AAC9D,oBAAI,SAAS,SAAU,QAAO;AAC9B,oBAAI,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,gBACJ;AACA,qBAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,uBAAO,KAAK,IAAI;AAChB,uBAAO;AAAA,cACX;AAAA,cACA,sBAAsB,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ,SAAS;AAC1E,oBAAI,UAAU,SAAU,QAAO;AAC/B,oBAAI,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,gBACJ;AACA,qBAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,uBAAO,KAAK,IAAI;AAChB,uBAAO;AAAA,cACX;AAAA,cACA,mBAAmB,SAAS,MAAM,MAAM,QAAQ,UAAU,QAAQ,SAAS;AACvE,oBAAI,UAAU,SAAU,QAAO;AAC/B,oBAAI,OAAO;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,QAAQ;AAAA,kBACR;AAAA,gBACJ;AACA,qBAAK,UAAU,OAAO,QAAQ,IAAI;AAClC,uBAAO,KAAK,IAAI;AAChB,uBAAO;AAAA,cACX;AAAA,YACJ;AAEA,kBAAM,OAAO;AACb,kBAAM,SAAS;AAEf,YAAAH,QAAO,UAAU;AAAA,UAEZ;AAAA;AAAA;AAAA,UAEC,SAASA,SAAQC,UAASF,sBAAqB;AAErD,YAAAC,QAAO,UAAUD,qBAAoB,EAAE;AAAA,UAElC;AAAA;AAAA;AAAA,UAEC,SAASC,SAAQC,UAASF,sBAAqB;AAgDrD,gBAAII,QAAOJ,qBAAoB,CAAC;AAGhC,mBAAO,kBAAkB,OAAO;AAChC,mBAAO,iBAAiB,OAAO;AAS/B,gBAAI;AACA,kBAAI,OAAO,MAAM,QAAQ;AAAA,YAC7B,SAAS,WAAW;AAChB,qBAAO,QAAQ,SAAS,MAAM,SAAS,YAAY,QAAQ;AACvD,oBAAI,QAAQ,SAAS,YAAY,aAAa;AAC9C,sBAAM,gBAAgB,MAAM,SAAS,YAAY,MAAM;AACvD,uBAAO;AAAA,cACX;AAAA,YACJ;AAEA,gBAAI,aAAa;AAAA;AAAA,cAEb,QAAQ;AAAA;AAAA,cAER,QAAQ;AAAA;AAAA,cAER,kBAAkB;AAAA;AAAA,cAElB,SAAS;AAAA;AAAA,cAET,MAAM;AAAA,YACV;AAEA,gBAAI,aAAa,uEAAuE,MAAM,GAAG;AACjG,gBAAI,yBAAyB,0BAA0B,MAAM,GAAG;AAChE,gBAAI,0BAA0B,0FAA0F,MAAM,GAAG;AAGjI,gBAAI,oBAAoB;AAAA,cACpB,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,YACT;AAMA,qBAAS,qBAAqB;AAE1B,mBAAK,SAAS;AAAA,gBACV,QAAQ,CAAC;AAAA,gBACT,gBAAgB,CAAC;AAAA,gBACjB,iBAAiB,CAAC;AAAA,cACtB;AAAA,YACJ;AAEA,+BAAmB,YAAY;AAAA,cAC3B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,YAKb;AAEA,+BAAmB,QAAQ,SAAS,UAAU;AAC1C,cAAAI,MAAK,OAAO,mBAAmB,WAAW,QAAQ;AAClD,qBAAO,mBAAmB;AAAA,YAC9B;AAEA,YAAAA,MAAK,OAAO,oBAAoB,UAAU;AAC1C,YAAAA,MAAK,OAAO,mBAAmB,WAAW,UAAU;AAGpD,+BAAmB,UAAU,OAAO;AAGpC,+BAAmB,UAAU,QAAQ;AAGrC,YAAAA,MAAK,OAAO,mBAAmB,WAAW;AAAA;AAAA;AAAA,cAGtC,MAAM,SAAS,QAAQ,KAAK,OAAO,UAAU,UAAU;AACnD,oBAAI,OAAO;AAEX,gBAAAA,MAAK,OAAO,KAAK,QAAQ;AAAA,kBACrB;AAAA,kBACA;AAAA,kBACA,OAAO,OAAO,UAAU,YAAY,QAAQ;AAAA,kBAC5C;AAAA,kBACA;AAAA,kBACA,SAAS;AAAA,oBACL;AAAA,oBACA,MAAM;AAAA,kBACV;AAAA,gBACJ,CAAC;AAED,qBAAK,OAAO,UAAU,SAAS,SAAS;AACpC,sBAAI,OAAO,YAAY,SAAU,QAAO;AACxC,sBAAI,OAAO,YAAY,YAAY,CAAC,CAAC,QAAQ,QAAQ,GAAG,EAAG,QAAO,SAAS,SAAS,EAAE;AACtF,sBAAI,OAAO,YAAY,YAAY,CAAC,QAAQ,QAAQ,GAAG,GAAG;AACtD,wBAAI,MAAM,QAAQ,MAAM,GAAG;AAC3B,wBAAI,MAAM,SAAS,IAAI,CAAC,GAAG,EAAE;AAC7B,wBAAI,MAAM,SAAS,IAAI,CAAC,GAAG,EAAE;AAC7B,2BAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI,IAAI;AAAA,kBACrD;AAAA,gBACJ,EAAE,mBAAmB,UAAU,OAAO;AAGtC,oBAAI,OAAO,KAAK,KAAK,OAAO,OAAO;AAEnC,yBAASqB,QAAO,OAAO;AAEnB,2BAASjB,KAAI,GAAGA,KAAI,wBAAwB,QAAQA,MAAK;AACrD,wBAAI;AACA,2BAAK,wBAAwBA,EAAC,CAAC,IAAI,IAAI,wBAAwBA,EAAC,CAAC;AAAA,oBACrE,SAAS,GAAG;AAAA,oBAAC;AAAA,kBACjB;AAEA,uBAAK,cAAc,IAAI;AAAA,oBAAM,MAAM;AAAA;AAAA,kBAA8B,CAAC;AAAA,gBACtE;AAGA,oBAAI,CAAC,MAAM;AAEP,sBAAI,MAAM,2BAA2B;AACrC,uBAAK,OAAO,MAAM;AAGlB,2BAASA,KAAI,GAAGA,KAAI,WAAW,QAAQA,MAAK;AACxC,wBAAI,iBAAiB,WAAWA,EAAC,GAAGiB,OAAM;AAAA,kBAC9C;AAGA,sBAAI,SAAU,KAAI,KAAK,QAAQ,KAAK,OAAO,UAAU,QAAQ;AAAA,sBACxD,KAAI,KAAK,QAAQ,KAAK,KAAK;AAGhC,2BAAS,IAAI,GAAG,IAAI,uBAAuB,QAAQ,KAAK;AACpD,wBAAI;AACA,0BAAI,uBAAuB,CAAC,CAAC,IAAI,KAAK,uBAAuB,CAAC,CAAC;AAAA,oBACnE,SAAS,GAAG;AAAA,oBAAC;AAAA,kBACjB;AAEA;AAAA,gBACJ;AAGA,qBAAK,QAAQ;AACb,qBAAK,OAAO,WAAW;AACvB,qBAAK,aAAa,mBAAmB;AACrC,qBAAK,cAAc,IAAI;AAAA,kBAAM;AAAA;AAAA,gBAA4C,CAAC;AAAA,cAC9E;AAAA;AAAA;AAAA,cAGA,kBAAkB,SAAS,MAAM,OAAO;AAEpC,oBAAI,CAAC,KAAK,OAAO;AACb,uBAAK,OAAO,IAAI,iBAAiB,MAAM,KAAK;AAC5C;AAAA,gBACJ;AAGA,oBAAI,iBAAiB,KAAK,OAAO;AACjC,oBAAI,eAAe,IAAI,EAAG,gBAAe,IAAI,KAAK,MAAM;AAAA,oBACnD,gBAAe,IAAI,IAAI;AAAA,cAChC;AAAA,cACA,SAAS;AAAA,cACT,iBAAiB;AAAA,cACjB,QAAQ,CAAC;AAAA;AAAA;AAAA,cAGT,MAAM,SAAS,KAAK,MAAM;AACtB,oBAAI,OAAO;AACX,qBAAK,OAAO,QAAQ,OAAO;AAG3B,oBAAI,CAAC,KAAK,OAAO;AACb,uBAAK,OAAO,IAAI,KAAK,IAAI;AACzB;AAAA,gBACJ;AAKA,qBAAK,iBAAiB,oBAAoB,oBAAoB;AAG9D,qBAAK,cAAc,IAAI;AAAA,kBAAM;AAAA;AAAA,gBAAqC,CAAC;AAEnE,oBAAI,KAAK,OAAO,MAAO,YAAW,MAAM,KAAK,OAAO,OAAO;AAAA,oBACtD,MAAK;AAEV,yBAAS,OAAO;AACZ,uBAAK,aAAa,mBAAmB;AACrC,uBAAK,cAAc,IAAI;AAAA,oBAAM;AAAA;AAAA,kBAA4C,CAAC;AAC1E,uBAAK,aAAa,mBAAmB;AACrC,uBAAK,cAAc,IAAI;AAAA,oBAAM;AAAA;AAAA,kBAA4C,CAAC;AAE1E,uBAAK,SAAS;AACd,uBAAK,aAAa,kBAAkB,GAAG;AAGvC,uBAAK,WAAW,KAAK,eAAe,KAAK;AAAA,oBACrC,QAAQ,KAAK,OAAO,UAAU,KAAK,OAAO,OAAO;AAAA,oBACjD;AAAA,oBAAM;AAAA,kBACV;AAEA,uBAAK,aAAa,mBAAmB;AACrC,uBAAK,cAAc,IAAI;AAAA,oBAAM;AAAA;AAAA,kBAA4C,CAAC;AAC1E,uBAAK,cAAc,IAAI;AAAA,oBAAM;AAAA;AAAA,kBAAgC,CAAC;AAC9D,uBAAK,cAAc,IAAI;AAAA,oBAAM;AAAA;AAAA,kBAAmC,CAAC;AAAA,gBACrE;AAAA,cACJ;AAAA;AAAA;AAAA,cAGA,OAAO,SAAS,QAAQ;AAEpB,oBAAI,CAAC,KAAK,OAAO;AACb,uBAAK,OAAO,IAAI,MAAM;AACtB;AAAA,gBACJ;AAGA,qBAAK,aAAa,mBAAmB;AACrC,qBAAK,cAAc,IAAI,MAAM,SAAS,OAAO,OAAO,IAAI,CAAC;AACzD,qBAAK,cAAc,IAAI,MAAM,SAAS,OAAO,OAAO,IAAI,CAAC;AAAA,cAC7D;AAAA,YACJ,CAAC;AAGD,YAAArB,MAAK,OAAO,mBAAmB,WAAW;AAAA,cACtC,aAAa;AAAA,cACb,QAAQ,mBAAmB;AAAA,cAC3B,YAAY;AAAA;AAAA,cAEZ,mBAAmB,SAAS,MAAM;AAE9B,oBAAI,CAAC,KAAK,OAAO;AACb,yBAAO,KAAK,OAAO,IAAI,kBAAkB,IAAI;AAAA,gBACjD;AAGA,uBAAO,KAAK,OAAO,gBAAgB,KAAK,YAAY,CAAC;AAAA,cACzD;AAAA;AAAA;AAAA,cAGA,uBAAuB,WAAW;AAE9B,oBAAI,CAAC,KAAK,OAAO;AACb,yBAAO,KAAK,OAAO,IAAI,sBAAsB;AAAA,gBACjD;AAGA,oBAAI,kBAAkB,KAAK,OAAO;AAClC,oBAAI,UAAU;AACd,yBAAS,KAAK,iBAAiB;AAC3B,sBAAI,CAAC,gBAAgB,eAAe,CAAC,EAAG;AACxC,6BAAW,IAAI,OAAO,gBAAgB,CAAC,IAAI;AAAA,gBAC/C;AACA,uBAAO;AAAA,cACX;AAAA,cACA,kBAAkB,WAAqB;AAAA,cAAC;AAAA,cACxC,cAAc;AAAA;AAAA,cACd,UAAU;AAAA,cACV,cAAc;AAAA,cACd,aAAa;AAAA,YACjB,CAAC;AAGD,YAAAA,MAAK,OAAO,mBAAmB,WAAW;AAAA,cACtC,kBAAkB,SAAS,iBAAiB,MAAMqB,SAAQ;AACtD,oBAAI,SAAS,KAAK,OAAO;AACzB,oBAAI,CAAC,OAAO,IAAI,EAAG,QAAO,IAAI,IAAI,CAAC;AACnC,uBAAO,IAAI,EAAE,KAAKA,OAAM;AAAA,cAC5B;AAAA,cACA,qBAAqB,SAAS,oBAAoB,MAAMA,SAAQ;AAC5D,oBAAI,UAAU,KAAK,OAAO,OAAO,IAAI,KAAK,CAAC;AAC3C,yBAASjB,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACrC,sBAAI,QAAQA,EAAC,MAAMiB,SAAQ;AACvB,4BAAQ,OAAOjB,MAAK,CAAC;AAAA,kBACzB;AAAA,gBACJ;AAAA,cACJ;AAAA,cACA,eAAe,SAAS,cAAc,OAAO;AACzC,oBAAI,UAAU,KAAK,OAAO,OAAO,MAAM,IAAI,KAAK,CAAC;AACjD,yBAASA,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACrC,0BAAQA,EAAC,EAAE,KAAK,MAAM,KAAK;AAAA,gBAC/B;AAEA,oBAAI,SAAS,OAAO,MAAM;AAC1B,oBAAI,KAAK,MAAM,EAAG,MAAK,MAAM,EAAE,KAAK;AAAA,cACxC;AAAA,YACJ,CAAC;AAGD,qBAAS,6BAA6B;AAClC,kBAAI,UAAU,WAAW;AACrB,oBAAI,iBAAiB;AACrB,oBAAI,OAAO;AACX,oBAAI,eAAe,SAAS;AAC5B,oBAAI,eAAe,KAAK,KAAK,aAAa,YAAY,CAAC,KAAK,CAAC;AAC7D,uBAAO,eAAe,KAAK,aAAa,CAAC,CAAC;AAAA,cAC9C,EAAE;AAEF,qBAAO,OAAO,gBACT,CAAC,WAAW,kBAAkB,KAAK,gBAAgB,IAAK,kBAAkB;AAE/E,uBAAS,oBAAoB;AACzB,oBAAI;AACA,yBAAO,IAAI,OAAO,gBAAgB;AAAA,gBACtC,SAAS,GAAG;AAAA,gBAAC;AAAA,cACjB;AAEA,uBAAS,kBAAkB;AACvB,oBAAI;AACA,yBAAO,IAAI,OAAO,eAAe,mBAAmB;AAAA,gBACxD,SAAS,GAAG;AAAA,gBAAC;AAAA,cACjB;AAAA,YACJ;AAIA,qBAAS,KAAKD,UAAS;AAEnB,uBAAS,YAAY,mBAAmB,KAAK,SAAS;AAClD,oBAAI,OAAO,mBAAmB,KAAK,QAAQ,QAAQ;AACnD,qBACK,CAAC,KAAK,QAAQ,MAAM,KAAK,MAAMA,SAAQ,GAAG,OAC1C,CAAC,KAAK,SAAS,MAAM,KAAK,OAAOA,SAAQ,KAAK,YAAY,CAAC,IAC9D;AAEE,yBAAO;AAAA,gBACX;AAAA,cACJ;AAEA,uBAAS,MAAM,UAAU,QAAQ;AAC7B,oBAAIH,MAAK,KAAK,QAAQ,MAAM,UAAU;AAClC,yBAAO,aAAa;AAAA,gBACxB;AACA,oBAAIA,MAAK,KAAK,QAAQ,MAAM,UAAU;AAClC,yBAAO,SAAS,KAAK,MAAM;AAAA,gBAC/B;AAAA,cACJ;AAAA,YAEJ;AAGA,qBAAS,QAAQ,MAAMG,UAAS;AAC5B,qBAAOH,MAAK,WAAW,KAAK,QAAQ,IAChC,KAAK,SAASG,QAAO,IAAI,mBAAmB,KAAK,KAAK,KAAK,QAAQ;AAAA,YAC3E;AAEA,YAAAN,QAAO,UAAU;AAAA,UAEZ;AAAA;AAAA,QACG,CAAC;AAAA;AAAA,IACV,CAAC;AAAA;AAAA;", "names": ["__webpack_require__", "module", "exports", "Handler", "<PERSON><PERSON>", "Random", "RE", "options", "i", "key", "parts", "re", "pathParts", "obj", "Constant", "<PERSON><PERSON><PERSON>", "n", "l", "u", "t", "r", "e", "o", "c", "name", "handle"]}