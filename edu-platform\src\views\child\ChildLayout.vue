<template>
  <div class="child-layout">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-content">
        <div class="logo">
          <el-icon size="24" color="#409EFF"><Star /></el-icon>
          <span class="logo-text">成长伙伴</span>
        </div>
        <div class="user-info">
          <el-avatar :size="32" :src="currentUser.avatar" />
          <span class="username">{{ currentUser.name }}</span>
          <el-badge :value="currentUser.points" class="points-badge">
            <el-icon><Coin /></el-icon>
          </el-badge>
        </div>
      </div>
    </el-header>

    <!-- 主要内容区域 -->
    <el-container class="main-container">
      <el-main class="content">
        <router-view />
      </el-main>
    </el-container>

    <!-- 底部导航栏 -->
    <el-footer class="footer">
      <div class="nav-tabs">
        <div
          v-for="tab in navTabs"
          :key="tab.name"
          class="nav-tab"
          :class="{ active: $route.name === tab.name }"
          @click="$router.push({ name: tab.name })"
        >
          <el-icon :size="24">
            <component :is="tab.icon" />
          </el-icon>
          <span class="tab-text">{{ tab.label }}</span>
        </div>
      </div>
    </el-footer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Star, Coin, ChatDotRound, Reading, User } from '@element-plus/icons-vue'

// 当前用户信息
const currentUser = ref({
  name: '小明',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  points: 520
})

// 底部导航配置
const navTabs = [
  {
    name: 'child-profile',
    label: '个人',
    icon: User
  },
  {
    name: 'child-chat',
    label: '好友聊天',
    icon: ChatDotRound
  },
  {
    name: 'child-study',
    label: '学习',
    icon: Reading
  }
]
</script>

<style scoped>
.child-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  height: 60px !important;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.username {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.points-badge {
  color: #F56C6C;
}

.main-container {
  flex: 1;
  overflow: hidden;
}

.content {
  padding: 0;
  height: 100%;
  overflow-y: auto;
}

.footer {
  height: 70px !important;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0;
}

.nav-tabs {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.nav-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  color: #999;
}

.nav-tab:hover {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.nav-tab.active {
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

.tab-text {
  font-size: 12px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .logo-text {
    font-size: 16px;
  }

  .nav-tab {
    padding: 6px 12px;
  }

  .tab-text {
    font-size: 11px;
  }
}
</style>
