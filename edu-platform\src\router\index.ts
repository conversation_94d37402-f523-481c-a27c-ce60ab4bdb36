import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue')
    },
    // 儿童端路由
    {
      path: '/child',
      name: 'child',
      component: () => import('../views/child/ChildLayout.vue'),
      children: [
        {
          path: '',
          redirect: '/child/profile'
        },
        {
          path: 'chat',
          name: 'child-chat',
          component: () => import('../views/child/ChatView.vue')
        },
        {
          path: 'study',
          name: 'child-study',
          component: () => import('../views/child/StudyView.vue')
        },
        {
          path: 'profile',
          name: 'child-profile',
          component: () => import('../views/child/ProfileView.vue')
        }
      ]
    },
    // 志愿者端路由
    {
      path: '/volunteer',
      name: 'volunteer',
      component: () => import('../views/volunteer/VolunteerLayout.vue'),
      children: [
        {
          path: '',
          redirect: '/volunteer/dashboard'
        },
        {
          path: 'dashboard',
          name: 'volunteer-dashboard',
          component: () => import('../views/volunteer/DashboardView.vue')
        },
        {
          path: 'children',
          name: 'volunteer-children',
          component: () => import('../views/volunteer/ChildrenView.vue')
        },
        {
          path: 'training',
          name: 'volunteer-training',
          component: () => import('../views/volunteer/TrainingView.vue')
        },
        {
          path: 'courses',
          name: 'volunteer-courses',
          component: () => import('../views/volunteer/CoursesView.vue')
        }
      ]
    },
    // 管理端路由
    {
      path: '/admin',
      name: 'admin',
      component: () => import('../views/admin/AdminLayout.vue'),
      children: [
        {
          path: '',
          redirect: '/admin/dashboard'
        },
        {
          path: 'dashboard',
          name: 'admin-dashboard',
          component: () => import('../views/admin/DashboardView.vue')
        },
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('../views/admin/UsersView.vue')
        },
        {
          path: 'content',
          name: 'admin-content',
          component: () => import('../views/admin/ContentView.vue')
        },
        {
          path: 'analytics',
          name: 'admin-analytics',
          component: () => import('../views/admin/AnalyticsView.vue')
        }
      ]
    }
  ],
})

export default router
