<template>
  <div class="chat-view">
    <!-- 聊天列表 -->
    <div v-if="!selectedChat" class="chat-list">
      <div class="section">
        <h3 class="section-title">
          <el-icon><Robot /></el-icon>
          AI历史人物
        </h3>
        <div class="chat-items">
          <div 
            v-for="figure in historicalFigures" 
            :key="figure.id"
            class="chat-item ai-chat"
            @click="selectChat(figure, 'ai')"
          >
            <el-avatar :size="50" :src="figure.avatar" />
            <div class="chat-info">
              <div class="name">{{ figure.name }}</div>
              <div class="description">{{ figure.description }}</div>
            </div>
            <el-tag size="small" type="success">AI</el-tag>
          </div>
        </div>
      </div>

      <div class="section">
        <h3 class="section-title">
          <el-icon><User /></el-icon>
          志愿者朋友
        </h3>
        <div class="chat-items">
          <div 
            v-for="volunteer in volunteers" 
            :key="volunteer.id"
            class="chat-item volunteer-chat"
            @click="selectChat(volunteer, 'volunteer')"
          >
            <el-avatar :size="50" :src="volunteer.avatar" />
            <div class="chat-info">
              <div class="name">{{ volunteer.name }}</div>
              <div class="description">{{ volunteer.profession }}</div>
            </div>
            <el-badge 
              :value="volunteer.status === 'available' ? '在线' : '离线'" 
              :type="volunteer.status === 'available' ? 'success' : 'info'"
            />
          </div>
        </div>
      </div>

      <div class="section">
        <h3 class="section-title">
          <el-icon><Heart /></el-icon>
          心理AI助手
        </h3>
        <div class="chat-items">
          <div 
            class="chat-item ai-chat"
            @click="selectChat(psychologyAI, 'psychology')"
          >
            <el-avatar :size="50" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            <div class="chat-info">
              <div class="name">小心</div>
              <div class="description">我是你的心理小助手</div>
            </div>
            <el-tag size="small" type="warning">心理</el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div v-else class="chat-interface">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <el-button 
          type="text" 
          @click="selectedChat = null"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <el-avatar :size="40" :src="selectedChat.avatar" />
        <div class="chat-title">
          <div class="name">{{ selectedChat.name }}</div>
          <div class="status">{{ getStatusText() }}</div>
        </div>
        <el-button type="text" @click="showChatInfo = true">
          <el-icon><MoreFilled /></el-icon>
        </el-button>
      </div>

      <!-- 消息列表 -->
      <div class="messages-container" ref="messagesContainer">
        <div 
          v-for="message in currentMessages" 
          :key="message.id"
          class="message"
          :class="{ 'own-message': message.isOwn }"
        >
          <el-avatar 
            v-if="!message.isOwn" 
            :size="32" 
            :src="selectedChat.avatar" 
            class="message-avatar"
          />
          <div class="message-content">
            <div class="message-bubble">
              {{ message.content }}
            </div>
            <div class="message-time">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <el-input
          v-model="newMessage"
          placeholder="输入消息..."
          @keyup.enter="sendMessage"
          class="message-input"
        >
          <template #append>
            <el-button type="primary" @click="sendMessage">
              <el-icon><Promotion /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { Robot, User, Heart, ArrowLeft, MoreFilled, Promotion } from '@element-plus/icons-vue'
import { historicalFigures, volunteersData } from '@/mock/data'

// 响应式数据
const selectedChat = ref(null)
const newMessage = ref('')
const currentMessages = ref([])
const messagesContainer = ref(null)
const showChatInfo = ref(false)

// 模拟数据
const volunteers = volunteersData.list.slice(0, 5)
const psychologyAI = {
  id: 'psychology',
  name: '小心',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  description: '我是你的心理小助手'
}

// 选择聊天对象
const selectChat = (chat: any, type: string) => {
  selectedChat.value = { ...chat, type }
  loadMessages()
}

// 加载消息
const loadMessages = () => {
  // 模拟消息数据
  currentMessages.value = [
    {
      id: 1,
      content: selectedChat.value.type === 'ai' ? 
        (historicalFigures.find(f => f.id === selectedChat.value.id)?.greetings[0] || '你好！') :
        '你好，很高兴认识你！',
      timestamp: new Date(Date.now() - 60000),
      isOwn: false
    }
  ]
  scrollToBottom()
}

// 发送消息
const sendMessage = () => {
  if (!newMessage.value.trim()) return
  
  // 添加用户消息
  currentMessages.value.push({
    id: Date.now(),
    content: newMessage.value,
    timestamp: new Date(),
    isOwn: true
  })
  
  const userMessage = newMessage.value
  newMessage.value = ''
  
  // 模拟回复
  setTimeout(() => {
    let reply = ''
    if (selectedChat.value.type === 'ai') {
      reply = generateAIReply(userMessage)
    } else if (selectedChat.value.type === 'psychology') {
      reply = generatePsychologyReply(userMessage)
    } else {
      reply = '谢谢你的消息，我会认真回复的！'
    }
    
    currentMessages.value.push({
      id: Date.now(),
      content: reply,
      timestamp: new Date(),
      isOwn: false
    })
    scrollToBottom()
  }, 1000)
  
  scrollToBottom()
}

// 生成AI回复
const generateAIReply = (message: string) => {
  const replies = [
    '这是一个很好的问题，让我来为你解答...',
    '古人云："学而时习之，不亦说乎？"',
    '你的想法很有趣，我们可以深入探讨一下',
    '让我给你讲一个相关的故事吧...'
  ]
  return replies[Math.floor(Math.random() * replies.length)]
}

// 生成心理AI回复
const generatePsychologyReply = (message: string) => {
  const replies = [
    '我理解你的感受，你愿意和我分享更多吗？',
    '每个人都会有这样的时候，这很正常',
    '你做得很好，要相信自己！',
    '让我们一起想想解决的办法吧'
  ]
  return replies[Math.floor(Math.random() * replies.length)]
}

// 获取状态文本
const getStatusText = () => {
  if (selectedChat.value.type === 'ai' || selectedChat.value.type === 'psychology') {
    return '在线'
  }
  return selectedChat.value.status === 'available' ? '在线' : '离线'
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}
</script>

<style scoped>
.chat-view {
  height: 100%;
  background: #f5f7fa;
}

.chat-list {
  height: 100%;
  overflow-y: auto;
  padding: 20px;
}

.section {
  margin-bottom: 30px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  color: #409EFF;
  font-size: 16px;
  font-weight: 600;
}

.chat-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.chat-info {
  flex: 1;
}

.name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.description {
  font-size: 14px;
  color: #666;
}

.chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.chat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background: white;
}

.back-btn {
  padding: 8px !important;
}

.chat-title {
  flex: 1;
}

.chat-title .name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.chat-title .status {
  font-size: 12px;
  color: #67C23A;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f5f7fa;
}

.message {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.message.own-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
}

.own-message .message-bubble {
  background: #409EFF;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  text-align: center;
}

.input-area {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  background: white;
}

.message-input {
  width: 100%;
}
</style>
