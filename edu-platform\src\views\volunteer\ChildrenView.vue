<template>
  <div class="children-view">
    <div class="page-header">
      <h2>我的孩子</h2>
      <p>管理和关爱你负责的孩子们</p>
    </div>
    
    <div class="children-grid">
      <div 
        v-for="child in children" 
        :key="child.id"
        class="child-card"
      >
        <div class="child-header">
          <el-avatar :size="60" :src="child.avatar" />
          <div class="child-basic-info">
            <h3>{{ child.name }}</h3>
            <p>{{ child.age }}岁</p>
            <el-tag :type="getMoodType(child.mood)" size="small">
              {{ getMoodText(child.mood) }}
            </el-tag>
          </div>
        </div>
        
        <div class="child-stats">
          <div class="stat-item">
            <span class="stat-label">学习时长</span>
            <span class="stat-value">{{ child.studyTime }}h</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">等级</span>
            <span class="stat-value">Level {{ child.level }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">积分</span>
            <span class="stat-value">{{ child.points }}</span>
          </div>
        </div>
        
        <div class="child-actions">
          <el-button type="primary" @click="chatWithChild(child)">
            <el-icon><ChatDotRound /></el-icon>
            聊天
          </el-button>
          <el-button @click="viewProgress(child)">
            <el-icon><DataAnalysis /></el-icon>
            查看进度
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ChatDotRound, DataAnalysis } from '@element-plus/icons-vue'
import { childrenData } from '@/mock/data'
import { ElMessage } from 'element-plus'

const children = ref(childrenData.list.slice(0, 8))

// 获取心情类型
const getMoodType = (mood: string) => {
  const typeMap = {
    happy: 'success',
    calm: 'primary',
    excited: 'warning',
    sad: 'danger',
    confused: 'info'
  }
  return typeMap[mood] || 'info'
}

// 获取心情文本
const getMoodText = (mood: string) => {
  const textMap = {
    happy: '开心',
    calm: '平静',
    excited: '兴奋',
    sad: '难过',
    confused: '困惑'
  }
  return textMap[mood] || '未知'
}

const chatWithChild = (child: any) => {
  ElMessage.success(`开始与${child.name}聊天`)
}

const viewProgress = (child: any) => {
  ElMessage.info(`查看${child.name}的学习进度`)
}
</script>

<style scoped>
.children-view {
  padding: 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.children-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.child-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.child-card:hover {
  transform: translateY(-2px);
}

.child-header {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.child-basic-info h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.child-basic-info p {
  margin: 0 0 8px 0;
  color: #666;
}

.child-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.child-actions {
  display: flex;
  gap: 12px;
}

.child-actions .el-button {
  flex: 1;
}
</style>
