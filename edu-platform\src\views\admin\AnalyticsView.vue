<template>
  <div class="analytics-view">
    <div class="page-header">
      <h2>数据分析</h2>
      <p>深度分析用户行为和学习效果</p>
    </div>
    
    <div class="analytics-content">
      <div class="chart-section">
        <div class="chart-card">
          <h3>用户行为分析</h3>
          <div class="chart-placeholder">
            <el-icon size="48" color="#ccc"><DataAnalysis /></el-icon>
            <p>用户行为分析图表</p>
          </div>
        </div>
        
        <div class="chart-card">
          <h3>学习效果分析</h3>
          <div class="chart-placeholder">
            <el-icon size="48" color="#ccc"><TrendCharts /></el-icon>
            <p>学习效果趋势图</p>
          </div>
        </div>
      </div>
      
      <div class="insights-section">
        <h3>数据洞察</h3>
        <div class="insights-grid">
          <div class="insight-card">
            <h4>用户参与度</h4>
            <p>本月用户参与度较上月提升15%，主要来源于AI对话功能的优化</p>
          </div>
          <div class="insight-card">
            <h4>学习完成率</h4>
            <p>课程完成率达到78%，建议增加更多互动元素提升用户粘性</p>
          </div>
          <div class="insight-card">
            <h4>心理健康指标</h4>
            <p>85%的用户心理状态良好，需要关注5%的高风险用户群体</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DataAnalysis, TrendCharts } from '@element-plus/icons-vue'
</script>

<style scoped>
.analytics-view {
  padding: 0;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.chart-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.insights-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.insights-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insight-card {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #f9f9f9;
}

.insight-card h4 {
  margin: 0 0 10px 0;
  color: #409EFF;
}

.insight-card p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}
</style>
