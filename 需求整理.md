# 明光筑梦帮扶产品介绍

系统共分为儿童/志愿者端和管理端。

## 一、儿童端/志愿者端

用户注册：注册时管理员统一分配账户，避免儿童乱填

虚拟形象设定：根据个人信息和习惯和简单的对话生成一个特定的形象，具体呈现方式需要后续讨论

打开页面之后是经典的下部导航栏，共分为三个页面，分别是个人、好友聊天和学习

整体定位为社交软件+学习软件

### 界面一：个人

为每个用户定制一个主页，应该只展示基本信息和虚拟形象，设计隐私信息的不要展示。对于每个人，可以看到自己的信息、成就、心理状态等实时监测指标，强调成就和创造力，而不是常见的点赞数、粉丝数，学习某门课结束后可以获得积分或者凭证用来换区奖品，可以看到正在学习的课程等内容，具体需要后续确定。

### 界面二：好友与聊天

传统的好友列表+聊天功能。

通讯录的好友共分为自研大模型、大模型模拟的虚拟人物和正常的好友。

具体参考常见的社交软件（QQ、微信）即可。自研大模型要求能做到主动发消息，询问相关的情况并记录，具体记录的内容需要后端进行建模时进行设计，实现基本的发消息、视频通话以及表情包功能（能否尝试使用自己的虚拟形象进行定制表情包？）

对于自研大模型，要求能够做到日常的心理辅导等作用，并且能够每天定时发消息开启对话。

对于虚拟人物，主要使用prompt技术让其模拟某个历史人物为主，能够实现基本对话即可，但是需要充分利用prompt，让其能够真正模拟出相关人物的语气。

### 界面三：学习

学习页面共分为每日小问题、学习课程两个内容，没有类似于抖音、b站等让儿童选择的视频，只有MOOC类似的课程学习，跟踪每一个课程的学习进度。

每日小问题主要是依照目前存在的虚拟人物进行实现，每日有一个和某个虚拟形象相关的问题，需要儿童去问，然后反过来进行回答。

每种学习方式都有学习的奖励，比如积分用来兑换奖品。

**志愿者和儿童共用一份客户端，用来视频对话，节约开发成本，对于是否限制其他功能等待后续讨论**

**是否可以用来发布作业？**

## 二、志愿者Web端

志愿者可以设计课程、查看当前课程学习状态，根据儿童的属性进行筛选并随时跟踪相应的学习状态，并提供相应的辅导，这里要注意课程编辑页面直接复用郭老师的项目or自己重新写，重新写难度较大，监控儿童学习状态也有点复杂，这部分需要在进行讨论

## 三、管理员后端

需要一个评价体系用来随时监控儿童的状态，收集一段时间的信息并给出相应的儿童画像or数字孪生评价，根据定义的指标监控儿童的心理状态，这里要注意收集的数据和评价体系的同意，这部分不太好办
