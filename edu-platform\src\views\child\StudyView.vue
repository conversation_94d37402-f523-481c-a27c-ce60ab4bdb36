<template>
  <div class="study-view">
    <!-- 顶部统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#409EFF"><Reading /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ studyStats.totalHours }}</div>
          <div class="stat-label">学习时长(小时)</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#67C23A"><Trophy /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ studyStats.completedCourses }}</div>
          <div class="stat-label">完成课程</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon size="24" color="#E6A23C"><Star /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-value">{{ studyStats.currentLevel }}</div>
          <div class="stat-label">当前等级</div>
        </div>
      </div>
    </div>

    <!-- 每日挑战 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><Lightning /></el-icon>
          每日挑战
        </h3>
        <el-tag type="warning" size="small">今日剩余 {{ remainingChallenges }} 个</el-tag>
      </div>
      <div class="challenge-cards">
        <div 
          v-for="challenge in dailyChallenges" 
          :key="challenge.id"
          class="challenge-card"
          :class="{ completed: challenge.isCompleted }"
          @click="startChallenge(challenge)"
        >
          <div class="challenge-header">
            <el-tag 
              :type="getDifficultyType(challenge.difficulty)" 
              size="small"
            >
              {{ getDifficultyText(challenge.difficulty) }}
            </el-tag>
            <div class="challenge-points">+{{ challenge.points }}分</div>
          </div>
          <div class="challenge-title">{{ challenge.title }}</div>
          <div class="challenge-description">{{ challenge.description }}</div>
          <div class="challenge-footer">
            <div class="challenge-time">
              <el-icon><Clock /></el-icon>
              {{ challenge.timeLimit }}分钟
            </div>
            <el-button 
              v-if="!challenge.isCompleted"
              type="primary" 
              size="small"
              @click.stop="startChallenge(challenge)"
            >
              开始挑战
            </el-button>
            <el-tag v-else type="success" size="small">已完成</el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程中心 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><School /></el-icon>
          课程中心
        </h3>
        <el-button type="text" @click="showAllCourses = !showAllCourses">
          {{ showAllCourses ? '收起' : '查看全部' }}
        </el-button>
      </div>
      
      <!-- 课程分类 -->
      <div class="course-categories">
        <el-button
          v-for="category in courseCategories"
          :key="category"
          :type="selectedCategory === category ? 'primary' : 'default'"
          size="small"
          @click="selectedCategory = category"
        >
          {{ category }}
        </el-button>
      </div>

      <!-- 课程列表 -->
      <div class="course-grid">
        <div 
          v-for="course in filteredCourses" 
          :key="course.id"
          class="course-card"
          @click="startCourse(course)"
        >
          <div class="course-thumbnail">
            <img :src="course.thumbnail" :alt="course.title" />
            <div class="course-level">Level {{ course.level }}</div>
          </div>
          <div class="course-content">
            <div class="course-title">{{ course.title }}</div>
            <div class="course-instructor">{{ course.instructor }}</div>
            <div class="course-meta">
              <div class="course-duration">
                <el-icon><Clock /></el-icon>
                {{ course.duration }}分钟
              </div>
              <div class="course-rating">
                <el-rate 
                  v-model="course.rating" 
                  disabled 
                  size="small"
                  show-score
                />
              </div>
            </div>
            <div class="course-progress">
              <el-progress 
                :percentage="course.progress" 
                :stroke-width="6"
                :show-text="false"
              />
              <span class="progress-text">{{ course.progress }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习报告 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          学习报告
        </h3>
      </div>
      <div class="report-card">
        <div class="report-item">
          <div class="report-label">本周学习时长</div>
          <div class="report-value">{{ weeklyReport.studyTime }}小时</div>
          <div class="report-trend positive">
            <el-icon><TrendCharts /></el-icon>
            +15%
          </div>
        </div>
        <div class="report-item">
          <div class="report-label">知识掌握度</div>
          <div class="report-value">{{ weeklyReport.masteryRate }}%</div>
          <div class="report-trend positive">
            <el-icon><TrendCharts /></el-icon>
            +8%
          </div>
        </div>
        <div class="report-item">
          <div class="report-label">活跃天数</div>
          <div class="report-value">{{ weeklyReport.activeDays }}天</div>
          <div class="report-trend">
            <el-icon><TrendCharts /></el-icon>
            持平
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Reading, Trophy, Star, Lightning, Clock, School, 
  DataAnalysis, TrendCharts 
} from '@element-plus/icons-vue'
import { coursesData, dailyChallenges as mockChallenges } from '@/mock/data'
import { ElMessage } from 'element-plus'

// 响应式数据
const showAllCourses = ref(false)
const selectedCategory = ref('全部')

// 学习统计
const studyStats = ref({
  totalHours: 45,
  completedCourses: 12,
  currentLevel: 5
})

// 每日挑战
const dailyChallenges = ref(mockChallenges.list.slice(0, 3))
const remainingChallenges = computed(() => 
  dailyChallenges.value.filter(c => !c.isCompleted).length
)

// 课程数据
const courses = ref(coursesData.list)
const courseCategories = ['全部', '语文', '数学', '英语', '科学', '艺术', '体育']

// 过滤课程
const filteredCourses = computed(() => {
  let filtered = courses.value
  if (selectedCategory.value !== '全部') {
    filtered = filtered.filter(course => course.category === selectedCategory.value)
  }
  return showAllCourses.value ? filtered : filtered.slice(0, 6)
})

// 学习报告
const weeklyReport = ref({
  studyTime: 12,
  masteryRate: 85,
  activeDays: 6
})

// 获取难度类型
const getDifficultyType = (difficulty: number) => {
  const types = ['success', 'warning', 'danger']
  return types[difficulty - 1] || 'info'
}

// 获取难度文本
const getDifficultyText = (difficulty: number) => {
  const texts = ['简单', '中等', '困难']
  return texts[difficulty - 1] || '未知'
}

// 开始挑战
const startChallenge = (challenge: any) => {
  if (challenge.isCompleted) {
    ElMessage.info('该挑战已完成')
    return
  }
  ElMessage.success(`开始挑战：${challenge.title}`)
  // 这里可以跳转到挑战页面
}

// 开始课程
const startCourse = (course: any) => {
  ElMessage.success(`开始学习：${course.title}`)
  // 这里可以跳转到课程页面
}
</script>

<style scoped>
.study-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100%;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 25px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409EFF;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.challenge-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.challenge-card {
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.challenge-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.challenge-card.completed {
  opacity: 0.7;
  background: #f9f9f9;
}

.challenge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.challenge-points {
  color: #E6A23C;
  font-weight: 600;
}

.challenge-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.challenge-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.challenge-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.challenge-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.course-categories {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.course-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.course-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.course-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.course-thumbnail {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.course-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-level {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.course-content {
  padding: 15px;
}

.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.course-instructor {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.course-duration {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.course-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 35px;
}

.report-card {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.report-item {
  text-align: center;
}

.report-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.report-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.report-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.report-trend.positive {
  color: #67C23A;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .challenge-cards {
    grid-template-columns: 1fr;
  }
  
  .course-grid {
    grid-template-columns: 1fr;
  }
  
  .report-card {
    grid-template-columns: 1fr;
  }
}
</style>
