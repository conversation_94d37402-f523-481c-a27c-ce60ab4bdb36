<template>
  <div class="volunteer-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="240px" class="sidebar">
        <div class="logo">
          <el-icon size="24" color="#67C23A"><User /></el-icon>
          <span class="logo-text">志愿者平台</span>
        </div>

        <el-menu
          :default-active="$route.name"
          class="sidebar-menu"
          :router="true"
          background-color="#f8f9fa"
          text-color="#333"
          active-text-color="#67C23A"
        >
          <el-menu-item index="volunteer-dashboard" route="/volunteer/dashboard">
            <el-icon><Monitor /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>

          <el-menu-item index="volunteer-children" route="/volunteer/children">
            <el-icon><User /></el-icon>
            <template #title>我的孩子</template>
          </el-menu-item>

          <el-menu-item index="volunteer-training" route="/volunteer/training">
            <el-icon><Reading /></el-icon>
            <template #title>培训中心</template>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/volunteer' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ getBreadcrumbText() }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <div class="header-right">
            <div class="volunteer-status">
              <el-switch
                v-model="isAvailable"
                active-text="在线"
                inactive-text="离线"
                @change="toggleStatus"
              />
            </div>

            <el-dropdown @command="handleUserCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="volunteerInfo.avatar" />
                <span class="username">{{ volunteerInfo.name }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                  <el-dropdown-item command="settings">设置</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主要内容区域 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { Monitor, User, Reading, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const route = useRoute()

// 志愿者信息
const volunteerInfo = ref({
  name: '张老师',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
})

const isAvailable = ref(true)

// 获取面包屑文本
const getBreadcrumbText = () => {
  const routeMap = {
    'volunteer-dashboard': '工作台',
    'volunteer-children': '我的孩子',
    'volunteer-training': '培训中心'
  }
  return routeMap[route.name as string] || '未知页面'
}

// 切换在线状态
const toggleStatus = (value: boolean) => {
  ElMessage.success(value ? '已上线，可以接收新的匹配' : '已离线')
}

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中')
      break
    case 'settings':
      ElMessage.info('设置功能开发中')
      break
    case 'logout':
      ElMessage.success('退出登录成功')
      break
  }
}
</script>

<style scoped>
.volunteer-layout {
  height: 100vh;
}

.sidebar {
  background: #f8f9fa;
  border-right: 1px solid #eee;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border-bottom: 1px solid #eee;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #67C23A;
}

.sidebar-menu {
  border-right: none;
  height: calc(100vh - 64px);
}

.header {
  background: white;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.volunteer-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #333;
}

.main-content {
  background: #f5f7fa;
  padding: 20px;
}
</style>
