<template>
  <div class="profile-view">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-avatar">
        <el-avatar :size="80" :src="userInfo.avatar" />
        <el-button type="primary" size="small" class="edit-avatar-btn">
          <el-icon><Camera /></el-icon>
        </el-button>
      </div>
      <div class="user-info">
        <h2 class="username">{{ userInfo.name }}</h2>
        <div class="user-level">
          <el-icon><Star /></el-icon>
          Level {{ userInfo.level }}
        </div>
        <div class="user-points">
          <el-icon><Coin /></el-icon>
          {{ userInfo.points }} 积分
        </div>
      </div>
      <div class="level-progress">
        <div class="progress-label">升级进度</div>
        <el-progress
          :percentage="levelProgress"
          :stroke-width="8"
          color="#409EFF"
        />
        <div class="progress-text">还需 {{ nextLevelPoints }} 积分升级</div>
      </div>
    </div>

    <!-- 成就系统 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><Trophy /></el-icon>
          我的成就
        </h3>
        <el-tag type="info" size="small">{{ unlockedAchievements }}/{{ totalAchievements }}</el-tag>
      </div>
      <div class="achievements-grid">
        <div
          v-for="achievement in achievements"
          :key="achievement.id"
          class="achievement-card"
          :class="{ unlocked: achievement.unlocked }"
        >
          <div class="achievement-icon">{{ achievement.icon }}</div>
          <div class="achievement-info">
            <div class="achievement-name">{{ achievement.name }}</div>
            <div class="achievement-description">{{ achievement.description }}</div>
          </div>
          <div v-if="achievement.unlocked" class="achievement-badge">
            <el-icon color="#67C23A"><Check /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 心理状态监测 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          心理状态监测
        </h3>
      </div>
      <div class="mental-status">
        <div class="status-item">
          <div class="status-icon">
            <el-icon size="24" color="#67C23A"><SuccessFilled /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-label">当前心情</div>
            <div class="status-value">开心</div>
          </div>
        </div>
        <div class="status-item">
          <div class="status-icon">
            <el-icon size="24" color="#409EFF"><TrendCharts /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-label">学习积极性</div>
            <div class="status-value">85%</div>
          </div>
        </div>
        <div class="status-item">
          <div class="status-icon">
            <el-icon size="24" color="#E6A23C"><Star /></el-icon>
          </div>
          <div class="status-content">
            <div class="status-label">创造力指数</div>
            <div class="status-value">92分</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 正在学习的课程 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><Reading /></el-icon>
          正在学习
        </h3>
      </div>
      <div class="current-courses">
        <div class="course-item">
          <div class="course-thumbnail">
            <img src="https://via.placeholder.com/80x60" alt="课程封面" />
          </div>
          <div class="course-info">
            <div class="course-title">古诗词鉴赏</div>
            <div class="course-progress">
              <el-progress :percentage="75" :stroke-width="6" />
              <span class="progress-text">75%</span>
            </div>
          </div>
          <el-button type="primary" size="small">继续学习</el-button>
        </div>
        <div class="course-item">
          <div class="course-thumbnail">
            <img src="https://via.placeholder.com/80x60" alt="课程封面" />
          </div>
          <div class="course-info">
            <div class="course-title">数学思维训练</div>
            <div class="course-progress">
              <el-progress :percentage="45" :stroke-width="6" />
              <span class="progress-text">45%</span>
            </div>
          </div>
          <el-button type="primary" size="small">继续学习</el-button>
        </div>
      </div>
    </div>

    <!-- 虚拟形象设置 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><User /></el-icon>
          虚拟形象
        </h3>
      </div>
      <div class="avatar-customization">
        <div class="avatar-preview">
          <el-avatar :size="120" :src="userInfo.avatar" />
        </div>
        <div class="customization-options">
          <div class="option-group">
            <div class="option-label">头像风格</div>
            <div class="option-buttons">
              <el-button
                v-for="style in avatarStyles"
                :key="style.id"
                :type="selectedAvatarStyle === style.id ? 'primary' : 'default'"
                size="small"
                @click="selectedAvatarStyle = style.id"
              >
                {{ style.name }}
              </el-button>
            </div>
          </div>
          <div class="option-group">
            <div class="option-label">主题色彩</div>
            <div class="color-options">
              <div
                v-for="color in themeColors"
                :key="color"
                class="color-option"
                :style="{ backgroundColor: color }"
                :class="{ active: selectedColor === color }"
                @click="selectedColor = color"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置选项 -->
    <div class="section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon><Setting /></el-icon>
          设置
        </h3>
      </div>
      <div class="settings-list">
        <div class="setting-item" @click="showParentControl = true">
          <div class="setting-info">
            <el-icon><Lock /></el-icon>
            <span>家长控制</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="showNotificationSettings = true">
          <div class="setting-info">
            <el-icon><Bell /></el-icon>
            <span>通知设置</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="showPrivacySettings = true">
          <div class="setting-info">
            <el-icon><View /></el-icon>
            <span>隐私设置</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="showHelp = true">
          <div class="setting-info">
            <el-icon><QuestionFilled /></el-icon>
            <span>帮助与反馈</span>
          </div>
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <el-button type="primary" @click="shareProfile">
        <el-icon><Share /></el-icon>
        分享我的成就
      </el-button>
      <el-button @click="exportReport">
        <el-icon><Download /></el-icon>
        导出学习报告
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Camera, Star, Coin, Trophy, Check, DataAnalysis, Reading,
  School, Lightning, ChatDotRound, User, Setting, Lock,
  Bell, View, QuestionFilled, ArrowRight, Share, Download,
  SuccessFilled, TrendCharts
} from '@element-plus/icons-vue'
import { achievementsData } from '@/mock/data'
import { ElMessage } from 'element-plus'

// 用户信息
const userInfo = ref({
  name: '小明',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  level: 5,
  points: 520
})

// 等级进度
const levelProgress = computed(() => {
  const currentLevelPoints = userInfo.value.level * 100
  const nextLevelPoints = (userInfo.value.level + 1) * 100
  const progress = ((userInfo.value.points - currentLevelPoints) / (nextLevelPoints - currentLevelPoints)) * 100
  return Math.min(Math.max(progress, 0), 100)
})

const nextLevelPoints = computed(() => {
  const nextLevelPoints = (userInfo.value.level + 1) * 100
  return nextLevelPoints - userInfo.value.points
})

// 成就数据
const achievements = ref(achievementsData)
const unlockedAchievements = computed(() =>
  achievements.value.filter(a => a.unlocked).length
)
const totalAchievements = computed(() => achievements.value.length)

// 学习统计
const studyStats = ref({
  totalHours: 45,
  completedCourses: 12,
  challengesCompleted: 28,
  chatSessions: 156
})

// 虚拟形象设置
const selectedAvatarStyle = ref(1)
const selectedColor = ref('#409EFF')

const avatarStyles = [
  { id: 1, name: '可爱' },
  { id: 2, name: '酷炫' },
  { id: 3, name: '简约' },
  { id: 4, name: '卡通' }
]

const themeColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#909399', '#9C27B0', '#FF9800', '#4CAF50'
]

// 设置弹窗状态
const showParentControl = ref(false)
const showNotificationSettings = ref(false)
const showPrivacySettings = ref(false)
const showHelp = ref(false)

// 分享成就
const shareProfile = () => {
  ElMessage.success('成就分享链接已复制到剪贴板')
}

// 导出报告
const exportReport = () => {
  ElMessage.success('学习报告导出成功')
}
</script>

<style scoped>
.profile-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100%;
}

.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 25px;
  position: relative;
  overflow: hidden;
}

.user-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.user-avatar {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  padding: 0;
}

.username {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.user-level, .user-points {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 16px;
}

.level-progress {
  margin-top: 20px;
}

.progress-label {
  font-size: 14px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.progress-text {
  font-size: 12px;
  margin-top: 8px;
  opacity: 0.8;
}

.section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409EFF;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
}

.achievement-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.achievement-card.unlocked {
  opacity: 1;
  border: 2px solid #67C23A;
}

.achievement-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 12px;
}

.achievement-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.achievement-description {
  font-size: 14px;
  color: #666;
}

.achievement-badge {
  position: absolute;
  top: 10px;
  right: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.avatar-customization {
  display: flex;
  gap: 30px;
  padding: 25px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.customization-options {
  flex: 1;
}

.option-group {
  margin-bottom: 20px;
}

.option-label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.option-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: #333;
  transform: scale(1.1);
}

.settings-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:hover {
  background: #f5f7fa;
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #333;
}

.quick-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.mental-status {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 12px;
}

.status-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
}

.status-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.current-courses {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.course-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 12px;
}

.course-thumbnail {
  flex-shrink: 0;
}

.course-thumbnail img {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.course-info {
  flex: 1;
}

.course-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.course-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 35px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .avatar-customization {
    flex-direction: column;
    text-align: center;
  }

  .quick-actions {
    flex-direction: column;
  }
}
</style>
