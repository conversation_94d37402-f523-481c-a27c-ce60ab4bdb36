import Mock from 'mockjs'

// 儿童用户数据
export const childrenData = Mock.mock({
  'list|20': [{
    'id|+1': 1,
    'name': '@cname',
    'age|6-16': 1,
    'avatar': '@image("100x100", "@color", "@cname")',
    'level|1-10': 1,
    'points|0-1000': 1,
    'studyTime|0-500': 1, // 小时
    'lastActive': '@datetime',
    'status|1': ['online', 'offline', 'studying'],
    'mood|1': ['happy', 'sad', 'excited', 'calm', 'confused'],
    'achievements|3-8': ['@word', '@word', '@word']
  }]
})

// 志愿者数据
export const volunteersData = Mock.mock({
  'list|15': [{
    'id|+1': 1,
    'name': '@cname',
    'age|22-45': 1,
    'avatar': '@image("100x100", "@color", "@cname")',
    'profession': '@word',
    'experience|1-10': 1, // 年
    'rating|3-5': 1,
    'serviceHours|0-1000': 1,
    'specialties|2-4': ['心理辅导', '学习指导', '兴趣培养', '生活指导'],
    'status|1': ['available', 'busy', 'offline'],
    'certifications|1-3': ['心理咨询师', '教师资格证', '社工证']
  }]
})

// AI历史人物数据
export const historicalFigures = [
  {
    id: 1,
    name: '孔子',
    avatar: '/avatars/confucius.png',
    description: '春秋时期思想家、教育家',
    specialties: ['教育', '道德', '礼仪'],
    personality: '温和、睿智、耐心',
    greetings: ['你好，小朋友，今天想学什么呢？', '学而时习之，不亦说乎？']
  },
  {
    id: 2,
    name: '李白',
    avatar: '/avatars/libai.png',
    description: '唐代浪漫主义诗人',
    specialties: ['诗词', '文学', '想象力'],
    personality: '豪放、浪漫、富有想象力',
    greetings: ['小朋友，想和我一起作诗吗？', '天生我材必有用，千金散尽还复来！']
  },
  {
    id: 3,
    name: '杜甫',
    avatar: '/avatars/dufu.png',
    description: '唐代现实主义诗人',
    specialties: ['诗词', '历史', '社会观察'],
    personality: '严谨、关怀、深沉',
    greetings: ['读书破万卷，下笔如有神', '小朋友，让我们一起感受诗词的美好吧']
  }
]

// 聊天消息数据
export const chatMessages = Mock.mock({
  'list|50': [{
    'id|+1': 1,
    'senderId|1-20': 1,
    'senderType|1': ['child', 'volunteer', 'ai'],
    'receiverId|1-20': 1,
    'content': '@sentence(5, 20)',
    'type|1': ['text', 'image', 'voice', 'video'],
    'timestamp': '@datetime',
    'status|1': ['sent', 'delivered', 'read']
  }]
})

// 课程数据
export const coursesData = Mock.mock({
  'list|30': [{
    'id|+1': 1,
    'title': '@ctitle(5, 15)',
    'description': '@cparagraph(2, 4)',
    'category|1': ['语文', '数学', '英语', '科学', '艺术', '体育'],
    'level|1-5': 1,
    'duration|30-120': 1, // 分钟
    'thumbnail': '@image("300x200", "@color", "@ctitle")',
    'instructor': '@cname',
    'rating|3-5': 1,
    'studentsCount|10-500': 1,
    'progress|0-100': 1,
    'isCompleted|1': [true, false]
  }]
})

// 每日挑战数据
export const dailyChallenges = Mock.mock({
  'list|10': [{
    'id|+1': 1,
    'title': '@ctitle(8, 15)',
    'description': '@cparagraph(1, 2)',
    'type|1': ['填空题', '选择题', '对话题', '创作题'],
    'difficulty|1-3': 1,
    'points|10-50': 1,
    'timeLimit|5-30': 1, // 分钟
    'isCompleted|1': [true, false],
    'completedAt': '@datetime'
  }]
})

// 成就数据
export const achievementsData = [
  { id: 1, name: '初学者', description: '完成第一次学习', icon: '🌟', unlocked: true },
  { id: 2, name: '勤奋学习者', description: '连续学习7天', icon: '📚', unlocked: true },
  { id: 3, name: '社交达人', description: '与10位朋友聊天', icon: '👥', unlocked: false },
  { id: 4, name: '诗词小能手', description: '完成10首诗词学习', icon: '📝', unlocked: true },
  { id: 5, name: '探索者', description: '尝试所有课程类别', icon: '🔍', unlocked: false }
]

// 统计数据
export const statisticsData = {
  totalUsers: 1250,
  activeUsers: 890,
  totalCourses: 156,
  completedSessions: 5420,
  averageStudyTime: 45, // 分钟
  userGrowth: 15.6, // 百分比
  engagementRate: 78.5,
  satisfactionScore: 4.6
}

// 实时监控数据
export const realtimeData = Mock.mock({
  onlineUsers: '@integer(50, 200)',
  activeSessions: '@integer(20, 100)',
  messagesPerMinute: '@integer(10, 50)',
  systemLoad: '@float(0, 100, 1, 2)',
  responseTime: '@integer(50, 500)', // 毫秒
  errorRate: '@float(0, 5, 1, 2)' // 百分比
})

// 心理健康指标
export const mentalHealthData = Mock.mock({
  'moodDistribution': {
    'happy': '@integer(30, 50)',
    'calm': '@integer(20, 40)',
    'excited': '@integer(10, 30)',
    'sad': '@integer(5, 15)',
    'confused': '@integer(5, 20)'
  },
  'riskAlerts|0-5': [{
    'userId|+1': 1,
    'userName': '@cname',
    'riskLevel|1': ['low', 'medium', 'high'],
    'indicators|1-3': ['情绪低落', '学习兴趣下降', '社交减少'],
    'timestamp': '@datetime'
  }]
})
